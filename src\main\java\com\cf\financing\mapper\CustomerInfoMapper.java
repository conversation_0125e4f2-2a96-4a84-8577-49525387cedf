package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.CustomerInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 客户信息Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface CustomerInfoMapper extends BaseMapper<CustomerInfo> {

    /**
 * 分页查询客户信息
 *
 * @param page 分页参数
 * @param customerNo 客户编号
 * @param customerName 客户姓名
 * @param idCard 身份证号
 * @param phone 手机号
 * @param email 邮箱
 * @param creditLevel 信用等级
 * @param riskLevel 风险等级
 * @return 客户分页数据
 */
    IPage<CustomerInfo> selectCustomerPage(
            Page<CustomerInfo> page,
            @Param("customerNo") String customerNo,
            @Param("customerName") String customerName,
            @Param("idCard") String idCard,
            @Param("phone") String phone,
            @Param("email") String email,
            @Param("creditLevel") String creditLevel,
            @Param("riskLevel") String riskLevel
    );

    /**
 * 根据客户ID查询客户详细信息（包含案件统计）
 *
 * @param customerId 客户ID
 * @return 客户详细信息
 */
    CustomerInfo selectCustomerById(@Param("customerId") Long customerId);

    /**
 * 根据身份证号查询客户信息
 *
 * @param idCard 身份证号
 * @return 客户信息
 */
    CustomerInfo selectCustomerByIdCard(@Param("idCard") String idCard);

    /**
 * 根据手机号查询客户信息
 *
 * @param phone 手机号
 * @return 客户信息
 */
    CustomerInfo selectCustomerByPhone(@Param("phone") String phone);

    /**
 * 根据客户编号查询客户信息
 *
 * @param customerNo 客户编号
 * @return 客户信息
 */
    CustomerInfo selectCustomerByNo(@Param("customerNo") String customerNo);

    /**
 * 获取客户统计信息
 *
 * @return 统计信息
 */
    Map<String, Object> selectCustomerStatistics();

    /**
 * 获取客户风险分布统计
 *
 * @return 风险分布统计
 */
    List<Map<String, Object>> selectRiskLevelDistribution();

    /**
 * 获取客户信用等级分布统计
 *
 * @return 信用等级分布统计
 */
    List<Map<String, Object>> selectCreditLevelDistribution();

    /**
 * 获取高风险客户列表
 *
 * @param limit 限制数量
 * @return 高风险客户列表
 */
    List<CustomerInfo> selectHighRiskCustomers(@Param("limit") Integer limit);

    /**
 * 获取逾期金额最高的客户列表
 *
 * @param limit 限制数量
 * @return 客户列表
 */
    List<CustomerInfo> selectTopOverdueCustomers(@Param("limit") Integer limit);

    /**
 * 批量更新客户风险等级
 *
 * @param customerIds 客户ID列表
 * @param riskLevel 风险等级
 * @return 更新结果
 */
    int batchUpdateRiskLevel(@Param("customerIds") List<Long> customerIds, @Param("riskLevel") String riskLevel);

    /**
 * 批量更新客户信用等级
 *
 * @param customerIds 客户ID列表
 * @param creditLevel 信用等级
 * @return 更新结果
 */
    int batchUpdateCreditLevel(@Param("customerIds") List<Long> customerIds, @Param("creditLevel") String creditLevel);

    /**
 * 检查身份证号是否存在
 *
 * @param idCard 身份证号
 * @param excludeCustomerId 排除的客户ID
 * @return 存在数量
 */
    int checkIdCardExists(@Param("idCard") String idCard, @Param("excludeCustomerId") Long excludeCustomerId);

    /**
 * 检查客户编号是否存在
 *
 * @param customerNo 客户编号
 * @param excludeCustomerId 排除的客户ID
 * @return 存在数量
 */
    int checkCustomerNoExists(@Param("customerNo") String customerNo, @Param("excludeCustomerId") Long excludeCustomerId);

    /**
 * 获取客户月度新增趋势
 *
 * @param months 月份数
 * @return 趋势数据
 */
    List<Map<String, Object>> selectMonthlyNewCustomerTrend(@Param("months") Integer months);

    /**
 * 搜索客户（模糊匹配姓名、手机号、身份证号）
 *
 * @param keyword 关键词
 * @param limit 限制数量
 * @return 客户列表
 */
    List<CustomerInfo> searchCustomers(@Param("keyword") String keyword, @Param("limit") Integer limit);
}