package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户信息实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("customer_info")
public class CustomerInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户编号
     */
    @TableField("customer_no")
    private String customerNo;

    /**
     * 客户姓名
     */
    @TableField("name")
    private String name;

    /**
     * 身份证号
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 备用手机号
     */
    @TableField("phone2")
    private String phone2;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 工作单位
     */
    @TableField("company")
    private String company;

    /**
     * 月收入
     */
    @TableField("income")
    private BigDecimal income;

    /**
     * 信用等级
     */
    @TableField("credit_level")
    private String creditLevel;

    /**
     * 风险等级
     */
    @TableField("risk_level")
    private String riskLevel;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记(0:正常,1:删除)
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 非数据库字段
    /**
     * 案件数量
     */
    @TableField(exist = false)
    private Integer caseCount;

    /**
     * 总欠款金额
     */
    @TableField(exist = false)
    private BigDecimal totalOverdueAmount;

    /**
     * 最大逾期天数
     */
    @TableField(exist = false)
    private Integer maxOverdueDays;
}