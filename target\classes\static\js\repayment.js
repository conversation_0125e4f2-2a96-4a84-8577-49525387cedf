// 还款管理页面JavaScript功能

// 全局变量
let currentPage = 1;
let pageSize = 20;
let totalRecords = 0;
let totalPages = 1;
let currentFilters = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadRepaymentData();
    loadStatistics();
    setupEventListeners();
});

// 初始化页面
function initializePage() {
    // 设置今天的日期作为默认值
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('repaymentDate').value = today;
}

// 设置事件监听器
function setupEventListeners() {
    // 全选复选框
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('#repaymentTableBody input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // 回车键搜索
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchRepayments();
        }
    });
}

// 加载还款数据
function loadRepaymentData() {
    showLoading();
    
    const params = new URLSearchParams({
        page: currentPage,
        size: pageSize,
        ...currentFilters
    });
    
    fetch(`/api/repayment-record/page?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                renderRepaymentTable(data.data.records);
                updatePagination(data.data);
            } else {
                showError('加载数据失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('加载数据失败:', error);
            showError('网络错误，请稍后重试');
            // 使用模拟数据
            renderMockData();
        })
        .finally(() => {
            hideLoading();
        });
}

// 渲染还款表格
function renderRepaymentTable(repayments) {
    const tbody = document.getElementById('repaymentTableBody');
    tbody.innerHTML = '';
    
    if (!repayments || repayments.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="11" style="text-align: center; padding: 40px; color: #999;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                    暂无数据
                </td>
            </tr>
        `;
        return;
    }
    
    repayments.forEach(repayment => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="checkbox" value="${repayment.id}"></td>
            <td>${repayment.repaymentNo || '-'}</td>
            <td>${repayment.caseNo || '-'}</td>
            <td>${repayment.customerName || '-'}</td>
            <td class="amount">¥${formatAmount(repayment.amount)}</td>
            <td>${getPaymentMethodText(repayment.paymentMethod)}</td>
            <td>${formatDateTime(repayment.repaymentTime)}</td>
            <td>${repayment.transactionNo || '-'}</td>
            <td><span class="status-badge ${getStatusClass(repayment.status)}">${getStatusText(repayment.status)}</span></td>
            <td>${repayment.remark || '-'}</td>
            <td>
                <button class="action-btn view" onclick="viewRepayment(${repayment.id})" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit" onclick="editRepayment(${repayment.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                ${repayment.status === 'pending' ? `
                    <button class="action-btn edit" onclick="confirmRepayment(${repayment.id})" title="确认">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="action-btn delete" onclick="rejectRepayment(${repayment.id})" title="拒绝">
                        <i class="fas fa-times"></i>
                    </button>
                ` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 渲染模拟数据
function renderMockData() {
    const mockData = [
        {
            id: 1,
            repaymentNo: 'RP202312150001',
            caseNo: 'CS202312150001',
            customerName: '张三',
            amount: 50000,
            paymentMethod: 'bank_transfer',
            repaymentTime: '2023-12-15 10:30:00',
            transactionNo: 'TXN20231215103001',
            status: 'success',
            remark: '正常还款'
        },
        {
            id: 2,
            repaymentNo: 'RP202312150002',
            caseNo: 'CS202312150002',
            customerName: '李四',
            amount: 30000,
            paymentMethod: 'alipay',
            repaymentTime: '2023-12-15 14:20:00',
            transactionNo: 'TXN20231215142001',
            status: 'pending',
            remark: '待确认'
        },
        {
            id: 3,
            repaymentNo: 'RP202312150003',
            caseNo: 'CS202312150003',
            customerName: '王五',
            amount: 75000,
            paymentMethod: 'wechat_pay',
            repaymentTime: '2023-12-15 16:45:00',
            transactionNo: 'TXN20231215164501',
            status: 'success',
            remark: '部分还款'
        }
    ];
    
    renderRepaymentTable(mockData);
    updatePagination({
        current: 1,
        pages: 1,
        total: mockData.length
    });
}

// 加载统计数据
function loadStatistics() {
    fetch('/api/repayment-record/statistics-simple')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                updateStatistics(data.data);
            }
        })
        .catch(error => {
            console.error('加载统计数据失败:', error);
            // 使用模拟数据
            updateStatistics({
                todayAmount: 356800,
                todayCount: 28,
                monthAmount: 8740000,
                pendingCount: 15
            });
        });
}

// 更新统计数据
function updateStatistics(stats) {
    if (stats.todayAmount !== undefined) {
        document.getElementById('todayAmount').textContent = '¥' + formatAmount(stats.todayAmount);
    }
    if (stats.todayCount !== undefined) {
        document.getElementById('todayCount').textContent = stats.todayCount;
    }
    if (stats.monthAmount !== undefined) {
        document.getElementById('monthAmount').textContent = '¥' + formatAmount(stats.monthAmount);
    }
    if (stats.pendingCount !== undefined) {
        document.getElementById('pendingCount').textContent = stats.pendingCount;
    }
}

// 搜索还款记录
function searchRepayments() {
    currentFilters = {
        repaymentNo: document.getElementById('repaymentNo').value.trim(),
        caseNo: document.getElementById('caseNo').value.trim(),
        customerName: document.getElementById('customerName').value.trim(),
        status: document.getElementById('status').value,
        repaymentDate: document.getElementById('repaymentDate').value,
        amountRange: document.getElementById('amountRange').value.trim()
    };
    
    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });
    
    currentPage = 1;
    loadRepaymentData();
}

// 重置筛选条件
function resetFilters() {
    document.getElementById('repaymentNo').value = '';
    document.getElementById('caseNo').value = '';
    document.getElementById('customerName').value = '';
    document.getElementById('status').value = '';
    document.getElementById('repaymentDate').value = '';
    document.getElementById('amountRange').value = '';
    
    currentFilters = {};
    currentPage = 1;
    loadRepaymentData();
}

// 更新分页信息
function updatePagination(pageData) {
    totalRecords = pageData.total;
    totalPages = pageData.pages;
    currentPage = pageData.current;
    
    // 更新分页显示
    const startRecord = (currentPage - 1) * pageSize + 1;
    const endRecord = Math.min(currentPage * pageSize, totalRecords);
    document.getElementById('startRecord').textContent = startRecord;
    document.getElementById('endRecord').textContent = endRecord;
    document.getElementById('totalRecords').textContent = totalRecords;
    
    // 更新按钮状态
    document.getElementById('prevPage').disabled = currentPage <= 1;
    document.getElementById('nextPage').disabled = currentPage >= totalPages;
    
    // 更新页码
    renderPageNumbers();
}

// 渲染页码
function renderPageNumbers() {
    const container = document.getElementById('pageNumbers');
    container.innerHTML = '';
    
    const maxVisible = 5;
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(totalPages, start + maxVisible - 1);
    
    if (end - start + 1 < maxVisible) {
        start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
        const btn = document.createElement('button');
        btn.className = `pagination-btn ${i === currentPage ? 'active' : ''}`;
        btn.textContent = i;
        btn.onclick = () => goToPage(i);
        container.appendChild(btn);
    }
}

// 分页相关函数
function prevPage() {
    if (currentPage > 1) {
        currentPage--;
        loadRepaymentData();
    }
}

function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        loadRepaymentData();
    }
}

function goToPage(page) {
    currentPage = page;
    loadRepaymentData();
}

// 还款操作
function viewRepayment(id) {
    window.open(`/repayment/detail/${id}`, '_blank');
}

function editRepayment(id) {
    window.open(`/repayment/edit/${id}`, '_blank');
}

function confirmRepayment(id) {
    if (confirm('确认要确认这笔还款吗？')) {
        fetch(`/api/repayment-record/${id}/confirm`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('确认成功');
                loadRepaymentData();
                loadStatistics();
            } else {
                alert('确认失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('确认失败:', error);
            alert('网络错误，请稍后重试');
        });
    }
}

function rejectRepayment(id) {
    const reason = prompt('请输入拒绝原因：');
    if (reason) {
        fetch(`/api/repayment-record/${id}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('拒绝成功');
                loadRepaymentData();
                loadStatistics();
            } else {
                alert('拒绝失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('拒绝失败:', error);
            alert('网络错误，请稍后重试');
        });
    }
}

// 批量操作
function batchConfirm() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        alert('请选择要确认的还款记录');
        return;
    }
    
    if (confirm(`确认要批量确认 ${selectedIds.length} 笔还款吗？`)) {
        fetch('/api/repayment-record/batch-confirm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ids: selectedIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('批量确认成功');
                loadRepaymentData();
                loadStatistics();
            } else {
                alert('批量确认失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('批量确认失败:', error);
            alert('网络错误，请稍后重试');
        });
    }
}

function batchReject() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        alert('请选择要拒绝的还款记录');
        return;
    }
    
    const reason = prompt('请输入拒绝原因：');
    if (reason) {
        fetch('/api/repayment-record/batch-reject', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ids: selectedIds, reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert('批量拒绝成功');
                loadRepaymentData();
                loadStatistics();
            } else {
                alert('批量拒绝失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('批量拒绝失败:', error);
            alert('网络错误，请稍后重试');
        });
    }
}

// 功能按钮
function exportData() {
    const params = new URLSearchParams(currentFilters);
    window.open(`/api/repayment-record/export?${params}`, '_blank');
}

function refreshData() {
    loadRepaymentData();
    loadStatistics();
}

// 工具函数
function getSelectedIds() {
    const checkboxes = document.querySelectorAll('#repaymentTableBody input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function formatAmount(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

function getPaymentMethodText(method) {
    const methodMap = {
        'bank_transfer': '银行转账',
        'alipay': '支付宝',
        'wechat_pay': '微信支付',
        'cash': '现金',
        'other': '其他'
    };
    return methodMap[method] || '未知';
}

function getStatusClass(status) {
    const statusMap = {
        'success': 'status-success',
        'pending': 'status-pending',
        'failed': 'status-failed'
    };
    return statusMap[status] || 'status-pending';
}

function getStatusText(status) {
    const statusMap = {
        'success': '已确认',
        'pending': '待确认',
        'failed': '已拒绝'
    };
    return statusMap[status] || '未知';
}

function showLoading() {
    const tbody = document.getElementById('repaymentTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="11" style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-right: 8px;"></i>
                加载中...
            </td>
        </tr>
    `;
}

function hideLoading() {
    // 加载完成后会被数据替换，无需特殊处理
}

function showError(message) {
    alert('错误: ' + message);
}
