<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4CAF50',
                        tableHeader: '#E8F5E9',
                        tableRow1: '#FFFFFF',
                        tableRow2: '#F1F8E9',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .border-primary {
                border-color: #4CAF50;
            }
            .shadow-modal {
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            }
            .transition-modal {
                transition: opacity 0.3s ease, transform 0.3s ease;
            }
            .truncate-10-chars {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 10ch;
            }
            .tooltip-container {
                position: relative;
            }
            .tooltip-content {
                visibility: hidden;
                opacity: 0;
                position: absolute;
                bottom: 100%;
                left: 50%;
                transform: translateX(-50%);
                background-color: rgba(0, 0, 0, 0.75);
                color: white;
                text-align: center;
                padding: 5px 10px;
                border-radius: 4px;
                z-index: 1;
                transition: opacity 0.2s, visibility 0.2s;
                white-space: nowrap;
                pointer-events: none;
                width: max-content;
                max-width: 300px;
            }
            .tooltip-container:hover .tooltip-content {
                visibility: visible;
                opacity: 1;
            }
        }
    </style>
</head>
<body class="bg-gray-200 min-h-screen flex items-center justify-center p-4">
    <!-- 弹框容器 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-300">
        <!-- 弹框主体 -->
        <div class="bg-white rounded-lg shadow-modal border-4 border-primary w-full max-w-6xl flex flex-col overflow-hidden transition-modal transform scale-100">
            <!-- 弹框头部 -->
            <div class="bg-primary text-white px-6 py-4 flex justify-between items-center">
                <h3 class="text-xl font-bold flex items-center">
                    <i class="fa fa-table mr-2"></i>历史记录
                </h3>
                <button id="closeModal" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fa fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- 弹框内容区 -->
            <div class="flex-1 overflow-auto p-4">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <table id="assignmentTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-tableHeader">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[5%]">序号</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">跟进时间</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">被叫号码</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">主叫号码</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">还款金额</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">还款时间</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">跟进记录</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">失联查询结果</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">作业员</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">组织</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">退案时间</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 表格内容将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 弹框底部 -->
            <div class="px-6 py-4 bg-gray-50 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    共 <span id="totalCount" class="font-medium">30</span> 条记录
                </div>
                <div class="flex space-x-1">
                    <button id="prevPage" class="px-3 py-1 rounded border border-gray-300 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fa fa-angle-left"></i>
                    </button>
                    <div id="pageNumbers" class="flex">
                        <!-- 页码将通过JavaScript动态生成 -->
                    </div>
                    <button id="nextPage" class="px-3 py-1 rounded border border-gray-300 text-gray-600 hover:bg-gray-50">
                        <i class="fa fa-angle-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟跟进记录数据
        const followUpData = [
            { id: 30, followTime: '2025-05-31 09:30:22', calledNum: '138****5678', callingNum: '159****1234', repayAmount: '¥2,500.00', repayTime: '2025-05-30 14:20', followNote: '客户承诺下周一前还款，已发送还款提醒短信', 失联结果: '正常联系', operator: '张三', org: '催收一部', returnTime: '无' },
            { id: 29, followTime: '2025-05-31 09:15:45', calledNum: '139****6789', callingNum: '158****5678', repayAmount: '¥1,800.00', repayTime: '2025-05-29 10:15', followNote: '客户表示资金周转困难，申请延期3天还款', 失联结果: '正常联系', operator: '李四', org: '催收二部', returnTime: '无' },
            { id: 28, followTime: '2025-05-31 09:00:12', calledNum: '137****7890', callingNum: '157****9012', repayAmount: '¥3,200.00', repayTime: '2025-05-28 16:30', followNote: '已成功收款，系统已更新还款状态', 失联结果: '正常联系', operator: '王五', org: '催收一部', returnTime: '无' },
            { id: 27, followTime: '2025-05-30 16:45:33', calledNum: '136****8901', callingNum: '156****3456', repayAmount: '¥0.00', repayTime: '无', followNote: '客户未接电话，已发送语音留言', 失联结果: '未接电话', operator: '赵六', org: '催收三部', returnTime: '无' },
            { id: 26, followTime: '2025-05-30 16:30:15', calledNum: '135****9012', callingNum: '155****7890', repayAmount: '¥4,500.00', repayTime: '2025-05-27 09:45', followNote: '客户反馈已还款，但系统未到账，已提交财务核查', 失联结果: '正常联系', operator: '孙七', org: '催收二部', returnTime: '无' },
            { id: 25, followTime: '2025-05-30 16:15:48', calledNum: '134****0123', callingNum: '154****1234', repayAmount: '¥0.00', repayTime: '无', followNote: '客户电话停机，已标记失联', 失联结果: '电话停机', operator: '周八', org: '催收一部', returnTime: '无' },
            { id: 24, followTime: '2025-05-30 16:00:22', calledNum: '133****1234', callingNum: '153****5678', repayAmount: '¥1,200.00', repayTime: '2025-05-26 14:10', followNote: '客户确认还款计划，每月25日前还款1200元', 失联结果: '正常联系', operator: '吴九', org: '催收三部', returnTime: '无' },
            { id: 23, followTime: '2025-05-29 14:30:55', calledNum: '132****2345', callingNum: '152****9012', repayAmount: '¥0.00', repayTime: '无', followNote: '客户表示在开会，稍后回电', 失联结果: '承诺回电', operator: '郑十', org: '催收二部', returnTime: '无' },
            { id: 22, followTime: '2025-05-29 14:15:33', calledNum: '131****3456', callingNum: '151****3456', repayAmount: '¥2,800.00', repayTime: '2025-05-25 10:20', followNote: '已确认收款，提醒客户保留还款凭证', 失联结果: '正常联系', operator: '钱十一', org: '催收一部', returnTime: '无' },
            { id: 21, followTime: '2025-05-29 14:00:17', calledNum: '130****4567', callingNum: '150****7890', repayAmount: '¥0.00', repayTime: '无', followNote: '客户拒接电话，标记为拒绝沟通', 失联结果: '拒接电话', operator: '孙十二', org: '催收三部', returnTime: '无' },
            { id: 20, followTime: '2025-05-28 16:45:22', calledNum: '189****5678', callingNum: '188****1234', repayAmount: '¥3,600.00', repayTime: '2025-05-24 16:10', followNote: '客户咨询还款优惠政策，已详细解答', 失联结果: '正常联系', operator: '周十三', org: '催收二部', returnTime: '无' },
            { id: 19, followTime: '2025-05-28 16:30:45', calledNum: '188****6789', callingNum: '187****5678', repayAmount: '¥0.00', repayTime: '无', followNote: '客户家人接听，称客户出差，下周回来联系', 失联结果: '家人代接', operator: '吴十四', org: '催收一部', returnTime: '无' },
            { id: 18, followTime: '2025-05-28 16:15:33', calledNum: '187****7890', callingNum: '186****9012', repayAmount: '¥1,500.00', repayTime: '2025-05-23 09:30', followNote: '客户部分还款，剩余1500元承诺月底前还清', 失联结果: '正常联系', operator: '郑十五', org: '催收三部', returnTime: '无' },
            { id: 17, followTime: '2025-05-27 14:30:12', calledNum: '186****8901', callingNum: '185****3456', repayAmount: '¥0.00', repayTime: '无', followNote: '客户电话无法接通，已尝试其他联系方式', 失联结果: '无法接通', operator: '王十六', org: '催收二部', returnTime: '无' },
            { id: 16, followTime: '2025-05-27 14:15:55', calledNum: '185****9012', callingNum: '184****7890', repayAmount: '¥2,200.00', repayTime: '2025-05-22 14:45', followNote: '确认收款，提醒客户查收还款确认函', 失联结果: '正常联系', operator: '李十七', org: '催收一部', returnTime: '无' },
        ]

        // 分页配置
        const recordsPerPage = 10;
        let currentPage = 1;
        const totalPages = Math.ceil(followUpData.length / recordsPerPage);

        // 关闭弹框功能
        document.getElementById('closeModal').addEventListener('click', closeModal);

        // 生成分页控件
        function generatePagination() {
            const pageNumbers = document.getElementById('pageNumbers');
            pageNumbers.innerHTML = '';
            
            // 显示当前页面前后各2页
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            // 调整起始页，确保显示5个页码
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }
            
            // 第一页按钮
            if (startPage > 1) {
                addPageButton(1);
                if (startPage > 2) {
                    addEllipsis();
                }
            }
            
            // 页码按钮
            for (let i = startPage; i <= endPage; i++) {
                addPageButton(i);
            }
            
            // 最后一页按钮
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    addEllipsis();
                }
                addPageButton(totalPages);
            }
            
            // 更新上一页/下一页按钮状态
            document.getElementById('prevPage').disabled = currentPage === 1;
            document.getElementById('nextPage').disabled = currentPage === totalPages;
        }
        
        // 添加页码按钮
        function addPageButton(pageNum) {
            const button = document.createElement('button');
            button.className = `px-3 py-1 rounded border ${pageNum === currentPage ? 'border-primary bg-primary text-white' : 'border-gray-300 text-gray-600 hover:bg-gray-50'}`;
            button.textContent = pageNum;
            button.addEventListener('click', () => {
                currentPage = pageNum;
                renderTable();
                generatePagination();
            });
            document.getElementById('pageNumbers').appendChild(button);
        }
        
        // 添加省略号
        function addEllipsis() {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'px-2 py-1';
            ellipsis.textContent = '...';
            document.getElementById('pageNumbers').appendChild(ellipsis);
        }
        
        // 格式化日期
        function formatDate(dateString) {
            if (!dateString || dateString === '无') return '无';
            const date = new Date(dateString);
            return `${date.getFullYear()}.${date.getMonth() + 1}.${date.getDate()}`;
        }
        
        // 渲染表格内容
        function renderTable() {
            const tableBody = document.getElementById('tableBody');
            tableBody.innerHTML = '';
            
            // 计算当前页的数据范围
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = Math.min(startIndex + recordsPerPage, followUpData.length);
            const currentData = followUpData.slice(startIndex, endIndex);
            
            // 生成表格行
            currentData.forEach((item, index) => {
                const row = document.createElement('tr');
                row.className = `bg-${index % 2 === 0 ? 'tableRow1' : 'tableRow2'} hover:bg-gray-50 transition-colors`;
                
                row.innerHTML = `
                    <td class="px-4 py-3 whitespace-nowrap text-xs font-medium text-gray-900">${item.id}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${formatDate(item.followTime)}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${item.calledNum}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${item.callingNum}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${item.repayAmount}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${formatDate(item.repayTime)}</td>
                    <td class="px-4 py-3 text-xs text-gray-500">
                        <div class="tooltip-container">
                            <span class="truncate-10-chars">${item.followNote}</span>
                            <span class="tooltip-content">${item.followNote}</span>
                        </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${item.失联结果}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${item.operator}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${item.org}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">${formatDate(item.returnTime)}</td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // 关闭弹框
        function closeModal() {
            const modal = document.querySelector('.fixed');
            modal.classList.add('opacity-0');
            modal.querySelector('.max-w-6xl').classList.add('scale-95');
            
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }
        
        // 上一页/下一页事件监听
        document.getElementById('prevPage').addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                renderTable();
                generatePagination();
            }
        });
        
        document.getElementById('nextPage').addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                renderTable();
                generatePagination();
            }
        });
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            renderTable();
            generatePagination();
        });
    </script>
</body>
</html>
    