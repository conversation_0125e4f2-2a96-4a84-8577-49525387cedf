<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" style="height: 100%;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案池页面 - CF金融催收管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #2c3e50);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
            height: 100%;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 40px);
            background: #fff;
        }
        
        /* 上框体：筛选框 - 占视口高度35% */
        .filter-box {
            height: 35vh;
            background: #fff;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            border-bottom: 2px solid #f0f0f0;
            overflow-y: auto;
        }
        
        .filter-title {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #2c3e50;
            font-size: 1.1rem;
            font-weight: 600;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
        }
        
        .filter-title i {
            color: #3498db;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(5, minmax(0, 1fr));
            gap: 15px;
            flex: 1;
        }
        
        /* 筛选组样式 */
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 2px;
        }
        
        /* 字段标签样式 */
        .filter-label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.75rem;
            min-width: 70px;
        }
        
        /* 输入控件样式 */
        .filter-control {
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.7rem;
            flex: 1;
        }
        
        .filter-control:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .filter-btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .filter-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 0.8rem;
        }
        
        .filter-btn.search {
            background-color: #3498db;
            color: white;
        }
        
        .filter-btn.reset {
            background-color: #e1e4e8;
            color: #2c3e50;
        }
        
        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        /* 中框体：功能框 - 占视口高度5% */
        .function-box {
            height: 5vh;
            background: linear-gradient(145deg, #2c3e50, #1a2a6c);
            display: flex;
            align-items: center;
            padding: 0 15px;
            position: relative;
        }
        
        .stats-info {
            position: absolute;
            left: 2px;
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
            font-size: 0.8rem;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .function-buttons {
            position: absolute;
            right: 2px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .function-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8rem;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .function-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        
        .function-btn.primary {
            background-color: #3498db;
        }
        
        /* 下框体：数据展示框 - 占视口高度60% */
        .data-box {
            height: 60vh;
            background: #fff;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .data-header {
            padding: 10px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e4e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .data-title {
            font-size: 1.0rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .data-title i {
            color: #3498db;
        }
        
        .data-actions {
            display: flex;
            gap: 8px;
        }
        
        .data-action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            color: #2c3e50;
            background: #e1e4e8;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .data-action-btn:hover {
            background: #d1d5db;
        }
        
        /* 改进的表格容器 */
        .data-container {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            background-color: #f9f9f9;
        }
        
        .table-wrapper {
            flex: 1;
            overflow: auto;
            position: relative;
        }
        
        table {
            border-collapse: collapse;
            min-width: 100%;
            width: max-content;
        }
        
        thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background: #f1f3f5;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 8px 10px;
            text-align: center;
            font-size: 0.7rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #2c3e50;
            font-weight: 600;
            min-width: 100px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        thead th:hover {
            background-color: #e1e4e8;
        }
        
        th:nth-child(1),
        td:nth-child(1) {
            position: sticky;
            left: 0;
            z-index: 5;
            width: 30px;
            min-width: 30px;
            background: #f8f8f8;
        }
        
        th:nth-child(2),
        td:nth-child(2) {
            position: sticky;
            left: 30px;
            z-index: 5;
            width: 100px;
            min-width: 100px;
            background: #f8f8f8;
        }
        
        th:nth-child(3),
        td:nth-child(3) {
            position: sticky;
            left: 130px;
            z-index: 5;
            width: 180px;
            min-width: 180px;
            background: #f8f8f8;
        }
        
        th:nth-child(1),
        th:nth-child(2),
        th:nth-child(3) {
            z-index: 15;
        }
        
        td {
            padding: 8px 10px;
            border-bottom: 1px solid #e1e4e8;
            background: white;
            white-space: nowrap;
            position: relative;
            font-size: 0.7rem;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #333;
        }
        
        .data-table tr:hover td {
            background: #f0f7ff;
        }
        
        .data-table tr:nth-child(even) td {
            background: #f8fafc;
        }
        
        .status-tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-align: center;
            min-width: 70px;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-processing {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-completed {
            background: #bbf7d0;
            color: #047857;
        }
        
        .status-overdue {
            background: #fee2e2;
            color: #b91c1c;
        }
        
        .amount {
            font-weight: 500;
            color: #2c3e50;
            text-align: center;
        }
        
        /* 分页控件样式 */
        .pagination {
            padding: 10px 15px;
            background: #f8f9fa;
            border-top: 1px solid #e1e4e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
        }
        
        .pagination-left, .pagination-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .pagination-select, .pagination-input {
            padding: 5px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.8rem;
            margin: 0 4px;
        }
        
        .pagination-input {
            width: 40px;
            text-align: center;
        }
        
        .pagination-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            background: #e1e4e8;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.8rem;
        }
        
        .pagination-btn:hover:not(:disabled) {
            background: #d1d5db;
        }
        
        .pagination-btn:disabled {
            background: #e1e4e8;
            color: #adb5bd;
            cursor: not-allowed;
        }
        
        .pagination-btn.active {
            background: #3498db;
            color: white;
        }
        
        .page-info {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 上框体：筛选框 -->
        <div class="filter-box">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                <span>筛选条件</span>
            </div>
            
            <div class="filter-grid">
                <!-- 第一行筛选条件 -->
                <div class="filter-group">
                    <label class="filter-label">案件编号:</label>
                    <input type="text" class="filter-control" id="caseNo" placeholder="请输入案件编号">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">客户姓名:</label>
                    <input type="text" class="filter-control" id="customerName" placeholder="请输入客户姓名">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">身份证号:</label>
                    <input type="text" class="filter-control" id="idCard" placeholder="请输入身份证号">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">手机号:</label>
                    <input type="text" class="filter-control" id="phone" placeholder="请输入手机号">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">案件状态:</label>
                    <select class="filter-control" id="caseStatus">
                        <option value="">全部状态</option>
                        <option value="pending">待处理</option>
                        <option value="processing">处理中</option>
                        <option value="completed">已完成</option>
                        <option value="overdue">逾期</option>
                    </select>
                </div>
                
                <!-- 第二行筛选条件 -->
                <div class="filter-group">
                    <label class="filter-label">委托方:</label>
                    <select class="filter-control" id="delegator">
                        <option value="">全部委托方</option>
                        <option value="bank1">银行A</option>
                        <option value="bank2">银行B</option>
                        <option value="finance1">金融公司C</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">分配人:</label>
                    <select class="filter-control" id="assignee">
                        <option value="">全部分配人</option>
                        <option value="user1">张三</option>
                        <option value="user2">李四</option>
                        <option value="user3">王五</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">委托时间:</label>
                    <input type="date" class="filter-control" id="delegateDate">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">到期时间:</label>
                    <input type="date" class="filter-control" id="dueDate">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">金额范围:</label>
                    <input type="number" class="filter-control" id="amountMin" placeholder="最小金额" style="width: 48%; margin-right: 4%;">
                    <input type="number" class="filter-control" id="amountMax" placeholder="最大金额" style="width: 48%;">
                </div>
            </div>
            
            <div class="filter-btn-group">
                <button class="filter-btn search" onclick="searchCases()">
                    <i class="fas fa-search"></i>
                    <span>查询</span>
                </button>
                <button class="filter-btn reset" onclick="resetFilters()">
                    <i class="fas fa-undo"></i>
                    <span>重置</span>
                </button>
            </div>
        </div>
        
        <!-- 中框体：功能框 -->
        <div class="function-box">
            <div class="stats-info">
                <div class="stat-item">
                    <i class="fas fa-list"></i>
                    <span>总案件数: <strong id="totalCases">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>总金额: <strong id="totalAmount">¥0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>今日到期: <strong id="todayDue">0</strong></span>
                </div>
            </div>
            
            <div class="function-buttons">
                <button class="function-btn" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    <span>导出</span>
                </button>
                <button class="function-btn" onclick="batchAssign()">
                    <i class="fas fa-user-plus"></i>
                    <span>批量分配</span>
                </button>
                <button class="function-btn primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    <span>刷新</span>
                </button>
            </div>
        </div>
        
        <!-- 下框体：数据展示框 -->
        <div class="data-box">
            <div class="data-header">
                <div class="data-title">
                    <i class="fas fa-table"></i>
                    <span>案件列表</span>
                </div>
                <div class="data-actions">
                    <button class="data-action-btn" onclick="toggleColumns()">
                        <i class="fas fa-columns"></i>
                        <span>列设置</span>
                    </button>
                    <button class="data-action-btn" onclick="fullScreen()">
                        <i class="fas fa-expand"></i>
                        <span>全屏</span>
                    </button>
                </div>
            </div>
            
            <div class="data-container">
                <div class="table-wrapper">
                    <table class="data-table" id="caseTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll"></th>
                                <th>案件编号</th>
                                <th>客户姓名</th>
                                <th>身份证号</th>
                                <th>手机号</th>
                                <th>委托金额</th>
                                <th>逾期金额</th>
                                <th>案件状态</th>
                                <th>委托方</th>
                                <th>分配人</th>
                                <th>委托时间</th>
                                <th>到期时间</th>
                                <th>最后联系</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="caseTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination">
                    <div class="pagination-left">
                        <span>每页显示</span>
                        <select class="pagination-select" id="pageSize" onchange="changePageSize()">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>条记录</span>
                    </div>
                    
                    <div class="pagination-right">
                        <span class="page-info">
                            显示第 <span id="startRecord">1</span> 到 <span id="endRecord">20</span> 条，
                            共 <span id="totalRecords">0</span> 条记录
                        </span>
                        
                        <button class="pagination-btn" id="prevPage" onclick="prevPage()" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        
                        <span>第</span>
                        <input type="number" class="pagination-input" id="currentPageInput" value="1" min="1" onchange="goToPage()">
                        <span>页，共 <span id="totalPages">1</span> 页</span>
                        
                        <button class="pagination-btn" id="nextPage" onclick="nextPage()">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script th:src="@{/js/case-pool.js}"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
</body>
</html>
