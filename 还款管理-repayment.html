<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>还款管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(135deg, #1a6dcc, #0d4a9e);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo i {
            font-size: 28px;
        }
        
        .logo h1 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info .notifications {
            position: relative;
            cursor: pointer;
        }
        
        .user-info .notifications .badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            font-size: 10px;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-info .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4d9df8;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .filter-section {
            padding: 25px 30px;
            border-bottom: 1px solid #eaeff5;
        }
        
        .filter-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-title i {
            color: #1a6dcc;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }
        
        .filter-item {
            display: flex;
            flex-direction: column;
        }
        
        .filter-item label {
            font-size: 14px;
            color: #5a6d80;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .filter-item input, .filter-item select {
            padding: 12px 15px;
            border: 1px solid #dce1e8;
            border-radius: 8px;
            font-size: 14px;
            background: #fafbfc;
            transition: all 0.3s;
        }
        
        .filter-item input:focus, .filter-item select:focus {
            outline: none;
            border-color: #4d9df8;
            box-shadow: 0 0 0 3px rgba(77, 157, 248, 0.15);
        }
        
        .filter-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-query {
            background: linear-gradient(135deg, #1a6dcc, #0d4a9e);
            color: white;
        }
        
        .btn-reset {
            background: #f1f4f8;
            color: #5a6d80;
        }
        
        .btn-query:hover {
            background: linear-gradient(135deg, #0d5cb6, #0a3d87);
            transform: translateY(-2px);
        }
        
        .btn-reset:hover {
            background: #e4e9f0;
        }
        
        .stats-section {
            padding: 20px 30px;
            background: #f9fbfd;
            border-bottom: 1px solid #eaeff5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-overview {
            display: flex;
            gap: 30px;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
        }
        
        .stat-label {
            font-size: 14px;
            color: #5a6d80;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 22px;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .stats-selected {
            display: flex;
            align-items: center;
            gap: 30px;
            background: #e6f2ff;
            padding: 10px 20px;
            border-radius: 8px;
            border: 1px solid #cce0ff;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, #ff6b6b, #ff4757);
            color: white;
            padding: 10px 25px;
        }
        
        .btn-delete:hover {
            background: linear-gradient(135deg, #e55a5a, #e53e4e);
            transform: translateY(-2px);
        }
        
        .table-section {
            padding: 0 30px 30px;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 25px 0 15px;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .table-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn-export {
            background: #27ae60;
            color: white;
        }
        
        .btn-export:hover {
            background: #219653;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
        }
        
        thead {
            background: linear-gradient(135deg, #f8fafd, #eef2f7);
        }
        
        th {
            padding: 16px 15px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            border-bottom: 2px solid #eaeff5;
        }
        
        tbody tr {
            border-bottom: 1px solid #eaeff5;
            transition: background 0.2s;
        }
        
        tbody tr:hover {
            background: #f9fbfd;
        }
        
        td {
            padding: 14px 15px;
            font-size: 14px;
            color: #4a5a6a;
        }
        
        .checkbox-cell {
            width: 50px;
            text-align: center;
        }
        
        .action-cell a {
            color: #1a6dcc;
            text-decoration: none;
            margin-right: 15px;
            transition: color 0.2s;
        }
        
        .action-cell a:hover {
            color: #0d4a9e;
            text-decoration: underline;
        }
        
        .action-cell a:last-child {
            color: #ff6b6b;
        }
        
        .action-cell a:last-child:hover {
            color: #e53e4e;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-completed {
            background: #e3fcef;
            color: #27ae60;
        }
        
        .status-pending {
            background: #fff8e6;
            color: #f39c12;
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 25px;
            gap: 10px;
        }
        
        .pagination button {
            padding: 8px 15px;
            background: #f1f4f8;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .pagination button:hover {
            background: #e4e9f0;
        }
        
        .pagination button.active {
            background: #1a6dcc;
            color: white;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #7f8c9a;
            font-size: 14px;
            border-top: 1px solid #eaeff5;
            margin-top: 30px;
        }
        
        @media (max-width: 1200px) {
            .filter-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 900px) {
            .filter-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .stats-overview {
                flex-direction: column;
                gap: 15px;
            }
            
            .stats-selected {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        
        @media (max-width: 600px) {
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .stats-section {
                flex-direction: column;
                align-items: flex-start;
                gap: 20px;
            }
            
            .table-actions {
                flex-direction: column;
                width: 100%;
            }
            
            .table-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        
        <section class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                <span>筛选条件</span>
            </div>
            
            <div class="filter-grid">
                <div class="filter-item">
                    <label>委托机构</label>
                    <select>
                        <option>全部机构</option>
                        <option>中国工商银行</option>
                        <option>中国建设银行</option>
                        <option>中国银行</option>
                        <option>招商银行</option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label>批次号</label>
                    <input type="text" placeholder="请输入批次号">
                </div>
                
                <div class="filter-item">
                    <label>客户索引号</label>
                    <input type="text" placeholder="请输入客户索引号">
                </div>
                
                <div class="filter-item">
                    <label>客户姓名</label>
                    <input type="text" placeholder="请输入客户姓名">
                </div>
                
                <div class="filter-item">
                    <label>案件类型</label>
                    <select>
                        <option>全部类型</option>
                        <option>信用卡还款</option>
                        <option>贷款还款</option>
                        <option>分期还款</option>
                        <option>提前还款</option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label>持卡人代码</label>
                    <input type="text" placeholder="请输入持卡人代码">
                </div>
                
                <div class="filter-item">
                    <label>还款时间</label>
                    <input type="date">
                </div>
            </div>
            
            <div class="filter-buttons">
                <button class="btn btn-query"><i class="fas fa-search"></i> 查询</button>
                <button class="btn btn-reset"><i class="fas fa-redo"></i> 重置</button>
            </div>
        </section>
        
        <section class="stats-section">
            <div class="stats-overview">
                <div class="stat-item">
                    <div class="stat-label">还款总户数</div>
                    <div class="stat-value">1,248户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">还款总金额</div>
                    <div class="stat-value">¥3,876,500.00</div>
                </div>
            </div>
            
            <div class="stats-selected">
                <div class="stat-item">
                    <div class="stat-label">勾选户数</div>
                    <div class="stat-value">5户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">勾选金额</div>
                    <div class="stat-value">¥15,800.00</div>
                </div>
                <button class="btn btn-delete"><i class="fas fa-trash-alt"></i> 删除</button>
            </div>
        </section>
        
        <section class="table-section">
            <div class="table-header">
                <div class="table-title">还款记录列表</div>
                <div class="table-actions">
                    <button class="btn btn-export"><i class="fas fa-file-export"></i> 导出数据</button>
                </div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th class="checkbox-cell"><input type="checkbox"></th>
                        <th>客户姓名</th>
                        <th>委托机构</th>
                        <th>批次号</th>
                        <th>客户索引号</th>
                        <th>持卡人代码</th>
                        <th>案件类型</th>
                        <th>还款时间</th>
                        <th>组织</th>
                        <th>作业员</th>
                        <th>工号</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="checkbox-cell"><input type="checkbox"></td>
                        <td>张明</td>
                        <td>中国工商银行</td>
                        <td>BATCH202305001</td>
                        <td>CI1001</td>
                        <td>CARD001</td>
                        <td>信用卡还款</td>
                        <td>2023-05-15 14:30</td>
                        <td>上海分行</td>
                        <td>李经理</td>
                        <td>EMP1001</td>
                        <td class="action-cell">
                            <a href="#"><i class="fas fa-eye"></i> 查看</a>
                            <a href="#"><i class="fas fa-edit"></i> 编辑</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="checkbox-cell"><input type="checkbox"></td>
                        <td>王芳</td>
                        <td>招商银行</td>
                        <td>BATCH202305002</td>
                        <td>CI1002</td>
                        <td>CARD002</td>
                        <td>贷款还款</td>
                        <td>2023-05-16 10:15</td>
                        <td>北京分行</td>
                        <td>张经理</td>
                        <td>EMP1002</td>
                        <td class="action-cell">
                            <a href="#"><i class="fas fa-eye"></i> 查看</a>
                            <a href="#"><i class="fas fa-edit"></i> 编辑</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="checkbox-cell"><input type="checkbox" checked></td>
                        <td>李华</td>
                        <td>中国建设银行</td>
                        <td>BATCH202305003</td>
                        <td>CI1003</td>
                        <td>CARD003</td>
                        <td>分期还款</td>
                        <td>2023-05-17 09:45</td>
                        <td>广州分行</td>
                        <td>王经理</td>
                        <td>EMP1003</td>
                        <td class="action-cell">
                            <a href="#"><i class="fas fa-eye"></i> 查看</a>
                            <a href="#"><i class="fas fa-edit"></i> 编辑</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="checkbox-cell"><input type="checkbox" checked></td>
                        <td>陈晓</td>
                        <td>中国银行</td>
                        <td>BATCH202305004</td>
                        <td>CI1004</td>
                        <td>CARD004</td>
                        <td>提前还款</td>
                        <td>2023-05-18 16:20</td>
                        <td>深圳分行</td>
                        <td>赵经理</td>
                        <td>EMP1004</td>
                        <td class="action-cell">
                            <a href="#"><i class="fas fa-eye"></i> 查看</a>
                            <a href="#"><i class="fas fa-edit"></i> 编辑</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="checkbox-cell"><input type="checkbox" checked></td>
                        <td>刘伟</td>
                        <td>工商银行</td>
                        <td>BATCH202305005</td>
                        <td>CI1005</td>
                        <td>CARD005</td>
                        <td>信用卡还款</td>
                        <td>2023-05-19 11:30</td>
                        <td>杭州分行</td>
                        <td>钱经理</td>
                        <td>EMP1005</td>
                        <td class="action-cell">
                            <a href="#"><i class="fas fa-eye"></i> 查看</a>
                            <a href="#"><i class="fas fa-edit"></i> 编辑</a>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <div class="pagination">
                <button><i class="fas fa-chevron-left"></i></button>
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>4</button>
                <button><i class="fas fa-chevron-right"></i></button>
            </div>
        </section>
        
        <footer class="footer">
            <p>© 2023 金融管理系统 | 还款管理模块 | 数据更新时间: 2023-05-20 08:30:00</p>
        </footer>
    </div>
    
    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 全选功能
            const selectAll = document.querySelector('thead input[type="checkbox"]');
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            
            selectAll.addEventListener('change', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAll.checked;
                });
                updateSelectedStats();
            });
            
            // 单个复选框改变时更新统计
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedStats);
            });
            
            // 更新勾选统计
            function updateSelectedStats() {
                const selectedCount = document.querySelectorAll('tbody input[type="checkbox"]:checked').length;
                document.querySelector('.stats-selected .stat-value:first-child').textContent = selectedCount + '户';
                
                // 模拟计算选中金额（实际应用中应从数据计算）
                const selectedAmount = selectedCount * 3160;
                document.querySelector('.stats-selected .stat-value:last-child').textContent = '¥' + selectedAmount.toLocaleString() + '.00';
            }
            
            // 删除按钮事件
            document.querySelector('.btn-delete').addEventListener('click', function() {
                const selectedCount = document.querySelectorAll('tbody input[type="checkbox"]:checked').length;
                if (selectedCount > 0) {
                    if (confirm(`确定要删除选中的 ${selectedCount} 条还款记录吗？`)) {
                        alert('删除操作成功！');
                        // 实际应用中这里应发送删除请求到后端
                    }
                } else {
                    alert('请至少选择一条记录进行删除！');
                }
            });
            
            // 查询按钮事件
            document.querySelector('.btn-query').addEventListener('click', function() {
                alert('执行查询操作...');
                // 实际应用中这里应收集表单数据并发送查询请求
            });
        });
    </script>
</body>
</html>