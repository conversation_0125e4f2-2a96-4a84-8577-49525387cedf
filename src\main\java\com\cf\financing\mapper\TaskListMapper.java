package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.TaskList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 作业清单 Mapper 接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface TaskListMapper extends BaseMapper<TaskList> {

    /**
     * 分页查询作业清单
     *
     * @param page 分页参数
     * @param taskNo 任务编号
     * @param caseNo 案件编号
     * @param customerName 客户姓名
     * @param taskType 任务类型
     * @param taskStatus 任务状态
     * @param assignedUserId 分配人员ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<TaskList> selectTaskListPage(Page<TaskList> page,
                                       @Param("taskNo") String taskNo,
                                       @Param("caseNo") String caseNo,
                                       @Param("customerName") String customerName,
                                       @Param("taskType") Integer taskType,
                                       @Param("taskStatus") Integer taskStatus,
                                       @Param("assignedUserId") Long assignedUserId,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID查询待处理任务
     *
     * @param userId 用户ID
     * @return 待处理任务列表
     */
    List<TaskList> selectPendingTasksByUserId(@Param("userId") Long userId);

    /**
     * 根据案件ID查询相关任务
     *
     * @param caseId 案件ID
     * @return 任务列表
     */
    List<TaskList> selectTasksByCaseId(@Param("caseId") Long caseId);

    /**
     * 统计任务状态分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param assignedUserId 分配人员ID（可选）
     * @return 状态统计结果
     */
    List<Map<String, Object>> selectTaskStatusStatistics(@Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime,
                                                          @Param("assignedUserId") Long assignedUserId);

    /**
     * 统计任务类型分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param assignedUserId 分配人员ID（可选）
     * @return 类型统计结果
     */
    List<Map<String, Object>> selectTaskTypeStatistics(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime,
                                                        @Param("assignedUserId") Long assignedUserId);

    /**
     * 查询逾期任务
     *
     * @param currentTime 当前时间
     * @return 逾期任务列表
     */
    List<TaskList> selectOverdueTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 批量更新任务状态
     *
     * @param taskIds 任务ID列表
     * @param taskStatus 新状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateTaskStatus(@Param("taskIds") List<Long> taskIds,
                              @Param("taskStatus") Integer taskStatus,
                              @Param("updateBy") String updateBy);
}