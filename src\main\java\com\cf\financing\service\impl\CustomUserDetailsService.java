package com.cf.financing.service.impl;

import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义用户详情服务
 * 用于Spring Security认证
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    // 模拟用户数据，实际项目中应该从数据库获取
    private final Map<String, UserInfo> users = new HashMap<>();
    
    public CustomUserDetailsService() {
        // 初始化用户数据
        users.put("admin", new UserInfo("admin", passwordEncoder.encode("123456"), "ROLE_ADMIN"));
        users.put("manager", new UserInfo("manager", passwordEncoder.encode("123456"), "ROLE_MANAGER"));
        users.put("collector1", new UserInfo("collector1", passwordEncoder.encode("123456"), "ROLE_USER"));
        users.put("collector2", new UserInfo("collector2", passwordEncoder.encode("123456"), "ROLE_USER"));
        users.put("collector3", new UserInfo("collector3", passwordEncoder.encode("123456"), "ROLE_USER"));
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserInfo userInfo = users.get(username);
        
        if (userInfo == null) {
            throw new UsernameNotFoundException("用户不存在: " + username);
        }
        
        return User.builder()
                .username(userInfo.getUsername())
                .password(userInfo.getPassword())
                .authorities(Arrays.asList(new SimpleGrantedAuthority(userInfo.getRole())))
                .build();
    }
    
    /**
     * 用户信息内部类
     */
    private static class UserInfo {
        private final String username;
        private final String password;
        private final String role;
        
        public UserInfo(String username, String password, String role) {
            this.username = username;
            this.password = password;
            this.role = role;
        }
        
        public String getUsername() {
            return username;
        }
        
        public String getPassword() {
            return password;
        }
        
        public String getRole() {
            return role;
        }
    }
}
