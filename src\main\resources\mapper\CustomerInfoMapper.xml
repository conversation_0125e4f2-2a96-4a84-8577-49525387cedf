<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.CustomerInfoMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.CustomerInfo">
        <id column="id" property="id" />
        <result column="customer_no" property="customerNo" />
        <result column="customer_name" property="customerName" />
        <result column="id_card" property="idCard" />
        <result column="phone" property="phone" />
        <result column="phone2" property="phone2" />
        <result column="email" property="email" />
        <result column="address" property="address" />
        <result column="company" property="company" />
        <result column="income" property="income" />
        <result column="credit_level" property="creditLevel" />
        <result column="risk_level" property="riskLevel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 扩展结果映射（包含统计信息） -->
    <resultMap id="ExtendedResultMap" type="com.cf.financing.entity.CustomerInfo" extends="BaseResultMap">
        <result column="case_count" property="caseCount" />
        <result column="total_overdue_amount" property="totalOverdueAmount" />
        <result column="max_overdue_days" property="maxOverdueDays" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, customer_no, customer_name, id_card, phone, phone2, email, address, 
        company, income, credit_level, risk_level, create_time, update_time
    </sql>

    <!-- 分页查询客户信息 -->
    <select id="selectCustomerPage" resultMap="ExtendedResultMap">
        SELECT 
            c.id, c.customer_no, c.customer_name, c.id_card, c.phone, c.phone2, c.email, 
            c.address, c.company, c.income, c.credit_level, c.risk_level, c.create_time, c.update_time,
            COUNT(ci.id) as case_count,
            COALESCE(SUM(ci.overdue_amount), 0) as total_overdue_amount,
            COALESCE(MAX(ci.overdue_days), 0) as max_overdue_days
        FROM customer_info c
        LEFT JOIN case_info ci ON c.id = ci.customer_id AND ci.deleted = 0
        <where>
            <if test="customerNo != null and customerNo != ''">
                AND c.customer_no LIKE CONCAT('%', #{customerNo}, '%')
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="idCard != null and idCard != ''">
                AND c.id_card LIKE CONCAT('%', #{idCard}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND (c.phone LIKE CONCAT('%', #{phone}, '%') OR c.phone2 LIKE CONCAT('%', #{phone}, '%'))
            </if>
            <if test="email != null and email != ''">
                AND c.email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="creditLevel != null and creditLevel != ''">
                AND c.credit_level = #{creditLevel}
            </if>
            <if test="riskLevel != null and riskLevel != ''">
                AND c.risk_level = #{riskLevel}
            </if>
        </where>
        GROUP BY c.id
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据客户ID查询客户详细信息 -->
    <select id="selectCustomerById" resultMap="ExtendedResultMap">
        SELECT 
            c.id, c.customer_no, c.customer_name, c.id_card, c.phone, c.phone2, c.email, 
            c.address, c.company, c.income, c.credit_level, c.risk_level, c.create_time, c.update_time,
            COUNT(ci.id) as case_count,
            COALESCE(SUM(ci.overdue_amount), 0) as total_overdue_amount,
            COALESCE(MAX(ci.overdue_days), 0) as max_overdue_days
        FROM customer_info c
        LEFT JOIN case_info ci ON c.id = ci.customer_id AND ci.deleted = 0
        WHERE c.id = #{customerId}
        GROUP BY c.id
    </select>

    <!-- 根据身份证号查询客户信息 -->
    <select id="selectCustomerByIdCard" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM customer_info
        WHERE id_card = #{idCard}
    </select>

    <!-- 根据手机号查询客户信息 -->
    <select id="selectCustomerByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM customer_info
        WHERE phone = #{phone} OR phone2 = #{phone}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据客户编号查询客户信息 -->
    <select id="selectCustomerByNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM customer_info
        WHERE customer_no = #{customerNo}
    </select>

    <!-- 获取客户统计信息 -->
    <select id="selectCustomerStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalCustomers,
            COUNT(CASE WHEN credit_level = 'A' THEN 1 END) as levelACustomers,
            COUNT(CASE WHEN credit_level = 'B' THEN 1 END) as levelBCustomers,
            COUNT(CASE WHEN credit_level = 'C' THEN 1 END) as levelCCustomers,
            COUNT(CASE WHEN credit_level = 'D' THEN 1 END) as levelDCustomers,
            COUNT(CASE WHEN risk_level = 'LOW' THEN 1 END) as lowRiskCustomers,
            COUNT(CASE WHEN risk_level = 'MEDIUM' THEN 1 END) as mediumRiskCustomers,
            COUNT(CASE WHEN risk_level = 'HIGH' THEN 1 END) as highRiskCustomers,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayNewCustomers,
            COUNT(CASE WHEN create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekNewCustomers
        FROM customer_info
    </select>

    <!-- 获取客户风险分布统计 -->
    <select id="selectRiskLevelDistribution" resultType="map">
        SELECT 
            risk_level as riskLevel,
            COUNT(*) as customerCount,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM customer_info), 2) as percentage
        FROM customer_info
        GROUP BY risk_level
        ORDER BY 
            CASE risk_level 
                WHEN 'LOW' THEN 1 
                WHEN 'MEDIUM' THEN 2 
                WHEN 'HIGH' THEN 3 
                ELSE 4 
            END
    </select>

    <!-- 获取客户信用等级分布统计 -->
    <select id="selectCreditLevelDistribution" resultType="map">
        SELECT 
            credit_level as creditLevel,
            COUNT(*) as customerCount,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM customer_info), 2) as percentage
        FROM customer_info
        GROUP BY credit_level
        ORDER BY credit_level
    </select>

    <!-- 获取高风险客户列表 -->
    <select id="selectHighRiskCustomers" resultMap="ExtendedResultMap">
        SELECT 
            c.id, c.customer_no, c.customer_name, c.id_card, c.phone, c.phone2, c.email, 
            c.address, c.company, c.income, c.credit_level, c.risk_level, c.create_time, c.update_time,
            COUNT(ci.id) as case_count,
            COALESCE(SUM(ci.overdue_amount), 0) as total_overdue_amount,
            COALESCE(MAX(ci.overdue_days), 0) as max_overdue_days
        FROM customer_info c
        LEFT JOIN case_info ci ON c.id = ci.customer_id AND ci.deleted = 0
        WHERE c.risk_level = 'HIGH'
        GROUP BY c.id
        ORDER BY total_overdue_amount DESC, max_overdue_days DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取逾期金额最高的客户列表 -->
    <select id="selectTopOverdueCustomers" resultMap="ExtendedResultMap">
        SELECT 
            c.id, c.customer_no, c.customer_name, c.id_card, c.phone, c.phone2, c.email, 
            c.address, c.company, c.income, c.credit_level, c.risk_level, c.create_time, c.update_time,
            COUNT(ci.id) as case_count,
            COALESCE(SUM(ci.overdue_amount), 0) as total_overdue_amount,
            COALESCE(MAX(ci.overdue_days), 0) as max_overdue_days
        FROM customer_info c
        INNER JOIN case_info ci ON c.id = ci.customer_id AND ci.deleted = 0
        GROUP BY c.id
        HAVING total_overdue_amount > 0
        ORDER BY total_overdue_amount DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新客户风险等级 -->
    <update id="batchUpdateRiskLevel">
        UPDATE customer_info 
        SET risk_level = #{riskLevel},
            update_time = NOW()
        WHERE id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </update>

    <!-- 批量更新客户信用等级 -->
    <update id="batchUpdateCreditLevel">
        UPDATE customer_info 
        SET credit_level = #{creditLevel},
            update_time = NOW()
        WHERE id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </update>

    <!-- 检查身份证号是否存在 -->
    <select id="checkIdCardExists" resultType="int">
        SELECT COUNT(*) 
        FROM customer_info 
        WHERE id_card = #{idCard}
        <if test="excludeCustomerId != null">
            AND id != #{excludeCustomerId}
        </if>
    </select>

    <!-- 检查客户编号是否存在 -->
    <select id="checkCustomerNoExists" resultType="int">
        SELECT COUNT(*) 
        FROM customer_info 
        WHERE customer_no = #{customerNo}
        <if test="excludeCustomerId != null">
            AND id != #{excludeCustomerId}
        </if>
    </select>

    <!-- 获取客户月度新增趋势 -->
    <select id="selectMonthlyNewCustomerTrend" resultType="map">
        SELECT 
            DATE_FORMAT(create_time, '%Y-%m') as month,
            COUNT(*) as customerCount
        FROM customer_info
        WHERE create_time >= DATE_SUB(NOW(), INTERVAL #{months} MONTH)
        GROUP BY DATE_FORMAT(create_time, '%Y-%m')
        ORDER BY month
    </select>

    <!-- 搜索客户 -->
    <select id="searchCustomers" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM customer_info
        WHERE customer_name LIKE CONCAT('%', #{keyword}, '%')
           OR phone LIKE CONCAT('%', #{keyword}, '%')
           OR phone2 LIKE CONCAT('%', #{keyword}, '%')
           OR id_card LIKE CONCAT('%', #{keyword}, '%')
           OR customer_no LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY 
            CASE 
                WHEN customer_name = #{keyword} THEN 1
                WHEN phone = #{keyword} OR phone2 = #{keyword} THEN 2
                WHEN id_card = #{keyword} THEN 3
                WHEN customer_no = #{keyword} THEN 4
                ELSE 5
            END,
            create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

</mapper>