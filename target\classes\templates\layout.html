<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - CF金融催收管理系统'">CF金融催收管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #0c2461 0%, #1e3799 100%);
            color: #f0f2f5;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .top-navbar {
            background: rgba(12, 36, 97, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 40px;
            height: 40px;
            background-color: #165DFF;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .system-title {
            font-size: 18px;
            font-weight: 600;
            color: #f6b93b;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .nav-item {
            position: relative;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #f6b93b;
            transform: translateY(-2px);
        }

        .nav-item.active {
            background: rgba(246, 185, 59, 0.2);
            color: #f6b93b;
        }

        .nav-item i {
            margin-right: 8px;
            font-size: 16px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .user-name {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        .logout-btn {
            padding: 8px 16px;
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.4);
            border-radius: 6px;
            color: #fca5a5;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(239, 68, 68, 0.3);
            transform: translateY(-2px);
        }

        /* 主内容区域 */
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .page-header {
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(90deg, #f6b93b, #fad390);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .page-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .page-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .top-navbar {
                padding: 0 15px;
            }

            .nav-menu {
                gap: 15px;
            }

            .nav-item {
                padding: 6px 12px;
                font-size: 13px;
            }

            .nav-item span {
                display: none;
            }

            .system-title {
                font-size: 16px;
            }

            .main-container {
                padding: 15px;
            }

            .page-title {
                font-size: 20px;
            }
        }

        /* 面包屑导航 */
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .breadcrumb-item:not(:last-child)::after {
            content: '/';
            margin-left: 8px;
            color: rgba(255, 255, 255, 0.4);
        }

        .breadcrumb-item.active {
            color: #f6b93b;
        }

        /* 通知提示 */
        .notification {
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(12, 36, 97, 0.95);
            border: 1px solid rgba(246, 185, 59, 0.3);
            border-radius: 8px;
            padding: 15px 20px;
            color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-color: rgba(16, 185, 129, 0.3);
        }

        .notification.error {
            border-color: rgba(239, 68, 68, 0.3);
        }

        .notification.warning {
            border-color: rgba(245, 158, 11, 0.3);
        }
    </style>
    <th:block th:fragment="head-extra">
        <!-- 页面特定的CSS和JS可以在这里添加 -->
    </th:block>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
        <div class="logo-section">
            <div class="logo">
                <i class="fa fa-bank"></i>
            </div>
            <div class="system-title">CF-作业系统</div>
        </div>

        <div class="nav-menu">
            <a href="/dashboard" class="nav-item" th:classappend="${#strings.contains(#httpServletRequest.requestURI, 'dashboard')} ? 'active' : ''">
                <i class="fa fa-home"></i>
                <span>首页</span>
            </a>
            <a href="/case-pool" class="nav-item" th:classappend="${#strings.contains(#httpServletRequest.requestURI, 'case-pool')} ? 'active' : ''">
                <i class="fa fa-briefcase"></i>
                <span>案池</span>
            </a>
            <a href="/repayment" class="nav-item" th:classappend="${#strings.contains(#httpServletRequest.requestURI, 'repayment')} ? 'active' : ''">
                <i class="fa fa-money"></i>
                <span>还款管理</span>
            </a>
            <a href="/task-prediction" class="nav-item" th:classappend="${#strings.contains(#httpServletRequest.requestURI, 'task-prediction')} ? 'active' : ''">
                <i class="fa fa-line-chart"></i>
                <span>任务预测</span>
            </a>
            <a href="/exchange-query" class="nav-item" th:classappend="${#strings.contains(#httpServletRequest.requestURI, 'exchange-query')} ? 'active' : ''">
                <i class="fa fa-search"></i>
                <span>换单查询</span>
            </a>
            <a href="/new-statistics" class="nav-item" th:classappend="${#strings.contains(#httpServletRequest.requestURI, 'new-statistics')} ? 'active' : ''">
                <i class="fa fa-bar-chart"></i>
                <span>新增统计</span>
            </a>
        </div>

        <div class="user-section">
            <div class="user-info">
                <div class="user-avatar" th:text="${user != null ? user.substring(0,1).toUpperCase() : 'U'}">U</div>
                <div class="user-name" th:text="${user ?: '用户'}">用户</div>
            </div>
            <a href="/logout" class="logout-btn">
                <i class="fa fa-sign-out"></i>
                退出
            </a>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <!-- 面包屑导航 -->
            <div class="breadcrumb" th:if="${breadcrumbs}">
                <div class="breadcrumb-item" th:each="crumb, iterStat : ${breadcrumbs}" 
                     th:classappend="${iterStat.last} ? 'active' : ''">
                    <i th:class="${crumb.icon}" th:if="${crumb.icon}"></i>
                    <span th:text="${crumb.name}">面包屑</span>
                </div>
            </div>

            <!-- 页面标题 -->
            <h1 class="page-title" th:text="${pageTitle ?: title}">页面标题</h1>
            <p class="page-description" th:text="${pageDescription}" th:if="${pageDescription}">页面描述</p>
        </div>

        <!-- 页面内容 -->
        <div class="page-content">
            <th:block th:fragment="content">
                <!-- 页面具体内容在这里 -->
            </th:block>
        </div>
    </main>

    <!-- 通知提示 -->
    <div id="notification" class="notification">
        <div id="notificationContent"></div>
    </div>

    <!-- 公共JavaScript -->
    <script>
        // 显示通知
        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            const content = document.getElementById('notificationContent');
            
            content.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }

        // 页面加载完成后的通用初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有Flash消息
            const urlParams = new URLSearchParams(window.location.search);
            const message = urlParams.get('message');
            const type = urlParams.get('type');
            
            if (message) {
                showNotification(decodeURIComponent(message), type || 'info');
                // 清除URL参数
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        });
    </script>

    <th:block th:fragment="scripts">
        <!-- 页面特定的JavaScript可以在这里添加 -->
    </th:block>
</body>
</html>
