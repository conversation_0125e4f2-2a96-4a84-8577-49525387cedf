package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.ContactRecord;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 联系记录服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface IContactRecordService extends IService<ContactRecord> {

    /**
     * 分页查询联系记录
     *
     * @param page 分页参数
     * @param caseId 案件ID
     * @param customerId 客户ID
     * @param contactType 联系类型
     * @param contactResult 联系结果
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param operatorId 操作员ID
     * @return 分页结果
     */
    IPage<ContactRecord> getContactPage(
            Page<ContactRecord> page,
            Long caseId,
            Long customerId,
            String contactType,
            String contactResult,
            LocalDateTime startTime,
            LocalDateTime endTime,
            Long operatorId
    );

    /**
     * 根据案件ID查询联系记录
     *
     * @param caseId 案件ID
     * @return 联系记录列表
     */
    List<ContactRecord> getContactsByCaseId(Long caseId);

    /**
     * 根据客户ID查询联系记录
     *
     * @param customerId 客户ID
     * @return 联系记录列表
     */
    List<ContactRecord> getContactsByCustomerId(Long customerId);

    /**
     * 根据操作员ID查询联系记录
     *
     * @param operatorId 操作员ID
     * @return 联系记录列表
     */
    List<ContactRecord> getContactsByOperatorId(Long operatorId);

    /**
     * 获取最后一次联系记录
     *
     * @param caseId 案件ID
     * @return 联系记录
     */
    ContactRecord getLastContactRecord(Long caseId);

    /**
     * 创建联系记录
     *
     * @param contact 联系记录
     * @return 是否成功
     */
    boolean createContact(ContactRecord contact);

    /**
     * 更新联系记录
     *
     * @param contact 联系记录
     * @return 是否成功
     */
    boolean updateContact(ContactRecord contact);

    /**
     * 删除联系记录
     *
     * @param contactId 联系记录ID
     * @return 是否成功
     */
    boolean deleteContact(Long contactId);

    /**
     * 批量删除联系记录
     *
     * @param contactIds 联系记录ID列表
     * @return 是否成功
     */
    boolean batchDeleteContacts(List<Long> contactIds);

    /**
     * 根据案件ID获取联系次数
     *
     * @param caseId 案件ID
     * @return 联系次数
     */
    Integer getContactCountByCaseId(Long caseId);

    /**
     * 根据客户ID获取联系次数
     *
     * @param customerId 客户ID
     * @return 联系次数
     */
    Integer getContactCountByCustomerId(Long customerId);

    /**
     * 获取联系统计信息
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param operatorId 操作员ID
     * @return 统计数据
     */
    Map<String, Object> getContactStatistics(LocalDate startDate, LocalDate endDate, Long operatorId);

    /**
     * 获取今日联系统计
     *
     * @return 今日统计数据
     */
    Map<String, Object> getTodayContactStatistics();

    /**
     * 获取联系结果统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果统计数据
     */
    List<Map<String, Object>> getContactResultStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * 获取联系类型统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 类型统计数据
     */
    List<Map<String, Object>> getContactTypeStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * 获取联系趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getContactTrend(Integer days);

    /**
     * 获取月度联系统计
     *
     * @param months 月份数
     * @return 月度统计数据
     */
    List<Map<String, Object>> getMonthlyContactStatistics(Integer months);

    /**
     * 获取今日联系记录
     *
     * @return 今日联系记录列表
     */
    List<ContactRecord> getTodayContacts();

    /**
     * 获取承诺还款记录
     *
     * @param promiseDate 承诺日期
     * @return 承诺还款记录列表
     */
    List<ContactRecord> getPromiseRepaymentRecords(LocalDate promiseDate);

    /**
     * 获取操作员联系排行
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行数据
     */
    List<Map<String, Object>> getOperatorContactRanking(
            LocalDate startDate, LocalDate endDate, Integer limit
    );

    /**
     * 获取联系效果分析
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 效果分析数据
     */
    Map<String, Object> getContactEffectAnalysis(LocalDate startDate, LocalDate endDate);

    /**
     * 批量删除联系记录（按条件）
     *
     * @param caseId 案件ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 是否成功
     */
    boolean batchDeleteContactsByCondition(Long caseId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取跟进记录
     *
     * @param caseId 案件ID
     * @param days 天数
     * @return 跟进记录列表
     */
    List<ContactRecord> getFollowUpRecords(Long caseId, Integer days);
}