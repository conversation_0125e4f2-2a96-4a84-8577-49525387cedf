<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.CaseInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.CaseInfo">
        <id column="id" property="id" />
        <result column="case_no" property="caseNo" />
        <result column="customer_id" property="customerId" />
        <result column="product_name" property="productName" />
        <result column="loan_amount" property="loanAmount" />
        <result column="overdue_amount" property="overdueAmount" />
        <result column="overdue_days" property="overdueDays" />
        <result column="overdue_date" property="overdueDate" />
        <result column="case_status" property="caseStatus" />
        <result column="case_level" property="caseLevel" />
        <result column="assign_user_id" property="assignUserId" />
        <result column="assign_time" property="assignTime" />
        <result column="last_contact_time" property="lastContactTime" />
        <result column="next_contact_time" property="nextContactTime" />
        <result column="contact_result" property="contactResult" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 包含客户信息的结果映射 -->
    <resultMap id="CaseInfoWithCustomerMap" type="com.cf.financing.entity.CaseInfo" extends="BaseResultMap">
        <result column="customer_name" property="customerName" />
        <result column="customer_phone" property="customerPhone" />
        <result column="assign_user_name" property="assignUserName" />
        <result column="repaid_amount" property="repaidAmount" />
        <result column="remaining_amount" property="remainingAmount" />
        <result column="contact_count" property="contactCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, case_no, customer_id, product_name, loan_amount, overdue_amount, overdue_days, 
        overdue_date, case_status, case_level, assign_user_id, assign_time, last_contact_time, 
        next_contact_time, contact_result, remark, create_time, update_time, deleted
    </sql>

    <!-- 分页查询案件信息（包含客户信息） -->
    <select id="selectCaseInfoPage" resultMap="CaseInfoWithCustomerMap">
        SELECT 
            c.id, c.case_no, c.customer_id, c.product_name, c.loan_amount, c.overdue_amount, 
            c.overdue_days, c.overdue_date, c.case_status, c.case_level, c.assign_user_id, 
            c.assign_time, c.last_contact_time, c.next_contact_time, c.contact_result, 
            c.remark, c.create_time, c.update_time, c.deleted,
            ci.name AS customer_name,
            ci.phone AS customer_phone,
            u.real_name AS assign_user_name,
            COALESCE(r.repaid_amount, 0) AS repaid_amount,
            (c.overdue_amount - COALESCE(r.repaid_amount, 0)) AS remaining_amount,
            COALESCE(cr.contact_count, 0) AS contact_count
        FROM case_info c
        LEFT JOIN customer_info ci ON c.customer_id = ci.id
        LEFT JOIN sys_user u ON c.assign_user_id = u.id
        LEFT JOIN (
            SELECT case_id, SUM(repayment_amount) AS repaid_amount
            FROM repayment_record 
            WHERE repayment_status = 'SUCCESS' AND deleted = 0
            GROUP BY case_id
        ) r ON c.id = r.case_id
        LEFT JOIN (
            SELECT case_id, COUNT(*) AS contact_count
            FROM contact_record
            GROUP BY case_id
        ) cr ON c.id = cr.case_id
        WHERE c.deleted = 0
        <if test="caseNo != null and caseNo != ''">
            AND c.case_no LIKE CONCAT('%', #{caseNo}, '%')
        </if>
        <if test="customerName != null and customerName != ''">
            AND ci.name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="customerPhone != null and customerPhone != ''">
            AND ci.phone LIKE CONCAT('%', #{customerPhone}, '%')
        </if>
        <if test="caseStatus != null and caseStatus != ''">
            AND c.case_status = #{caseStatus}
        </if>
        <if test="caseLevel != null and caseLevel != ''">
            AND c.case_level = #{caseLevel}
        </if>
        <if test="assignUserId != null">
            AND c.assign_user_id = #{assignUserId}
        </if>
        <if test="overdueStartDate != null">
            AND c.overdue_date &gt;= #{overdueStartDate}
        </if>
        <if test="overdueEndDate != null">
            AND c.overdue_date &lt;= #{overdueEndDate}
        </if>
        <if test="minOverdueAmount != null">
            AND c.overdue_amount &gt;= #{minOverdueAmount}
        </if>
        <if test="maxOverdueAmount != null">
            AND c.overdue_amount &lt;= #{maxOverdueAmount}
        </if>
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据ID查询案件详细信息 -->
    <select id="selectCaseInfoById" resultMap="CaseInfoWithCustomerMap">
        SELECT 
            c.id, c.case_no, c.customer_id, c.product_name, c.loan_amount, c.overdue_amount, 
            c.overdue_days, c.overdue_date, c.case_status, c.case_level, c.assign_user_id, 
            c.assign_time, c.last_contact_time, c.next_contact_time, c.contact_result, 
            c.remark, c.create_time, c.update_time, c.deleted,
            ci.name AS customer_name,
            ci.phone AS customer_phone,
            u.real_name AS assign_user_name,
            COALESCE(r.repaid_amount, 0) AS repaid_amount,
            (c.overdue_amount - COALESCE(r.repaid_amount, 0)) AS remaining_amount,
            COALESCE(cr.contact_count, 0) AS contact_count
        FROM case_info c
        LEFT JOIN customer_info ci ON c.customer_id = ci.id
        LEFT JOIN sys_user u ON c.assign_user_id = u.id
        LEFT JOIN (
            SELECT case_id, SUM(repayment_amount) AS repaid_amount
            FROM repayment_record 
            WHERE repayment_status = 'SUCCESS' AND deleted = 0
            GROUP BY case_id
        ) r ON c.id = r.case_id
        LEFT JOIN (
            SELECT case_id, COUNT(*) AS contact_count
            FROM contact_record
            GROUP BY case_id
        ) cr ON c.id = cr.case_id
        WHERE c.id = #{id} AND c.deleted = 0
    </select>

    <!-- 查询我的案件列表 -->
    <select id="selectMyCaseList" resultMap="CaseInfoWithCustomerMap">
        SELECT 
            c.id, c.case_no, c.customer_id, c.product_name, c.loan_amount, c.overdue_amount, 
            c.overdue_days, c.overdue_date, c.case_status, c.case_level, c.assign_user_id, 
            c.assign_time, c.last_contact_time, c.next_contact_time, c.contact_result, 
            c.remark, c.create_time, c.update_time,
            ci.name AS customer_name,
            ci.phone AS customer_phone,
            COALESCE(r.repaid_amount, 0) AS repaid_amount,
            (c.overdue_amount - COALESCE(r.repaid_amount, 0)) AS remaining_amount,
            COALESCE(cr.contact_count, 0) AS contact_count
        FROM case_info c
        LEFT JOIN customer_info ci ON c.customer_id = ci.id
        LEFT JOIN (
            SELECT case_id, SUM(repayment_amount) AS repaid_amount
            FROM repayment_record 
            WHERE repayment_status = 'SUCCESS' AND deleted = 0
            GROUP BY case_id
        ) r ON c.id = r.case_id
        LEFT JOIN (
            SELECT case_id, COUNT(*) AS contact_count
            FROM contact_record
            GROUP BY case_id
        ) cr ON c.id = cr.case_id
        WHERE c.assign_user_id = #{assignUserId} AND c.deleted = 0
        <if test="caseStatus != null and caseStatus != ''">
            AND c.case_status = #{caseStatus}
        </if>
        <if test="caseLevel != null and caseLevel != ''">
            AND c.case_level = #{caseLevel}
        </if>
        ORDER BY c.next_contact_time ASC, c.create_time DESC
    </select>

    <!-- 统计案件数据 -->
    <select id="selectCaseStatistics" resultType="map">
        SELECT 
            COUNT(*) AS totalCases,
            COUNT(CASE WHEN case_status = 'PENDING' THEN 1 END) AS pendingCases,
            COUNT(CASE WHEN case_status = 'PROCESSING' THEN 1 END) AS processingCases,
            COUNT(CASE WHEN case_status = 'COMPLETED' THEN 1 END) AS completedCases,
            SUM(overdue_amount) AS totalOverdueAmount,
            SUM(CASE WHEN case_status = 'COMPLETED' THEN overdue_amount ELSE 0 END) AS recoveredAmount,
            ROUND(SUM(CASE WHEN case_status = 'COMPLETED' THEN overdue_amount ELSE 0 END) / SUM(overdue_amount) * 100, 2) AS recoveryRate
        FROM case_info
        WHERE deleted = 0
        <if test="assignUserId != null">
            AND assign_user_id = #{assignUserId}
        </if>
        <if test="startDate != null">
            AND create_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_time &lt;= #{endDate}
        </if>
    </select>

    <!-- 查询逾期案件统计（按等级分组） -->
    <select id="selectOverdueCaseStatsByLevel" resultType="map">
        SELECT 
            case_level AS caseLevel,
            COUNT(*) AS caseCount,
            SUM(overdue_amount) AS totalAmount,
            AVG(overdue_days) AS avgOverdueDays
        FROM case_info
        WHERE deleted = 0 AND case_status IN ('PENDING', 'PROCESSING')
        <if test="assignUserId != null">
            AND assign_user_id = #{assignUserId}
        </if>
        GROUP BY case_level
        ORDER BY case_level
    </select>

    <!-- 查询案件趋势数据 -->
    <select id="selectCaseTrendData" resultType="map">
        SELECT 
            DATE(create_time) AS statDate,
            COUNT(*) AS newCases,
            COUNT(CASE WHEN case_status = 'COMPLETED' THEN 1 END) AS completedCases,
            SUM(overdue_amount) AS totalAmount
        FROM case_info
        WHERE deleted = 0 AND create_time &gt;= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        <if test="assignUserId != null">
            AND assign_user_id = #{assignUserId}
        </if>
        GROUP BY DATE(create_time)
        ORDER BY statDate DESC
    </select>

    <!-- 批量分配案件 -->
    <update id="batchAssignCases">
        UPDATE case_info 
        SET assign_user_id = #{assignUserId}, assign_time = NOW(), update_time = NOW()
        WHERE id IN
        <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 批量更新案件状态 -->
    <update id="batchUpdateCaseStatus">
        UPDATE case_info 
        SET case_status = #{caseStatus}, update_time = NOW()
        WHERE id IN
        <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 查询待处理案件数量 -->
    <select id="selectPendingCaseCount" resultType="int">
        SELECT COUNT(*)
        FROM case_info
        WHERE assign_user_id = #{assignUserId} 
        AND case_status = 'PENDING' 
        AND deleted = 0
    </select>

    <!-- 查询今日到期案件 -->
    <select id="selectTodayDueCases" resultMap="CaseInfoWithCustomerMap">
        SELECT 
            c.id, c.case_no, c.customer_id, c.product_name, c.overdue_amount, 
            c.case_level, c.next_contact_time,
            ci.name AS customer_name,
            ci.phone AS customer_phone
        FROM case_info c
        LEFT JOIN customer_info ci ON c.customer_id = ci.id
        WHERE c.assign_user_id = #{assignUserId} 
        AND DATE(c.next_contact_time) = #{targetDate}
        AND c.case_status IN ('PENDING', 'PROCESSING')
        AND c.deleted = 0
        ORDER BY c.next_contact_time ASC
    </select>

</mapper>