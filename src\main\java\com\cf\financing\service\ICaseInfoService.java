package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.CaseInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 案件信息服务类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ICaseInfoService extends IService<CaseInfo> {

    /**
     * 分页查询案件信息
     *
     * @param page 分页参数
     * @param caseNo 案件编号
     * @param customerName 客户姓名
     * @param customerPhone 客户手机号
     * @param caseStatus 案件状态
     * @param caseLevel 案件等级
     * @param assignUserId 分配催收员ID
     * @param overdueStartDate 逾期开始日期
     * @param overdueEndDate 逾期结束日期
     * @param minOverdueAmount 最小逾期金额
     * @param maxOverdueAmount 最大逾期金额
     * @return 案件信息分页结果
     */
    IPage<CaseInfo> getCaseInfoPage(
            Page<CaseInfo> page,
            String caseNo,
            String customerName,
            String customerPhone,
            String caseStatus,
            String caseLevel,
            Long assignUserId,
            LocalDate overdueStartDate,
            LocalDate overdueEndDate,
            BigDecimal minOverdueAmount,
            BigDecimal maxOverdueAmount
    );

    /**
     * 根据ID查询案件详细信息
     *
     * @param id 案件ID
     * @return 案件详细信息
     */
    CaseInfo getCaseInfoById(Long id);

    /**
     * 查询我的案件列表
     *
     * @param page 分页参数
     * @param assignUserId 分配催收员ID
     * @param caseStatus 案件状态
     * @param caseLevel 案件等级
     * @return 案件信息分页结果
     */
    IPage<CaseInfo> getMyCaseList(
            Page<CaseInfo> page,
            Long assignUserId,
            String caseStatus,
            String caseLevel
    );

    /**
     * 创建案件
     *
     * @param caseInfo 案件信息
     * @return 是否成功
     */
    boolean createCase(CaseInfo caseInfo);

    /**
     * 更新案件信息
     *
     * @param caseInfo 案件信息
     * @return 是否成功
     */
    boolean updateCase(CaseInfo caseInfo);

    /**
     * 分配案件
     *
     * @param caseId 案件ID
     * @param assignUserId 分配催收员ID
     * @return 是否成功
     */
    boolean assignCase(Long caseId, Long assignUserId);

    /**
     * 批量分配案件
     *
     * @param caseIds 案件ID列表
     * @param assignUserId 分配催收员ID
     * @return 是否成功
     */
    boolean batchAssignCases(List<Long> caseIds, Long assignUserId);

    /**
     * 更新案件状态
     *
     * @param caseId 案件ID
     * @param caseStatus 案件状态
     * @return 是否成功
     */
    boolean updateCaseStatus(Long caseId, String caseStatus);

    /**
     * 批量更新案件状态
     *
     * @param caseIds 案件ID列表
     * @param caseStatus 案件状态
     * @return 是否成功
     */
    boolean batchUpdateCaseStatus(List<Long> caseIds, String caseStatus);

    /**
     * 更新联系信息
     *
     * @param caseId 案件ID
     * @param contactResult 联系结果
     * @param nextContactTime 下次联系时间
     * @return 是否成功
     */
    boolean updateContactInfo(Long caseId, String contactResult, LocalDate nextContactTime);

    /**
     * 统计案件数据
     *
     * @param assignUserId 催收员ID（为空则统计全部）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    Map<String, Object> getCaseStatistics(Long assignUserId, LocalDate startDate, LocalDate endDate);

    /**
     * 查询逾期案件统计（按等级分组）
     *
     * @param assignUserId 催收员ID（为空则统计全部）
     * @return 逾期案件统计
     */
    List<Map<String, Object>> getOverdueCaseStatsByLevel(Long assignUserId);

    /**
     * 查询案件趋势数据
     *
     * @param assignUserId 催收员ID（为空则统计全部）
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getCaseTrendData(Long assignUserId, Integer days);

    /**
     * 查询待处理案件数量
     *
     * @param assignUserId 催收员ID
     * @return 待处理案件数量
     */
    int getPendingCaseCount(Long assignUserId);

    /**
     * 查询今日到期案件
     *
     * @param assignUserId 催收员ID
     * @return 到期案件列表
     */
    List<CaseInfo> getTodayDueCases(Long assignUserId);

    /**
     * 生成案件编号
     *
     * @return 案件编号
     */
    String generateCaseNo();

    /**
     * 删除案件
     *
     * @param caseId 案件ID
     * @return 是否成功
     */
    boolean deleteCase(Long caseId);

    /**
     * 批量删除案件
     *
     * @param caseIds 案件ID列表
     * @return 是否成功
     */
    boolean batchDeleteCases(List<Long> caseIds);

    /**
     * 获取案件状态分布
     *
     * @return 状态分布数据
     */
    List<Map<String, Object>> getCaseStatusDistribution();

    /**
     * 获取每日案件统计
     *
     * @param days 天数
     * @param assignUserId 指派用户ID
     * @return 每日统计数据
     */
    List<Map<String, Object>> getDailyCaseStats(Integer days, Long assignUserId);

    /**
     * 获取月度案件统计
     *
     * @param months 月份数
     * @param assignUserId 指派用户ID
     * @return 月度统计数据
     */
    List<Map<String, Object>> getMonthlyCaseStats(Integer months, Long assignUserId);
}