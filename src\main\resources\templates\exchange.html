<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>换单查询系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        :root {
            --primary-color: #165DFF;
            --primary-light: #E8F3FF;
            --success-color: #00B42A;
            --success-light: #E8FFEF;
            --danger-color: #F53F3F;
            --danger-light: #FFF2F2;
            --text-color: #1D2129;
            --text-secondary: #4E5969;
            --bg-color: #F7F8FA;
            --card-bg: #FFFFFF;
            --border-color: #E5E6EB;
            --hover-bg: #F2F3F5;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            padding: 10px;
            display: flex;
            justify-content: center;
            font-size: 14px;
            overflow: hidden;
        }
        
        .app-container {
            width: 100%;
            max-width: 1400px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            background: var(--card-bg);
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 900px;
        }
        
        .app-container:hover {
            box-shadow: var(--shadow-lg);
        }
        
        /* 导航栏 */
        .app-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14px 20px;
            border-bottom: 1px solid var(--border-color);
            background: var(--card-bg);
            position: relative;
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        /* 筛选框区域 */
        .query-form {
            padding: 18px 20px;
            background: var(--card-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 10;
        }
        
        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .form-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
        }
        
        @media (max-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 480px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        
        .form-group label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
            white-space: normal;
        }
        
        .form-control {
            padding: 8px 12px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 13px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.2);
        }
        
        .form-control::placeholder {
            color: #C9CDD4;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: var(--transition);
            justify-content: center;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: #0D47A1;
            box-shadow: 0 2px 8px rgba(22, 93, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            background: #E5E6EB;
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 12px;
            justify-content: flex-end;
        }
        
        /* 统计信息区域 */
        .summary-info {
            background: var(--card-bg);
            padding: 10px 20px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            border-bottom: 1px solid var(--border-color);
            position: relative;
            z-index: 5;
        }
        
        @media (max-width: 768px) {
            .summary-info {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 480px) {
            .summary-info {
                grid-template-columns: 1fr;
            }
        }
        
        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            background: var(--primary-light);
            border-radius: 6px;
            transition: var(--transition);
        }
        
        .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .summary-value {
            font-size: 16px;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1.2;
            margin-bottom: 3px;
        }
        
        .summary-value.success {
            color: var(--success-color);
        }
        
        .summary-value.danger {
            color: var(--danger-color);
        }
        
        .summary-label {
            font-size: 11px;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        /* 记录展示区域 */
        .table-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        .table-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            min-width: 800px;
        }
        
        .data-table th {
            text-align: left;
            padding: 10px 12px;
            background-color: #F2F3F5;
            color: var(--text-secondary);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 150;
            border-bottom: 2px solid var(--border-color);
            font-size: 12px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .data-table td {
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-color);
            transition: var(--transition);
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .data-table tr:hover {
            background: #F9FAFB;
        }
        
        .data-table .checkbox-cell {
            width: 36px;
            text-align: center;
        }
        
        .data-table .highlight {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .status-tag {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }
        
        .status-tag.success {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .status-tag.danger {
            background: var(--danger-light);
            color: var(--danger-color);
        }
        
        .action-btn {
            padding: 4px 8px;
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            display: inline-flex;
            align-items: center;
            gap: 2px;
            transition: var(--transition);
        }
        
        .action-btn:hover {
            background: #E5E6EB;
            transform: translateY(-1px);
        }
        
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-top: 1px solid var(--border-color);
            background: var(--card-bg);
            font-size: 12px;
        }
        
        .pagination-controls {
            display: flex;
            gap: 5px;
        }
        
        .pagination-btn {
            padding: 6px 10px;
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            transition: var(--transition);
            font-size: 11px;
            min-width: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .pagination-btn:hover {
            background: #E5E6EB;
        }
        
        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            font-weight: bold;
            border-color: var(--primary-color);
        }
        
        .pagination-btn:disabled {
            background: #F2F3F5;
            color: #C9CDD4;
            cursor: not-allowed;
            border-color: #E5E6EB;
        }
        
        .pagination-info {
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 导航栏 -->
        <div class="app-nav">
            <div class="app-title">
                <i class="fas fa-exchange-alt"></i>
                换单查询系统
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 筛选框区域 -->
            <div class="query-form">
                <div class="form-header">
                    <div class="form-title">
                        <i class="fas fa-filter"></i>
                        查询条件
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label>客户姓名</label>
                        <input type="text" class="form-control" placeholder="请输入客户姓名" id="customerName">
                    </div>
                    
                    <div class="form-group">
                        <label>客户索引号</label>
                        <input type="text" class="form-control" placeholder="请输入客户索引号" id="customerId">
                    </div>
                    
                    <div class="form-group">
                        <label>换单状态</label>
                        <select class="form-control" id="exchangeStatus">
                            <option value="">全部状态</option>
                            <option value="pending">待处理</option>
                            <option value="approved">已批准</option>
                            <option value="rejected">已拒绝</option>
                            <option value="completed">已完成</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>申请时间</label>
                        <input type="text" class="form-control" placeholder="选择日期范围" id="dateRange">
                    </div>
                    
                    <div class="form-group">
                        <label>作业员</label>
                        <select class="form-control" id="operator">
                            <option value="">全部作业员</option>
                            <option value="zhang">张三</option>
                            <option value="li">李四</option>
                            <option value="wang">王五</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button class="btn btn-secondary" id="resetBtn">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                    <button class="btn btn-primary" id="searchBtn">
                        <i class="fas fa-search"></i>
                        查询
                    </button>
                </div>
            </div>

            <!-- 统计信息区域 -->
            <div class="summary-info">
                <div class="summary-item">
                    <div class="summary-value" id="totalCount">0</div>
                    <div class="summary-label">总换单数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value success" id="approvedCount">0</div>
                    <div class="summary-label">已批准</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value danger" id="rejectedCount">0</div>
                    <div class="summary-label">已拒绝</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="pendingCount">0</div>
                    <div class="summary-label">待处理</div>
                </div>
            </div>

            <!-- 记录展示区域 -->
            <div class="table-container">
                <div class="table-content">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th>客户姓名</th>
                                <th>客户索引号</th>
                                <th>原作业员</th>
                                <th>新作业员</th>
                                <th>换单原因</th>
                                <th>申请时间</th>
                                <th>处理时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- 数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">
                        共 <span id="totalRecords">0</span> 条记录，第 <span id="currentPage">1</span> / <span id="totalPages">1</span> 页
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn" id="nextBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockData = [
            {
                id: 1,
                customerName: '张三',
                customerId: 'KH001',
                originalOperator: '李四',
                newOperator: '王五',
                reason: '工作量调整',
                applyTime: '2023-12-01 10:30',
                processTime: '2023-12-01 14:20',
                status: 'approved'
            },
            {
                id: 2,
                customerName: '李明',
                customerId: 'KH002',
                originalOperator: '王五',
                newOperator: '张三',
                reason: '专业匹配',
                applyTime: '2023-12-02 09:15',
                processTime: '2023-12-02 16:45',
                status: 'rejected'
            },
            {
                id: 3,
                customerName: '王芳',
                customerId: 'KH003',
                originalOperator: '张三',
                newOperator: '李四',
                reason: '客户要求',
                applyTime: '2023-12-03 11:20',
                processTime: '',
                status: 'pending'
            },
            {
                id: 4,
                customerName: '刘强',
                customerId: 'KH004',
                originalOperator: '李四',
                newOperator: '王五',
                reason: '技能匹配',
                applyTime: '2023-12-04 08:45',
                processTime: '2023-12-04 17:30',
                status: 'completed'
            },
            {
                id: 5,
                customerName: '陈静',
                customerId: 'KH005',
                originalOperator: '王五',
                newOperator: '张三',
                reason: '地域调整',
                applyTime: '2023-12-05 13:10',
                processTime: '',
                status: 'pending'
            }
        ];

        // 状态映射
        const statusMap = {
            'pending': { text: '待处理', class: 'warning' },
            'approved': { text: '已批准', class: 'success' },
            'rejected': { text: '已拒绝', class: 'danger' },
            'completed': { text: '已完成', class: 'success' }
        };

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            mockData.forEach(item => {
                const status = statusMap[item.status];
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="checkbox-cell">
                        <input type="checkbox" data-id="${item.id}">
                    </td>
                    <td>${item.customerName}</td>
                    <td class="highlight">${item.customerId}</td>
                    <td>${item.originalOperator}</td>
                    <td>${item.newOperator}</td>
                    <td>${item.reason}</td>
                    <td>${item.applyTime}</td>
                    <td>${item.processTime || '-'}</td>
                    <td>
                        <span class="status-tag ${status.class}">${status.text}</span>
                    </td>
                    <td>
                        <button class="action-btn" onclick="viewDetail(${item.id})">
                            <i class="fas fa-eye"></i>
                            查看
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateSummary();
        }

        // 更新统计信息
        function updateSummary() {
            document.getElementById('totalCount').textContent = mockData.length;
            document.getElementById('approvedCount').textContent = mockData.filter(item => item.status === 'approved').length;
            document.getElementById('rejectedCount').textContent = mockData.filter(item => item.status === 'rejected').length;
            document.getElementById('pendingCount').textContent = mockData.filter(item => item.status === 'pending').length;
        }

        // 查看详情
        function viewDetail(id) {
            const item = mockData.find(data => data.id === id);
            if (item) {
                alert(`查看换单详情：\n客户：${item.customerName}\n索引号：${item.customerId}\n状态：${statusMap[item.status].text}`);
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 渲染表格
            renderTable();

            // 初始化日期选择器
            flatpickr("#dateRange", {
                mode: "range",
                dateFormat: "Y-m-d",
                locale: "zh"
            });

            // 搜索功能
            document.getElementById('searchBtn').addEventListener('click', function() {
                // 这里可以添加实际的搜索逻辑
                console.log('执行搜索');
            });

            // 重置功能
            document.getElementById('resetBtn').addEventListener('click', function() {
                document.getElementById('customerName').value = '';
                document.getElementById('customerId').value = '';
                document.getElementById('exchangeStatus').value = '';
                document.getElementById('dateRange').value = '';
                document.getElementById('operator').value = '';
            });

            // 全选功能
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('#tableBody input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        });
    </script>
</body>
</html>
