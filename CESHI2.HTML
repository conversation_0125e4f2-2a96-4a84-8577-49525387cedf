<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CF-作业系统框架</title>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 主色调与辅助色（替换为新配色） */
            --primary-color: #1a2a6c;       /* 深蓝主色 */
            --secondary-color: #FFCC00;      /* 黄色辅助色（下划线动画） */
            --success-color: #00B42A;
            --warning-color: #FF7D00;
            --error-color: #F53F3F;
            --info-color: #86909C;
            --sidebar-bg: #1E293B;           /* 侧边栏背景色 */
            --sidebar-item: #CBD5E1;         /* 侧边栏文字色 */
            --sidebar-item-hover: #FFFFFF;   /* 侧边栏悬停文字色 */
            --sidebar-item-active: #FFCC00;   /* 侧边栏激活文字色 */
            --content-bg: #F8FAFC;
            --nav-bg: #1a2a6c;                /* 顶部导航背景色 */
            --breadcrumb-bg: #E2E8F0;
        }

        .container {
            display: flex;
            height: 100vh;
            flex-direction: column;
            overflow: hidden;
        }

        .nav-bar-1 {
            width: 100%;
            height: 64px;
            background-color: var(--nav-bg);
            display: flex;
            z-index: 100;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logo-area {
            width: 280px;
            height: 100%;
            background-color: var(--sidebar-bg);
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-right: 1px solid rgba(255,255,255,0.1);
        }

        .logo-image {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo-image i {
            color: white;
            font-size: 24px;
        }

        .company-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--sidebar-item-active);
            letter-spacing: 0.5px;
        }

        .system-messages {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 0 20px;
            overflow: hidden;
            position: relative;
        }

        .messages-container {
            flex: 1;
            height: 32px;
            overflow: hidden;
            position: relative;
        }

        .message-list {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .message-item {
            height: 32px;
            line-height: 32px;
            color: var(--sidebar-item-active);
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 10px;
            border-radius: 4px;
        }

        .message-info {
            background-color: rgba(26, 42, 108, 0.2); /* 深蓝背景 */
        }

        .message-warning {
            background-color: rgba(255, 204, 0, 0.2); /* 黄色背景 */
        }

        .user-info {
            width: 300px;
            height: 100%;
            background-color: var(--sidebar-bg);
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 20px;
            color: var(--sidebar-item-active);
            border-left: 1px solid rgba(255,255,255,0.1);
            position: relative;
        }

        .greeting-container {
            display: flex;
            align-items: center;
            margin-right: 20px;/*问候语位置*/
        }

        .notification-bell {
            margin-right: 40px;/*铃铛位置*/
            font-size: 22px;/*铃铛大小*/
            cursor: pointer;
            position: relative;
            transition: transform 0.3s;
            color: white;
        }

        .notification-bell:hover {
            transform: scale(1.2);
        }

        .notification-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
        }

        .notification-panel {
            position: fixed;
            top: 64px;
            right: -320px;
            width: 320px;
            height: calc(100vh - 64px);
            background-color: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s;
            z-index: 1000;
            overflow-y: auto;
        }

        .notification-panel.active {
            right: 0;
        }

        .notification-header {
            padding: 16px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
        }

        .close-notification {
            cursor: pointer;
            color: #64748B;
        }

        .close-notification:hover {
            color: var(--error-color);
        }

        .notification-list {
            padding: 16px;
        }

        .notification-item {
            padding: 12px;
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative; /* 为下划线动画准备 */
        }

        .notification-item:hover {
            background-color: #f8fafc;
        }

        .notification-item.unread {
            background-color: rgba(26, 42, 108, 0.05);
        }

        .notification-time {
            font-size: 12px;
            color: #86909C;
            margin-bottom: 4px;
        }

        .notification-content {
            font-size: 14px;
            color: #333333;
        }

        .user-avatar {
            font-size: 32px !important;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .user-avatar:hover {
            transform: scale(1.2);
        }

        .nav-bar-2 {
            width: calc(100% - 280px);
            height: 40px;
            background-color: var(--breadcrumb-bg);
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #333333;
            margin-left: 280px;
            z-index: 99;
            padding: 0 10px;
            transition: all 0.3s;
            border-bottom: 1px solid #CBD5E1;
        }

        .sidebar-toggle {
            margin-right: 10px;
            cursor: pointer;
            padding: 4px 8px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
            display: flex;
            align-items: center;
            transition: all 0.2s;
        }

        .sidebar {
            width: 280px;
            height: calc(100vh - 64px);
            background-color: var(--sidebar-bg);
            position: fixed;
            top: 64px;
            left: 0;
            overflow-y: auto;
            transition: transform 0.3s;
            z-index: 98;
        }

        .sidebar.collapsed {
            transform: translateX(-280px);
        }

        .menu-item {
            padding: 14px 24px;
            cursor: pointer;
            transition: all 0.2s;
            color: var(--sidebar-item);
            display: flex;
            align-items: center;
            border-left: 3px solid transparent;
            position: relative; /* 下划线动画容器 */
        }

        /* 新增：菜单悬停下划线动画 */
        .menu-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50px; /* 图标宽度+间距 */
            width: 0;
            height: 2px;
            background-color: var(--secondary-color); /* 黄色下划线 */
            transition: width 0.3s ease;
        }

        .menu-item:hover::after {
            width: calc(100% - 50px); /* 减去图标宽度+间距 */
        }

        .menu-item.active {
            background-color: rgba(26, 42, 108, 0.1); /* 深蓝激活背景 */
            color: var(--sidebar-item-active);
            border-left-color: var(--primary-color);
        }

        .menu-item i {
            width: 24px;
            margin-right: 12px;
            font-size: 16px;
            text-align: center;
        }

        .content {
            flex: 1;
            margin-left: 270px;
            background-color: var(--content-bg);
            padding: 15px;
            transition: all 0.3s;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .content.expanded {
            margin-left: 0;
            width: 100%;
        }

        .page-container {
            padding: 15px;
            height: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            overflow: auto;
            display: none;
        }

        .page-container.active {
            display: block;
        }

        .profile-form {
            max-width: 500px;
            margin: 20px auto;
            padding: 24px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close-btn {
            cursor: pointer;
            font-size: 20px;
            color: var(--info-color);
            transition: color 0.3s;
        }

        .close-btn:hover {
            color: var(--error-color);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--sidebar-item);
        }

        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
        }

        input[readonly] {
            background: #f8fafc;
            cursor: not-allowed;
        }

        button[type="submit"] {
            background-color: var(--primary-color);
            color: white;
            padding: 8px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button[type="submit"]:hover {
            background-color: #112a56; /* 更深的蓝色 */
        }

        .tab-nav {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 20px;
        }

        .tab-item {
            padding: 6px 12px;
            background: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            border: 1px solid #e2e8f0;
            transition: all 0.2s;
            position: relative; /* 标签下划线 */
        }

        .tab-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--secondary-color);
            transition: width 0.3s ease;
        }

        .tab-item.active::after,
        .tab-item:hover::after {
            width: 100%;
        }

        .tab-item.active {
            border-color: var(--primary-color);
            background: rgba(26, 42, 108, 0.1);
        }

        .tab-close {
            color: var(--info-color);
            padding-left: 8px;
            margin-left: 8px;
            border-left: 1px solid #e2e8f0;
            transition: all 0.2s;
        }

        .tab-close:hover {
            color: var(--error-color);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-bar-1">
            <div class="logo-area">
                <div class="logo-image">
                    <i class="fa fa-bank"></i>
                </div>
                <div class="company-name">CF-作业系统</div>
            </div>
            
            <div class="system-messages">
                <div class="messages-container">
                    <div class="message-list">
                        <div class="message-item message-info">系统公告：系统运行正常</div>
                        <div class="message-item message-warning">警告：今日有3个待处理事项</div>
                    </div>
                </div>
            </div>
            
            <div class="user-info">
                <div class="greeting-container">
                    <div class="notification-bell" onclick="toggleNotificationPanel()">
                        <i class="fa fa-bell"></i>
                        <span class="notification-count" id="notificationCount">3</span>
                    </div>
                    <span class="greeting-text" id="greetingText"></span>
                    <i class="fa fa-user-circle-o user-avatar" onclick="showProfile()"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">张三</div>
                    <div class="user-id">工号：10086</div>
                </div>
            </div>
        </div>

        <!-- 通知面板 -->
        <div class="notification-panel" id="notificationPanel">
            <div class="notification-header">
                <div class="notification-title">系统通知</div>
                <div class="close-notification" onclick="toggleNotificationPanel()">
                    <i class="fa fa-times"></i>
                </div>
            </div>
            <div class="notification-list" id="notificationList">
                <div class="notification-item unread">
                    <div class="notification-time">刚刚</div>
                    <div class="notification-content">您有3个待处理的还款提醒</div>
                </div>
                <div class="notification-item unread">
                    <div class="notification-time">10分钟前</div>
                    <div class="notification-content">系统检测到异常登录尝试</div>
                </div>
                <div class="notification-item unread">
                    <div class="notification-time">今天 09:30</div>
                    <div class="notification-content">您的个人信息需要更新</div>
                </div>
                <div class="notification-item">
                    <div class="notification-time">昨天 16:45</div>
                    <div class="notification-content">本月业绩目标已完成80%</div>
                </div>
                <div class="notification-item">
                    <div class="notification-time">2025-05-27</div>
                    <div class="notification-content">系统更新已完成</div>
                </div>
            </div>
        </div>

        <div class="nav-bar-2">
            <div class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fa fa-bars"></i>
            </div>
            <div class="tab-nav" id="tabNav"></div>
        </div>

        <div class="sidebar" id="sidebar">
            <div class="menu-items">
                <div class="menu-item active" data-id="home" onclick="showPage('home')">
                    <i class="fa fa-home"></i> 首页
                </div>
                <div class="menu-item" data-id="system" onclick="showPage('system')">
                    <i class="fa fa-cog"></i> 系统管理
                </div>
                <div class="menu-item" data-id="casepool" onclick="showPage('casepool')">
                    <i class="fa fa-folder-open"></i> 案池管理
                </div>
                <div class="menu-item" data-id="tasklist" onclick="showPage('tasklist')">
                    <i class="fa fa-tasks"></i> 作业清单
                </div>
                <div class="menu-item" data-id="repayment" onclick="showPage('repayment')">
                    <i class="fa fa-credit-card"></i> 还款管理
                </div>
                <div class="menu-item" data-id="statistics" onclick="showPage('statistics')">
                    <i class="fa fa-bar-chart"></i> 数据统计
                </div>
                <div class="menu-item" data-id="prediction" onclick="showPage('prediction')">
                    <i class="fa fa-line-chart"></i> 任务预测
                </div>
                <div class="menu-item" data-id="orderchange" onclick="showPage('orderchange')">
                    <i class="fa fa-exchange"></i> 换单记录
                </div>
                <div class="menu-item" data-id="orderrequest" onclick="showPage('orderrequest')">
                    <i class="fa fa-hand-paper-o"></i> 索单
                </div>
            </div>
        </div>

        <div class="content" id="content">
            <div id="page-home" class="page-container active">
                <h2>欢迎使用CF作业系统</h2>
                <p>请从左侧菜单选择功能模块</p>
            </div>

            <div id="page-profile" class="page-container">
                <div class="profile-form">
                    <div class="form-header">
                        <h3>个人信息管理</h3>
                        <i class="fa fa-times close-btn" onclick="closeProfile()"></i>
                    </div>
                    <form onsubmit="updateProfile(event)">
                        <div class="form-group">
                            <label>工号：</label>
                            <input type="text" value="10086" readonly>
                        </div>
                        <div class="form-group">
                            <label>姓名：</label>
                            <input type="text" value="张三" readonly>
                        </div>
                        <div class="form-group">
                            <label>部门：</label>
                            <input type="text" value="信贷管理部" readonly>
                        </div>
                        <div class="form-group">
                            <label>手机号码：</label>
                            <input type="tel" id="mobile" value="13800138000" required>
                        </div>
                        <div class="form-group">
                            <label>新密码：</label>
                            <input type="password" id="newPassword" placeholder="留空则不修改">
                        </div>
                        <div class="form-group">
                            <label>确认密码：</label>
                            <input type="password" id="confirmPassword" placeholder="再次输入新密码">
                        </div>
                        <button type="submit">保存修改</button>
                    </form>
                </div>
            </div>

            <div id="page-system" class="page-container"><h3>系统管理</h3></div>
            <div id="page-casepool" class="page-container"><h3>案池管理</h3></div>
            <div id="page-tasklist" class="page-container"><h3>作业清单</h3></div>
            <div id="page-repayment" class="page-container"><h3>还款管理</h3></div>
            <div id="page-statistics" class="page-container"><h3>数据统计</h3></div>
            <div id="page-prediction" class="page-container"><h3>任务预测</h3></div>
            <div id="page-orderchange" class="page-container"><h3>换单记录</h3></div>
            <div id="page-orderrequest" class="page-container"><h3>索单管理</h3></div>
        </div>
    </div>

    <script>
        // 系统状态管理
        let openedPages = ['home'];
        let currentPage = 'home';
        let isSidebarCollapsed = false;
        let notifications = [
            {id: 1, time: '刚刚', content: '您有3个待处理的还款提醒', read: false},
            {id: 2, time: '10分钟前', content: '系统检测到异常登录尝试', read: false},
            {id: 3, time: '今天 09:30', content: '您的个人信息需要更新', read: false},
            {id: 4, time: '昨天 16:45', content: '本月业绩目标已完成80%', read: true},
            {id: 5, time: '2025-05-27', content: '系统更新已完成', read: true}
        ];

        // 侧边栏切换
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            const navBar2 = document.querySelector('.nav-bar-2');
            
            isSidebarCollapsed = !isSidebarCollapsed;
            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');
            
            if (isSidebarCollapsed) {
                navBar2.style.width = '100%';
                navBar2.style.marginLeft = '0';
                content.style.width = 'calc(100% - 2px)';
            } else {
                navBar2.style.width = 'calc(100% - 280px)';
                navBar2.style.marginLeft = '280px';
                content.style.width = 'calc(100% - 280px - 2px)';
            }
        }

        // 页面切换
        function showPage(pageId) {
            currentPage = pageId;
            if (!openedPages.includes(pageId)) {
                openedPages.push(pageId);
            }

            document.querySelectorAll('.page-container').forEach(page => {
                page.classList.remove('active');
            });

            const targetPage = document.getElementById(`page-${pageId}`);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.id === pageId) {
                    item.classList.add('active');
                }
            });

            updateTabs();
        }

        // 个人信息管理
        function showProfile() {
            if (!openedPages.includes('profile')) {
                openedPages.push('profile');
            }
            currentPage = 'profile';
            updateTabs();
            document.querySelectorAll('.page-container').forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById('page-profile').classList.add('active');
        }

        function closeProfile() {
            const index = openedPages.indexOf('profile');
            if (index > -1) {
                openedPages.splice(index, 1);
            }
            showPage(openedPages[openedPages.length - 1]);
        }

        // 标签导航
        function updateTabs() {
            const tabNav = document.getElementById('tabNav');
            tabNav.innerHTML = '';
            
            openedPages.forEach(pageId => {
                const tab = document.createElement('div');
                tab.className = `tab-item ${currentPage === pageId ? 'active' : ''}`;
                tab.innerHTML = `
                    <span>${getPageName(pageId)}</span>
                    ${pageId !== 'home' ? `<i class="fa fa-times tab-close" onclick="event.stopPropagation();closeTab('${pageId}')"></i>` : ''}
                `;
                tab.onclick = () => showPage(pageId);
                tabNav.appendChild(tab);
            });
        }

        function closeTab(pageId) {
            if (pageId === 'home') return;
            
            const index = openedPages.indexOf(pageId);
            if (index > -1) {
                openedPages.splice(index, 1);
                
                if (currentPage === pageId) {
                    currentPage = openedPages[openedPages.length - 1];
                    showPage(currentPage);
                }
                
                if (pageId === 'profile') {
                    document.getElementById('page-profile').classList.remove('active');
                }
                
                updateTabs();
            }
        }

        // 通知功能
        function toggleNotificationPanel() {
            const panel = document.getElementById('notificationPanel');
            panel.classList.toggle('active');
            
            // 标记所有通知为已读
            if (panel.classList.contains('active')) {
                const unreadCount = notifications.filter(n => !n.read).length;
                if (unreadCount > 0) {
                    notifications.forEach(n => n.read = true);
                    document.getElementById('notificationCount').textContent = '0';
                    document.querySelectorAll('.notification-item.unread').forEach(item => {
                        item.classList.remove('unread');
                    });
                }
            }
        }

        // 初始化消息滚动
        function initMessageScroll() {
            const messageList = document.querySelector('.message-list');
            const items = messageList.children;
            
            // 初始位置设置
            messageList.style.transform = 'translateY(0)';
            let currentIndex = 0;
            
            setInterval(() => {
                currentIndex = (currentIndex + 1) % items.length;
                messageList.style.transform = `translateY(-${currentIndex * 32}px)`;
            }, 3000);
        }

        // 辅助函数
        function getPageName(pageId) {
            const pageNames = {
                home: '首页',
                profile: '个人信息',
                system: '系统管理',
                casepool: '案池管理',
                tasklist: '作业清单',
                repayment: '还款管理',
                statistics: '数据统计',
                prediction: '任务预测',
                orderchange: '换单记录',
                orderrequest: '索单管理'
            };
            return pageNames[pageId] || pageId;
        }

        function updateProfile(event) {
            event.preventDefault();
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword && newPassword !== confirmPassword) {
                alert('两次输入的密码不一致！');
                return;
            }

            alert('信息更新成功');
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        }

        function updateGreeting() {
            const hours = new Date().getHours();
            let greeting = '夜深了';
            if (hours < 6) greeting = '凌晨好';
            else if (hours < 9) greeting = '早上好';
            else if (hours < 12) greeting = '上午好';
            else if (hours < 14) greeting = '中午好';
            else if (hours < 18) greeting = '下午好';
            else if (hours < 22) greeting = '晚上好';
            document.getElementById('greetingText').textContent = greeting;
        }

        // 初始化
        window.addEventListener('DOMContentLoaded', () => {
            updateGreeting();
            initMessageScroll();
            updateTabs();
        });
    </script>
</body>
</html>