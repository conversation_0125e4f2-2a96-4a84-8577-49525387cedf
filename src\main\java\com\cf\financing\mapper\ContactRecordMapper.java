package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.ContactRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 联系记录Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface ContactRecordMapper extends BaseMapper<ContactRecord> {

    /**
 * 分页查询联系记录
 *
 * @param page 分页参数
 * @param caseId 案件ID
 * @param customerName 客户姓名
 * @param contactType 联系类型
 * @param contactResult 联系结果
 * @param operatorId 操作员ID
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @return 联系记录分页数据
 */
    IPage<ContactRecord> selectContactRecordPage(
            Page<ContactRecord> page,
            @Param("caseId") Long caseId,
            @Param("customerName") String customerName,
            @Param("contactType") String contactType,
            @Param("contactResult") String contactResult,
            @Param("operatorId") Long operatorId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
 * 根据案件ID查询联系记录
 *
 * @param caseId 案件ID
 * @return 联系记录列表
 */
    List<ContactRecord> selectContactRecordsByCaseId(@Param("caseId") Long caseId);

    /**
 * 根据客户ID查询联系记录
 *
 * @param customerId 客户ID
 * @return 联系记录列表
 */
    List<ContactRecord> selectContactRecordsByCustomerId(@Param("customerId") Long customerId);

    /**
 * 根据操作员ID查询联系记录
 *
 * @param operatorId 操作员ID
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @return 联系记录列表
 */
    List<ContactRecord> selectContactRecordsByOperatorId(
            @Param("operatorId") Long operatorId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
 * 获取案件的最后联系记录
 *
 * @param caseId 案件ID
 * @return 最后联系记录
 */
    ContactRecord selectLastContactRecordByCaseId(@Param("caseId") Long caseId);

    /**
 * 获取案件的联系次数
 *
 * @param caseId 案件ID
 * @return 联系次数
 */
    int selectContactCountByCaseId(@Param("caseId") Long caseId);

    /**
 * 获取客户的联系次数
 *
 * @param customerId 客户ID
 * @return 联系次数
 */
    int selectContactCountByCustomerId(@Param("customerId") Long customerId);

    /**
 * 获取联系统计信息
 *
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param operatorId 操作员ID（可选）
 * @return 统计信息
 */
    Map<String, Object> selectContactStatistics(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("operatorId") Long operatorId
    );

    /**
     * 获取今日联系统计
     *
     * @return 今日统计数据
     */
    Map<String, Object> selectTodayContactStatistics();

    /**
 * 获取联系结果统计
 *
 * @param operatorId 操作员ID（可选）
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @return 联系结果统计
 */
    List<Map<String, Object>> selectContactResultStats(
            @Param("operatorId") Long operatorId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
 * 获取联系类型统计
 *
 * @param operatorId 操作员ID（可选）
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @return 联系类型统计
 */
    List<Map<String, Object>> selectContactTypeStats(
            @Param("operatorId") Long operatorId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
 * 获取联系趋势数据
 *
 * @param operatorId 操作员ID（可选）
 * @param days 天数
 * @return 趋势数据
 */
    List<Map<String, Object>> selectContactTrendData(
            @Param("operatorId") Long operatorId,
            @Param("days") Integer days
    );

    /**
 * 获取今日联系记录
 *
 * @param operatorId 操作员ID（可选）
 * @param contactDate 联系日期
 * @return 今日联系记录
 */
    List<ContactRecord> selectTodayContactRecords(
            @Param("operatorId") Long operatorId,
            @Param("contactDate") LocalDate contactDate
    );

    /**
 * 获取有承诺还款的联系记录
 *
 * @param operatorId 操作员ID（可选）
 * @param promiseStartDate 承诺开始日期
 * @param promiseEndDate 承诺结束日期
 * @return 承诺还款记录
 */
    List<ContactRecord> selectPromiseRepaymentRecords(
            @Param("operatorId") Long operatorId,
            @Param("promiseStartDate") LocalDate promiseStartDate,
            @Param("promiseEndDate") LocalDate promiseEndDate
    );

    /**
 * 获取操作员联系业绩排行
 *
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param limit 限制数量
 * @return 业绩排行
 */
    List<Map<String, Object>> selectOperatorContactRanking(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("limit") Integer limit
    );

    /**
 * 获取联系效果分析
 *
 * @param operatorId 操作员ID（可选）
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @return 联系效果分析
 */
    Map<String, Object> selectContactEffectAnalysis(
            @Param("operatorId") Long operatorId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
 * 获取月度联系统计
 *
 * @param months 月份数
 * @return 月度统计数据
 */
    List<Map<String, Object>> selectMonthlyContactStats(@Param("months") Integer months);

    /**
 * 批量删除联系记录
 *
 * @param recordIds 记录ID列表
 * @return 删除结果
 */
    int batchDeleteContactRecords(@Param("recordIds") List<Long> recordIds);

    /**
 * 根据案件ID删除联系记录
 *
 * @param caseId 案件ID
 * @return 删除结果
 */
    int deleteContactRecordsByCaseId(@Param("caseId") Long caseId);

    /**
 * 获取需要跟进的联系记录（有承诺还款日期的）
 *
 * @param operatorId 操作员ID（可选）
 * @param followUpDate 跟进日期
 * @return 需要跟进的记录
 */
    List<ContactRecord> selectFollowUpRecords(
            @Param("operatorId") Long operatorId,
            @Param("followUpDate") LocalDate followUpDate
    );

    /**
     * 获取联系结果统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果统计数据
     */
    List<Map<String, Object>> selectContactResultStatistics(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    /**
     * 获取联系类型统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 类型统计数据
     */
    List<Map<String, Object>> selectContactTypeStatistics(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    /**
     * 获取联系趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> selectContactTrend(@Param("days") Integer days);

    /**
     * 获取月度联系统计
     *
     * @param months 月份数
     * @return 月度统计数据
     */
    List<Map<String, Object>> selectMonthlyContactStatistics(@Param("months") Integer months);

    /**
     * 批量删除联系记录（按条件）
     *
     * @param caseId 案件ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 删除数量
     */
    int batchDeleteContactsByCondition(
            @Param("caseId") Long caseId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    /**
     * 获取跟进记录
     *
     * @param caseId 案件ID
     * @param days 天数
     * @return 跟进记录列表
     */
    List<ContactRecord> selectFollowUpRecords(
            @Param("caseId") Long caseId,
            @Param("days") Integer days
    );
}