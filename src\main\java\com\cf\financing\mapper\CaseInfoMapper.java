package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.CaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 案件信息 Mapper 接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface CaseInfoMapper extends BaseMapper<CaseInfo> {

    /**
     * 分页查询案件信息（包含客户信息）
     *
     * @param page 分页参数
     * @param caseNo 案件编号
     * @param customerName 客户姓名
     * @param customerPhone 客户手机号
     * @param caseStatus 案件状态
     * @param caseLevel 案件等级
     * @param assignUserId 分配催收员ID
     * @param overdueStartDate 逾期开始日期
     * @param overdueEndDate 逾期结束日期
     * @param minOverdueAmount 最小逾期金额
     * @param maxOverdueAmount 最大逾期金额
     * @return 案件信息分页结果
     */
    IPage<CaseInfo> selectCaseInfoPage(
            Page<CaseInfo> page,
            @Param("caseNo") String caseNo,
            @Param("customerName") String customerName,
            @Param("customerPhone") String customerPhone,
            @Param("caseStatus") String caseStatus,
            @Param("caseLevel") String caseLevel,
            @Param("assignUserId") Long assignUserId,
            @Param("overdueStartDate") LocalDate overdueStartDate,
            @Param("overdueEndDate") LocalDate overdueEndDate,
            @Param("minOverdueAmount") BigDecimal minOverdueAmount,
            @Param("maxOverdueAmount") BigDecimal maxOverdueAmount
    );

    /**
     * 根据ID查询案件详细信息（包含客户信息）
     *
     * @param id 案件ID
     * @return 案件详细信息
     */
    CaseInfo selectCaseInfoById(@Param("id") Long id);

    /**
     * 查询我的案件列表
     *
     * @param page 分页参数
     * @param assignUserId 分配催收员ID
     * @param caseStatus 案件状态
     * @param caseLevel 案件等级
     * @return 案件信息分页结果
     */
    IPage<CaseInfo> selectMyCaseList(
            Page<CaseInfo> page,
            @Param("assignUserId") Long assignUserId,
            @Param("caseStatus") String caseStatus,
            @Param("caseLevel") String caseLevel
    );

    /**
     * 统计案件数据
     *
     * @param assignUserId 催收员ID（为空则统计全部）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    Map<String, Object> selectCaseStatistics(
            @Param("assignUserId") Long assignUserId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    /**
     * 查询逾期案件统计（按等级分组）
     *
     * @param assignUserId 催收员ID（为空则统计全部）
     * @return 逾期案件统计
     */
    List<Map<String, Object>> selectOverdueCaseStatsByLevel(@Param("assignUserId") Long assignUserId);

    /**
     * 查询案件趋势数据（按日期分组）
     *
     * @param assignUserId 催收员ID（为空则统计全部）
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> selectCaseTrendData(
            @Param("assignUserId") Long assignUserId,
            @Param("days") Integer days
    );

    /**
     * 批量分配案件
     *
     * @param caseIds 案件ID列表
     * @param assignUserId 分配催收员ID
     * @return 更新记录数
     */
    int batchAssignCases(
            @Param("caseIds") List<Long> caseIds,
            @Param("assignUserId") Long assignUserId
    );

    /**
     * 批量更新案件状态
     *
     * @param caseIds 案件ID列表
     * @param caseStatus 案件状态
     * @return 更新记录数
     */
    int batchUpdateCaseStatus(
            @Param("caseIds") List<Long> caseIds,
            @Param("caseStatus") String caseStatus
    );

    /**
     * 查询待处理案件数量
     *
     * @param assignUserId 催收员ID
     * @return 待处理案件数量
     */
    int selectPendingCaseCount(@Param("assignUserId") Long assignUserId);

    /**
     * 查询今日到期案件
     *
     * @param assignUserId 催收员ID
     * @param targetDate 目标日期
     * @return 到期案件列表
     */
    List<CaseInfo> selectTodayDueCases(
            @Param("assignUserId") Long assignUserId,
            @Param("targetDate") LocalDate targetDate
    );

    /**
     * 获取案件状态分布
     *
     * @return 状态分布数据
     */
    List<Map<String, Object>> selectCaseStatusDistribution();

    /**
     * 获取每日案件统计
     *
     * @param days 天数
     * @param assignUserId 指派用户ID
     * @return 每日统计数据
     */
    List<Map<String, Object>> selectDailyCaseStats(
            @Param("days") Integer days,
            @Param("assignUserId") Long assignUserId
    );

    /**
     * 获取月度案件统计
     *
     * @param months 月份数
     * @param assignUserId 指派用户ID
     * @return 月度统计数据
     */
    List<Map<String, Object>> selectMonthlyCaseStats(
            @Param("months") Integer months,
            @Param("assignUserId") Long assignUserId
    );
}