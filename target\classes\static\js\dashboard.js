// Dashboard页面JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    // 初始化图表
    initTrendChart();
    initTeamChart();
    
    // 初始化进度条动画
    initProgressAnimations();
    
    // 绑定事件
    bindEvents();
});

// 回收趋势折线图
function initTrendChart() {
    const trendCtx = document.getElementById('trendChart');
    if (!trendCtx) return;
    
    const trendChart = new Chart(trendCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: ['12/08', '12/09', '12/10', '12/11', '12/12', '12/13', '12/14'],
            datasets: [{
                label: '回收金额 (万元)',
                data: [32, 45, 28, 51, 42, 39, 48],
                borderColor: '#f6b93b',
                backgroundColor: 'rgba(246, 185, 59, 0.1)',
                borderWidth: 3,
                pointBackgroundColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                fill: true,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 10
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// 团队绩效对比图
function initTeamChart() {
    const teamCtx = document.getElementById('teamChart');
    if (!teamCtx) return;
    
    const teamChart = new Chart(teamCtx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['催收一组', '催收二组', '催收三组', '催收四组'],
            datasets: [{
                label: '回收金额 (万元)',
                data: [186, 152, 124, 98],
                backgroundColor: [
                    'rgba(59, 130, 246, 0.7)',
                    'rgba(16, 185, 129, 0.7)',
                    'rgba(245, 158, 11, 0.7)',
                    'rgba(156, 163, 175, 0.7)'
                ],
                borderColor: [
                    'rgba(59, 130, 246, 1)',
                    'rgba(16, 185, 129, 1)',
                    'rgba(245, 158, 11, 1)',
                    'rgba(156, 163, 175, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 10
                        }
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// 进度条动画效果
function initProgressAnimations() {
    const progressBars = document.querySelectorAll('.progress, .progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
}

// 绑定事件
function bindEvents() {
    // 快速操作按钮事件
    const actionButtons = document.querySelectorAll('.action-btn-top');
    actionButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.querySelector('span').textContent;
            handleQuickAction(action);
        });
    });
    
    // 面板切换按钮事件
    const panelButtons = document.querySelectorAll('.btn');
    panelButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除同级按钮的active类
            const siblings = this.parentNode.querySelectorAll('.btn');
            siblings.forEach(sibling => sibling.classList.remove('active'));
            
            // 添加active类到当前按钮
            this.classList.add('active');
            
            // 处理面板切换逻辑
            handlePanelSwitch(this);
        });
    });
    
    // 下拉选择框事件
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            handleSelectChange(this);
        });
    });
}

// 处理快速操作
function handleQuickAction(action) {
    switch(action) {
        case '导出报表':
            console.log('导出报表功能');
            // 这里可以调用后端API导出报表
            break;
        case '设置提醒':
            console.log('设置提醒功能');
            // 这里可以打开提醒设置对话框
            break;
        case '设置':
            console.log('设置功能');
            // 这里可以跳转到设置页面
            break;
        case '刷新':
            console.log('刷新数据');
            // 这里可以刷新页面数据
            location.reload();
            break;
        default:
            console.log('未知操作:', action);
    }
}

// 处理面板切换
function handlePanelSwitch(button) {
    const buttonText = button.textContent;
    const panel = button.closest('.panel');
    const panelTitle = panel.querySelector('.panel-title').textContent;
    
    console.log(`面板 "${panelTitle}" 切换到 "${buttonText}"`);
    
    // 这里可以根据不同的面板和按钮执行相应的数据更新逻辑
    // 例如：更新图表数据、更新表格数据等
}

// 处理下拉选择框变化
function handleSelectChange(select) {
    const selectedValue = select.value;
    const panel = select.closest('.panel');
    const panelTitle = panel.querySelector('.panel-title').textContent;
    
    console.log(`面板 "${panelTitle}" 选择了 "${selectedValue}"`);
    
    // 这里可以根据选择的值更新相应的数据
    // 例如：更新时间范围、更新数据源等
}

// 工具函数：格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 工具函数：格式化货币
function formatCurrency(amount) {
    return '¥' + amount.toLocaleString();
}

// 工具函数：更新统计数据
function updateStats(stats) {
    if (stats.totalCases) {
        document.querySelector('.overview-card.warning .value').textContent = formatNumber(stats.totalCases);
    }
    if (stats.totalAmount) {
        document.querySelector('.overview-card.primary .value').textContent = formatCurrency(stats.totalAmount);
    }
    if (stats.todayRecovery) {
        document.querySelector('.overview-card.success .value').textContent = formatCurrency(stats.todayRecovery);
    }
    if (stats.avgRecoveryRate) {
        document.querySelector('.overview-card.info .value').textContent = stats.avgRecoveryRate + '%';
    }
}
