package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.ExchangeOrder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 换单记录服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface IExchangeOrderService extends IService<ExchangeOrder> {

    /**
     * 分页查询换单记录
     *
     * @param page 分页参数
     * @param exchangeNo 换单编号
     * @param originalCaseNo 原案件编号
     * @param newCaseNo 新案件编号
     * @param customerName 客户姓名
     * @param exchangeType 换单类型
     * @param exchangeStatus 换单状态
     * @param applicantId 申请人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<ExchangeOrder> getExchangeOrderPage(Page<ExchangeOrder> page,
                                             String exchangeNo,
                                             String originalCaseNo,
                                             String newCaseNo,
                                             String customerName,
                                             Integer exchangeType,
                                             Integer exchangeStatus,
                                             Long applicantId,
                                             LocalDateTime startTime,
                                             LocalDateTime endTime);

    /**
     * 创建换单申请
     *
     * @param exchangeOrder 换单信息
     * @return 是否成功
     */
    boolean createExchangeOrder(ExchangeOrder exchangeOrder);

    /**
     * 更新换单信息
     *
     * @param exchangeOrder 换单信息
     * @return 是否成功
     */
    boolean updateExchangeOrder(ExchangeOrder exchangeOrder);

    /**
     * 审核换单申请
     *
     * @param exchangeId 换单ID
     * @param exchangeStatus 审核结果（1-通过，2-拒绝）
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @param reviewComment 审核意见
     * @return 是否成功
     */
    boolean reviewExchangeOrder(Long exchangeId,
                               Integer exchangeStatus,
                               Long reviewerId,
                               String reviewerName,
                               String reviewComment);

    /**
     * 执行换单操作
     *
     * @param exchangeId 换单ID
     * @param executeBy 执行人
     * @return 是否成功
     */
    boolean executeExchangeOrder(Long exchangeId, String executeBy);

    /**
     * 取消换单申请
     *
     * @param exchangeId 换单ID
     * @param cancelReason 取消原因
     * @param cancelBy 取消人
     * @return 是否成功
     */
    boolean cancelExchangeOrder(Long exchangeId, String cancelReason, String cancelBy);

    /**
     * 根据客户ID查询换单记录
     *
     * @param customerId 客户ID
     * @return 换单记录列表
     */
    List<ExchangeOrder> getExchangeOrdersByCustomerId(Long customerId);

    /**
     * 根据案件ID查询相关换单记录
     *
     * @param caseId 案件ID
     * @return 换单记录列表
     */
    List<ExchangeOrder> getExchangeOrdersByCaseId(Long caseId);

    /**
     * 查询待审核的换单记录
     *
     * @param reviewerId 审核人ID（可选）
     * @return 待审核换单记录列表
     */
    List<ExchangeOrder> getPendingExchangeOrders(Long reviewerId);

    /**
     * 统计换单类型分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param applicantId 申请人ID（可选）
     * @return 类型统计结果
     */
    List<Map<String, Object>> getExchangeTypeStatistics(LocalDateTime startTime,
                                                        LocalDateTime endTime,
                                                        Long applicantId);

    /**
     * 统计换单状态分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param applicantId 申请人ID（可选）
     * @return 状态统计结果
     */
    List<Map<String, Object>> getExchangeStatusStatistics(LocalDateTime startTime,
                                                          LocalDateTime endTime,
                                                          Long applicantId);

    /**
     * 查询换单趋势数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组方式（day, week, month）
     * @return 趋势数据
     */
    List<Map<String, Object>> getExchangeTrendData(LocalDateTime startTime,
                                                   LocalDateTime endTime,
                                                   String groupBy);

    /**
     * 查询用户换单统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 用户统计结果
     */
    List<Map<String, Object>> getUserExchangeStatistics(LocalDateTime startTime,
                                                        LocalDateTime endTime,
                                                        Integer limit);

    /**
     * 查询换单金额统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param exchangeType 换单类型（可选）
     * @return 金额统计结果
     */
    Map<String, Object> getExchangeAmountStatistics(LocalDateTime startTime,
                                                    LocalDateTime endTime,
                                                    Integer exchangeType);

    /**
     * 批量审核换单申请
     *
     * @param exchangeIds 换单ID列表
     * @param exchangeStatus 审核结果
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @param reviewComment 审核意见
     * @return 是否成功
     */
    boolean batchReviewExchangeOrders(List<Long> exchangeIds,
                                     Integer exchangeStatus,
                                     Long reviewerId,
                                     String reviewerName,
                                     String reviewComment);

    /**
     * 检查案件是否可以换单
     *
     * @param caseId 案件ID
     * @return 是否可以换单
     */
    boolean canExchangeCase(Long caseId);

    /**
     * 生成换单编号
     *
     * @return 换单编号
     */
    String generateExchangeNo();

    /**
     * 导出换单记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param exchangeStatus 换单状态（可选）
     * @param applicantId 申请人ID（可选）
     * @return 导出文件路径
     */
    String exportExchangeOrders(LocalDateTime startTime,
                               LocalDateTime endTime,
                               Integer exchangeStatus,
                               Long applicantId);
}