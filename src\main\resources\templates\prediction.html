<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务预测</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .stat-card.primary {
            border-left-color: #667eea;
        }
        
        .stat-card.success {
            border-left-color: #28a745;
        }
        
        .stat-card.warning {
            border-left-color: #ffc107;
        }
        
        .stat-card.danger {
            border-left-color: #dc3545;
        }
        
        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .stat-card.primary .stat-icon {
            color: #667eea;
        }
        
        .stat-card.success .stat-icon {
            color: #28a745;
        }
        
        .stat-card.warning .stat-icon {
            color: #ffc107;
        }
        
        .stat-card.danger .stat-icon {
            color: #dc3545;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #7f8c8d;
            font-weight: 500;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }
        
        .prediction-table {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .table-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .trend-up {
            color: #28a745;
        }
        
        .trend-down {
            color: #dc3545;
        }
        
        .trend-stable {
            color: #ffc107;
        }
        
        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
        }
        
        .filter-input {
            padding: 10px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .filter-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .filters {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> 任务预测分析</h1>
            <p>基于历史数据和机器学习算法的智能任务预测系统</p>
        </div>
        
        <div class="content">
            <!-- 筛选器 -->
            <div class="filters">
                <div class="filter-group">
                    <label class="filter-label">预测周期</label>
                    <select class="filter-input">
                        <option value="7">未来7天</option>
                        <option value="14">未来14天</option>
                        <option value="30" selected>未来30天</option>
                        <option value="90">未来90天</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">任务类型</label>
                    <select class="filter-input">
                        <option value="all">全部任务</option>
                        <option value="collection">催收任务</option>
                        <option value="negotiation">协商任务</option>
                        <option value="legal">法务任务</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">作业员</label>
                    <select class="filter-input">
                        <option value="all">全部作业员</option>
                        <option value="zhang">张三</option>
                        <option value="li">李四</option>
                        <option value="wang">王五</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">&nbsp;</label>
                    <button class="btn btn-primary">
                        <i class="fas fa-search"></i> 更新预测
                    </button>
                </div>
            </div>
            
            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-number">1,247</div>
                    <div class="stat-label">预测总任务数</div>
                </div>
                
                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-number">892</div>
                    <div class="stat-label">预计完成任务</div>
                </div>
                
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number">245</div>
                    <div class="stat-label">预计延期任务</div>
                </div>
                
                <div class="stat-card danger">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-number">110</div>
                    <div class="stat-label">高风险任务</div>
                </div>
            </div>
            
            <!-- 图表区域 -->
            <div class="charts-section">
                <div class="chart-container">
                    <div class="chart-title">任务完成趋势预测</div>
                    <canvas id="trendChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">任务类型分布预测</div>
                    <canvas id="distributionChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <!-- 预测详情表格 -->
            <div class="prediction-table">
                <div class="table-title">详细预测数据</div>
                <table>
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>预测任务数</th>
                            <th>预计完成率</th>
                            <th>风险等级</th>
                            <th>趋势</th>
                            <th>建议操作</th>
                        </tr>
                    </thead>
                    <tbody id="predictionTableBody">
                        <!-- 数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 生成模拟预测数据
        function generatePredictionData() {
            const data = [];
            const today = new Date();
            
            for (let i = 1; i <= 30; i++) {
                const date = new Date(today);
                date.setDate(today.getDate() + i);
                
                const taskCount = Math.floor(Math.random() * 50) + 30;
                const completionRate = Math.floor(Math.random() * 30) + 70;
                const riskLevel = completionRate > 85 ? '低' : completionRate > 70 ? '中' : '高';
                const trend = Math.random() > 0.5 ? 'up' : Math.random() > 0.3 ? 'stable' : 'down';
                
                data.push({
                    date: date.toLocaleDateString('zh-CN'),
                    taskCount,
                    completionRate,
                    riskLevel,
                    trend,
                    suggestion: riskLevel === '高' ? '增加人员配置' : riskLevel === '中' ? '密切关注' : '保持现状'
                });
            }
            
            return data;
        }
        
        // 渲染预测表格
        function renderPredictionTable() {
            const data = generatePredictionData();
            const tbody = document.getElementById('predictionTableBody');
            
            tbody.innerHTML = data.map(item => `
                <tr>
                    <td>${item.date}</td>
                    <td>${item.taskCount}</td>
                    <td>${item.completionRate}%</td>
                    <td>
                        <span class="trend-${item.riskLevel === '低' ? 'up' : item.riskLevel === '中' ? 'stable' : 'down'}">
                            ${item.riskLevel}
                        </span>
                    </td>
                    <td>
                        <i class="fas fa-arrow-${item.trend === 'up' ? 'up trend-up' : item.trend === 'down' ? 'down trend-down' : 'right trend-stable'}"></i>
                    </td>
                    <td>${item.suggestion}</td>
                </tr>
            `).join('');
        }
        
        // 初始化趋势图表
        function initTrendChart() {
            const ctx = document.getElementById('trendChart').getContext('2d');
            const data = generatePredictionData().slice(0, 14);
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(item => item.date.slice(5)),
                    datasets: [{
                        label: '预测任务数',
                        data: data.map(item => item.taskCount),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '预计完成率',
                        data: data.map(item => item.completionRate),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // 初始化分布图表
        function initDistributionChart() {
            const ctx = document.getElementById('distributionChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['催收任务', '协商任务', '法务任务', '其他任务'],
                    datasets: [{
                        data: [45, 25, 20, 10],
                        backgroundColor: [
                            '#667eea',
                            '#28a745',
                            '#ffc107',
                            '#dc3545'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderPredictionTable();
            initTrendChart();
            initDistributionChart();
        });
    </script>
</body>
</html>
