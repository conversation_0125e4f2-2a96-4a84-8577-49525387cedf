// 案池页面JavaScript功能

// 全局变量
let currentPage = 1;
let pageSize = 20;
let totalRecords = 0;
let totalPages = 1;
let currentFilters = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadCaseData();
    setupEventListeners();
});

// 初始化页面
function initializePage() {
    // 初始化日期选择器
    flatpickr("#delegateDate", {
        dateFormat: "Y-m-d",
        locale: "zh"
    });
    
    flatpickr("#dueDate", {
        dateFormat: "Y-m-d",
        locale: "zh"
    });
    
    // 设置默认页面大小
    document.getElementById('pageSize').value = pageSize;
}

// 设置事件监听器
function setupEventListeners() {
    // 全选复选框
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('#caseTableBody input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // 回车键搜索
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchCases();
        }
    });
}

// 加载案件数据
function loadCaseData() {
    showLoading();
    
    const params = new URLSearchParams({
        page: currentPage,
        size: pageSize,
        ...currentFilters
    });
    
    fetch(`/api/case-info/page?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                renderCaseTable(data.data.records);
                updatePagination(data.data);
                updateStatistics(data.data);
            } else {
                showError('加载数据失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('加载数据失败:', error);
            showError('网络错误，请稍后重试');
        })
        .finally(() => {
            hideLoading();
        });
}

// 渲染案件表格
function renderCaseTable(cases) {
    const tbody = document.getElementById('caseTableBody');
    tbody.innerHTML = '';
    
    if (!cases || cases.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="14" style="text-align: center; padding: 40px; color: #999;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                    暂无数据
                </td>
            </tr>
        `;
        return;
    }
    
    cases.forEach(caseItem => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="checkbox" value="${caseItem.id}"></td>
            <td>${caseItem.caseNo || '-'}</td>
            <td>${caseItem.customerName || '-'}</td>
            <td>${caseItem.idCard || '-'}</td>
            <td>${caseItem.phone || '-'}</td>
            <td class="amount">¥${formatAmount(caseItem.delegateAmount)}</td>
            <td class="amount">¥${formatAmount(caseItem.overdueAmount)}</td>
            <td><span class="status-tag ${getStatusClass(caseItem.status)}">${getStatusText(caseItem.status)}</span></td>
            <td>${caseItem.delegator || '-'}</td>
            <td>${caseItem.assigneeName || '-'}</td>
            <td>${formatDate(caseItem.delegateTime)}</td>
            <td>${formatDate(caseItem.dueTime)}</td>
            <td>${formatDate(caseItem.lastContactTime)}</td>
            <td>
                <button class="data-action-btn" onclick="viewCase(${caseItem.id})" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="data-action-btn" onclick="editCase(${caseItem.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="data-action-btn" onclick="assignCase(${caseItem.id})" title="分配">
                    <i class="fas fa-user-plus"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 更新分页信息
function updatePagination(pageData) {
    totalRecords = pageData.total;
    totalPages = pageData.pages;
    currentPage = pageData.current;
    
    // 更新分页显示
    document.getElementById('totalRecords').textContent = totalRecords;
    document.getElementById('totalPages').textContent = totalPages;
    document.getElementById('currentPageInput').value = currentPage;
    
    const startRecord = (currentPage - 1) * pageSize + 1;
    const endRecord = Math.min(currentPage * pageSize, totalRecords);
    document.getElementById('startRecord').textContent = startRecord;
    document.getElementById('endRecord').textContent = endRecord;
    
    // 更新按钮状态
    document.getElementById('prevPage').disabled = currentPage <= 1;
    document.getElementById('nextPage').disabled = currentPage >= totalPages;
}

// 更新统计信息
function updateStatistics(pageData) {
    document.getElementById('totalCases').textContent = pageData.total || 0;
    
    // 计算总金额
    let totalAmount = 0;
    if (pageData.records) {
        totalAmount = pageData.records.reduce((sum, item) => sum + (item.delegateAmount || 0), 0);
    }
    document.getElementById('totalAmount').textContent = '¥' + formatAmount(totalAmount);
    
    // 今日到期案件数（这里需要单独的API调用）
    loadTodayDueCount();
}

// 加载今日到期案件数
function loadTodayDueCount() {
    fetch('/api/case-info/today-due-count')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                document.getElementById('todayDue').textContent = data.data || 0;
            }
        })
        .catch(error => {
            console.error('加载今日到期数据失败:', error);
        });
}

// 搜索案件
function searchCases() {
    currentFilters = {
        caseNo: document.getElementById('caseNo').value.trim(),
        customerName: document.getElementById('customerName').value.trim(),
        idCard: document.getElementById('idCard').value.trim(),
        phone: document.getElementById('phone').value.trim(),
        status: document.getElementById('caseStatus').value,
        delegator: document.getElementById('delegator').value,
        assigneeId: document.getElementById('assignee').value,
        delegateDate: document.getElementById('delegateDate').value,
        dueDate: document.getElementById('dueDate').value,
        amountMin: document.getElementById('amountMin').value,
        amountMax: document.getElementById('amountMax').value
    };
    
    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });
    
    currentPage = 1;
    loadCaseData();
}

// 重置筛选条件
function resetFilters() {
    document.getElementById('caseNo').value = '';
    document.getElementById('customerName').value = '';
    document.getElementById('idCard').value = '';
    document.getElementById('phone').value = '';
    document.getElementById('caseStatus').value = '';
    document.getElementById('delegator').value = '';
    document.getElementById('assignee').value = '';
    document.getElementById('delegateDate').value = '';
    document.getElementById('dueDate').value = '';
    document.getElementById('amountMin').value = '';
    document.getElementById('amountMax').value = '';
    
    currentFilters = {};
    currentPage = 1;
    loadCaseData();
}

// 分页相关函数
function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSize').value);
    currentPage = 1;
    loadCaseData();
}

function prevPage() {
    if (currentPage > 1) {
        currentPage--;
        loadCaseData();
    }
}

function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        loadCaseData();
    }
}

function goToPage() {
    const page = parseInt(document.getElementById('currentPageInput').value);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        loadCaseData();
    } else {
        document.getElementById('currentPageInput').value = currentPage;
    }
}

// 功能按钮
function exportData() {
    const params = new URLSearchParams(currentFilters);
    window.open(`/api/case-info/export?${params}`, '_blank');
}

function batchAssign() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showError('请选择要分配的案件');
        return;
    }
    
    // 这里应该打开分配对话框
    alert('批量分配功能开发中...');
}

function refreshData() {
    loadCaseData();
}

function toggleColumns() {
    alert('列设置功能开发中...');
}

function fullScreen() {
    if (document.fullscreenElement) {
        document.exitFullscreen();
    } else {
        document.documentElement.requestFullscreen();
    }
}

// 案件操作
function viewCase(caseId) {
    window.open(`/case-detail/${caseId}`, '_blank');
}

function editCase(caseId) {
    window.open(`/case-edit/${caseId}`, '_blank');
}

function assignCase(caseId) {
    alert('分配功能开发中...');
}

// 工具函数
function getSelectedIds() {
    const checkboxes = document.querySelectorAll('#caseTableBody input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function formatAmount(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
}

function getStatusClass(status) {
    const statusMap = {
        'pending': 'status-pending',
        'processing': 'status-processing',
        'completed': 'status-completed',
        'overdue': 'status-overdue'
    };
    return statusMap[status] || 'status-pending';
}

function getStatusText(status) {
    const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'overdue': '逾期'
    };
    return statusMap[status] || '未知';
}

function showLoading() {
    // 显示加载指示器
    const tbody = document.getElementById('caseTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="14" style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-right: 8px;"></i>
                加载中...
            </td>
        </tr>
    `;
}

function hideLoading() {
    // 加载完成后会被数据替换，无需特殊处理
}

function showError(message) {
    alert('错误: ' + message);
}
