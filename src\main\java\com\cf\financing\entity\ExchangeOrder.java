package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 换单记录实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exchange_order")
public class ExchangeOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 换单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 换单编号
     */
    @TableField("exchange_no")
    private String exchangeNo;

    /**
     * 原案件ID
     */
    @TableField("original_case_id")
    private Long originalCaseId;

    /**
     * 原案件编号
     */
    @TableField("original_case_no")
    private String originalCaseNo;

    /**
     * 新案件ID
     */
    @TableField("new_case_id")
    private Long newCaseId;

    /**
     * 新案件编号
     */
    @TableField("new_case_no")
    private String newCaseNo;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private Long customerId;

    /**
     * 客户姓名
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 换单类型（1-产品变更，2-金额调整，3-期限调整，4-其他）
     */
    @TableField("exchange_type")
    private Integer exchangeType;

    /**
     * 换单原因
     */
    @TableField("exchange_reason")
    private String exchangeReason;

    /**
     * 原产品名称
     */
    @TableField("original_product_name")
    private String originalProductName;

    /**
     * 新产品名称
     */
    @TableField("new_product_name")
    private String newProductName;

    /**
     * 原金额
     */
    @TableField("original_amount")
    private BigDecimal originalAmount;

    /**
     * 新金额
     */
    @TableField("new_amount")
    private BigDecimal newAmount;

    /**
     * 金额差异
     */
    @TableField("amount_difference")
    private BigDecimal amountDifference;

    /**
     * 换单状态（0-待审核，1-审核通过，2-审核拒绝，3-已执行）
     */
    @TableField("exchange_status")
    private Integer exchangeStatus;

    /**
     * 申请人ID
     */
    @TableField("applicant_id")
    private Long applicantId;

    /**
     * 申请人姓名
     */
    @TableField("applicant_name")
    private String applicantName;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 审核人ID
     */
    @TableField("reviewer_id")
    private Long reviewerId;

    /**
     * 审核人姓名
     */
    @TableField("reviewer_name")
    private String reviewerName;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 审核意见
     */
    @TableField("review_comment")
    private String reviewComment;

    /**
     * 执行时间
     */
    @TableField("execute_time")
    private LocalDateTime executeTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;
}