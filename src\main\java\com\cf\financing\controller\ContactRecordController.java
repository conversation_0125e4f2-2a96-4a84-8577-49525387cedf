package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.ContactRecord;
import com.cf.financing.service.IContactRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 联系记录控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Api(tags = "联系记录管理")
@RestController
@RequestMapping("/api/contact-record")
@RequiredArgsConstructor
public class ContactRecordController {

    private final IContactRecordService contactRecordService;

    /**
     * 分页查询联系记录列表
     */
    @ApiOperation("分页查询联系记录列表")
    @GetMapping("/page")
    public Map<String, Object> getContactRecordPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("案件ID") @RequestParam(required = false) Long caseId,
            @ApiParam("客户ID") @RequestParam(required = false) Long customerId,
            @ApiParam("操作员ID") @RequestParam(required = false) Long operatorId,
            @ApiParam("联系方式") @RequestParam(required = false) String contactType,
            @ApiParam("联系结果") @RequestParam(required = false) String contactResult) {
        
        Page<ContactRecord> page = new Page<>(current, size);
        IPage<ContactRecord> result = contactRecordService.getContactRecordPage(
                page, caseId, customerId, operatorId, contactType, contactResult
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 根据案件ID查询联系记录
     */
    @ApiOperation("根据案件ID查询联系记录")
    @GetMapping("/case/{caseId}")
    public Map<String, Object> getContactRecordsByCaseId(
            @ApiParam("案件ID") @PathVariable Long caseId) {
        
        List<ContactRecord> records = contactRecordService.getContactRecordsByCaseId(caseId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 根据客户ID查询联系记录
     */
    @ApiOperation("根据客户ID查询联系记录")
    @GetMapping("/customer/{customerId}")
    public Map<String, Object> getContactRecordsByCustomerId(
            @ApiParam("客户ID") @PathVariable Long customerId) {
        
        List<ContactRecord> records = contactRecordService.getContactRecordsByCustomerId(customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 根据操作员ID查询联系记录
     */
    @ApiOperation("根据操作员ID查询联系记录")
    @GetMapping("/operator/{operatorId}")
    public Map<String, Object> getContactRecordsByOperatorId(
            @ApiParam("操作员ID") @PathVariable Long operatorId) {
        
        List<ContactRecord> records = contactRecordService.getContactRecordsByOperatorId(operatorId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 获取最后一次联系记录
     */
    @ApiOperation("获取最后一次联系记录")
    @GetMapping("/last/{caseId}")
    public Map<String, Object> getLastContactRecord(
            @ApiParam("案件ID") @PathVariable Long caseId) {
        
        ContactRecord record = contactRecordService.getLastContactRecord(caseId);
        
        Map<String, Object> response = new HashMap<>();
        if (record != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", record);
        } else {
            response.put("code", 404);
            response.put("message", "暂无联系记录");
        }
        return response;
    }

    /**
     * 创建联系记录
     */
    @ApiOperation("创建联系记录")
    @PostMapping
    public Map<String, Object> createContactRecord(@RequestBody ContactRecord record) {
        boolean success = contactRecordService.createContactRecord(record);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "创建成功");
            response.put("data", record);
        } else {
            response.put("code", 400);
            response.put("message", "创建失败");
        }
        return response;
    }

    /**
     * 更新联系记录
     */
    @ApiOperation("更新联系记录")
    @PutMapping("/{recordId}")
    public Map<String, Object> updateContactRecord(
            @ApiParam("记录ID") @PathVariable Long recordId,
            @RequestBody ContactRecord record) {
        
        record.setId(recordId);
        boolean success = contactRecordService.updateContactRecord(record);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "更新失败");
        }
        return response;
    }

    /**
     * 删除联系记录
     */
    @ApiOperation("删除联系记录")
    @DeleteMapping("/{recordId}")
    public Map<String, Object> deleteContactRecord(
            @ApiParam("记录ID") @PathVariable Long recordId) {
        
        boolean success = contactRecordService.deleteContactRecord(recordId);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "删除失败");
        }
        return response;
    }

    /**
     * 批量删除联系记录
     */
    @ApiOperation("批量删除联系记录")
    @DeleteMapping("/batch")
    public Map<String, Object> batchDeleteContactRecords(@RequestBody List<Long> recordIds) {
        boolean success = contactRecordService.batchDeleteContactRecords(recordIds);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量删除失败");
        }
        return response;
    }

    /**
     * 获取联系次数统计
     */
    @ApiOperation("获取联系次数统计")
    @GetMapping("/count/case/{caseId}")
    public Map<String, Object> getContactCountByCaseId(
            @ApiParam("案件ID") @PathVariable Long caseId) {
        
        Integer count = contactRecordService.getContactCountByCaseId(caseId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", count);
        return response;
    }

    /**
     * 获取客户联系次数统计
     */
    @ApiOperation("获取客户联系次数统计")
    @GetMapping("/count/customer/{customerId}")
    public Map<String, Object> getContactCountByCustomerId(
            @ApiParam("客户ID") @PathVariable Long customerId) {
        
        Integer count = contactRecordService.getContactCountByCustomerId(customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", count);
        return response;
    }

    /**
     * 获取联系统计信息
     */
    @ApiOperation("获取联系统计信息")
    @GetMapping("/statistics")
    public Map<String, Object> getContactStatistics(
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @ApiParam("操作员ID") @RequestParam(required = false) Long operatorId) {
        Map<String, Object> statistics = contactRecordService.getContactStatistics(startDate, endDate, operatorId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取联系结果统计
     */
    @ApiOperation("获取联系结果统计")
    @GetMapping("/result-statistics")
    public Map<String, Object> getContactResultStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<Map<String, Object>> statistics = contactRecordService.getContactResultStatistics(startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取联系方式统计
     */
    @ApiOperation("获取联系方式统计")
    @GetMapping("/type-statistics")
    public Map<String, Object> getContactTypeStatistics(
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<Map<String, Object>> statistics = contactRecordService.getContactTypeStatistics(startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取联系趋势数据
     */
    @ApiOperation("获取联系趋势数据")
    @GetMapping("/trend")
    public Map<String, Object> getContactTrend(
            @ApiParam("天数") @RequestParam(defaultValue = "30") Integer days) {
        
        List<Map<String, Object>> trend = contactRecordService.getContactTrend(days);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", trend);
        return response;
    }

    /**
     * 获取月度联系统计
     */
    @ApiOperation("获取月度联系统计")
    @GetMapping("/monthly-statistics")
    public Map<String, Object> getMonthlyContactStatistics(
            @ApiParam("月份数") @RequestParam(defaultValue = "12") Integer months) {
        
        List<Map<String, Object>> statistics = contactRecordService.getMonthlyContactStatistics(months);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取今日联系记录
     */
    @ApiOperation("获取今日联系记录")
    @GetMapping("/today")
    public Map<String, Object> getTodayContactRecords() {
        List<ContactRecord> records = contactRecordService.getTodayContactRecords();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 获取承诺还款记录
     */
    @ApiOperation("获取承诺还款记录")
    @GetMapping("/promise-repayment")
    public Map<String, Object> getPromiseRepaymentRecords() {
        List<ContactRecord> records = contactRecordService.getPromiseRepaymentRecords();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 获取操作员联系排行
     */
    @ApiOperation("获取操作员联系排行")
    @GetMapping("/operator-ranking")
    public Map<String, Object> getOperatorContactRanking(
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        
        List<Map<String, Object>> ranking = contactRecordService.getOperatorContactRanking(limit);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", ranking);
        return response;
    }

    /**
     * 获取联系效果分析
     */
    @ApiOperation("获取联系效果分析")
    @GetMapping("/effect-analysis")
    public Map<String, Object> getContactEffectAnalysis(
            @ApiParam("操作员ID") @RequestParam(required = false) Long operatorId,
            @ApiParam("天数") @RequestParam(defaultValue = "30") Integer days) {
        
        Map<String, Object> analysis = contactRecordService.getContactEffectAnalysis(operatorId, days);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", analysis);
        return response;
    }

    /**
     * 按条件批量删除联系记录
     */
    @ApiOperation("按条件批量删除联系记录")
    @DeleteMapping("/batch/condition")
    public Map<String, Object> batchDeleteContactRecordsByCondition(
            @RequestBody Map<String, Object> condition) {
        
        Long caseId = condition.get("caseId") != null ? 
                Long.valueOf(condition.get("caseId").toString()) : null;
        Long customerId = condition.get("customerId") != null ? 
                Long.valueOf(condition.get("customerId").toString()) : null;
        Long operatorId = condition.get("operatorId") != null ? 
                Long.valueOf(condition.get("operatorId").toString()) : null;
        String contactType = (String) condition.get("contactType");
        String contactResult = (String) condition.get("contactResult");
        
        boolean success = contactRecordService.batchDeleteContactRecordsByCondition(
                caseId, customerId, operatorId, contactType, contactResult
        );
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量删除失败");
        }
        return response;
    }

    /**
     * 获取跟进记录
     */
    @ApiOperation("获取跟进记录")
    @GetMapping("/follow-up/{caseId}")
    public Map<String, Object> getFollowUpRecords(
            @ApiParam("案件ID") @PathVariable Long caseId,
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        
        List<ContactRecord> records = contactRecordService.getFollowUpRecords(caseId, limit);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }
}