package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.ContactRecord;
import com.cf.financing.service.IContactRecordService;
// Swagger imports temporarily disabled
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;
// import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 联系记录控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/contact-record")
@RequiredArgsConstructor
public class ContactRecordController {

    private final IContactRecordService contactRecordService;

    /**
     * 分页查询联系记录列表
     */
        @GetMapping("/page")
    public Map<String, Object> getContactRecordPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long caseId,
            @RequestParam(required = false) Long customerId,
            @RequestParam(required = false) Long operatorId,
            @RequestParam(required = false) String contactType,
            @RequestParam(required = false) String contactResult) {
        
        Page<ContactRecord> page = new Page<>(current, size);
        IPage<ContactRecord> result = contactRecordService.getContactPage(
                page, caseId, customerId, contactType, contactResult, null, null, operatorId
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 根据案件ID查询联系记录
     */
        @GetMapping("/case/{caseId}")
    public Map<String, Object> getContactRecordsByCaseId(
            @PathVariable Long caseId) {
        
        List<ContactRecord> records = contactRecordService.getContactsByCaseId(caseId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 根据客户ID查询联系记录
     */
        @GetMapping("/customer/{customerId}")
    public Map<String, Object> getContactRecordsByCustomerId(
            @PathVariable Long customerId) {
        
        List<ContactRecord> records = contactRecordService.getContactsByCustomerId(customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 根据操作员ID查询联系记录
     */
        @GetMapping("/operator/{operatorId}")
    public Map<String, Object> getContactRecordsByOperatorId(
            @PathVariable Long operatorId) {
        
        List<ContactRecord> records = contactRecordService.getContactsByOperatorId(operatorId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 获取最后一次联系记录
     */
        @GetMapping("/last/{caseId}")
    public Map<String, Object> getLastContactRecord(
            @PathVariable Long caseId) {
        
        ContactRecord record = contactRecordService.getLastContactRecord(caseId);
        
        Map<String, Object> response = new HashMap<>();
        if (record != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", record);
        } else {
            response.put("code", 404);
            response.put("message", "暂无联系记录");
        }
        return response;
    }

    /**
     * 创建联系记录
     */
        @PostMapping
    public Map<String, Object> createContactRecord(@RequestBody ContactRecord record) {
        boolean success = contactRecordService.createContact(record);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "创建成功");
            response.put("data", record);
        } else {
            response.put("code", 400);
            response.put("message", "创建失败");
        }
        return response;
    }

    /**
     * 更新联系记录
     */
        @PutMapping("/{recordId}")
    public Map<String, Object> updateContactRecord(
            @PathVariable Long recordId,
            @RequestBody ContactRecord record) {
        
        record.setId(recordId);
        boolean success = contactRecordService.updateContact(record);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "更新失败");
        }
        return response;
    }

    /**
     * 删除联系记录
     */
        @DeleteMapping("/{recordId}")
    public Map<String, Object> deleteContactRecord(
            @PathVariable Long recordId) {
        
        boolean success = contactRecordService.deleteContact(recordId);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "删除失败");
        }
        return response;
    }

    /**
     * 批量删除联系记录
     */
        @DeleteMapping("/batch")
    public Map<String, Object> batchDeleteContactRecords(@RequestBody List<Long> recordIds) {
        boolean success = contactRecordService.batchDeleteContacts(recordIds);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量删除失败");
        }
        return response;
    }

    /**
     * 获取联系次数统计
     */
        @GetMapping("/count/case/{caseId}")
    public Map<String, Object> getContactCountByCaseId(
            @PathVariable Long caseId) {
        
        Integer count = contactRecordService.getContactCountByCaseId(caseId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", count);
        return response;
    }

    /**
     * 获取客户联系次数统计
     */
        @GetMapping("/count/customer/{customerId}")
    public Map<String, Object> getContactCountByCustomerId(
            @PathVariable Long customerId) {
        
        Integer count = contactRecordService.getContactCountByCustomerId(customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", count);
        return response;
    }

    /**
     * 获取联系统计信息
     */
        @GetMapping("/statistics")
    public Map<String, Object> getContactStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(required = false) Long operatorId) {
        Map<String, Object> statistics = contactRecordService.getContactStatistics(startDate, endDate, operatorId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取联系结果统计
     */
        @GetMapping("/result-statistics")
    public Map<String, Object> getContactResultStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<Map<String, Object>> statistics = contactRecordService.getContactResultStatistics(startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取联系方式统计
     */
        @GetMapping("/type-statistics")
    public Map<String, Object> getContactTypeStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<Map<String, Object>> statistics = contactRecordService.getContactTypeStatistics(startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取联系趋势数据
     */
        @GetMapping("/trend")
    public Map<String, Object> getContactTrend(
            @RequestParam(defaultValue = "30") Integer days) {
        
        List<Map<String, Object>> trend = contactRecordService.getContactTrend(days);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", trend);
        return response;
    }

    /**
     * 获取月度联系统计
     */
        @GetMapping("/monthly-statistics")
    public Map<String, Object> getMonthlyContactStatistics(
            @RequestParam(defaultValue = "12") Integer months) {
        
        List<Map<String, Object>> statistics = contactRecordService.getMonthlyContactStatistics(months);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取今日联系记录
     */
        @GetMapping("/today")
    public Map<String, Object> getTodayContactRecords() {
        List<ContactRecord> records = contactRecordService.getTodayContacts();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 获取承诺还款记录
     */
        @GetMapping("/promise-repayment")
    public Map<String, Object> getPromiseRepaymentRecords() {
        List<ContactRecord> records = contactRecordService.getPromiseRepaymentRecords(null);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 获取操作员联系排行
     */
        @GetMapping("/operator-ranking")
    public Map<String, Object> getOperatorContactRanking(
            @RequestParam(defaultValue = "10") Integer limit) {
        
        // 需要传入日期范围，这里使用默认值
        List<Map<String, Object>> ranking = contactRecordService.getOperatorContactRanking(null, null, limit);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", ranking);
        return response;
    }

    /**
     * 获取联系效果分析
     */
        @GetMapping("/effect-analysis")
    public Map<String, Object> getContactEffectAnalysis(
            @RequestParam(required = false) Long operatorId,
            @RequestParam(defaultValue = "30") Integer days) {
        
        // 需要传入日期范围而不是操作员ID和天数
        Map<String, Object> analysis = contactRecordService.getContactEffectAnalysis(null, null);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", analysis);
        return response;
    }

    /**
     * 按条件批量删除联系记录
     */
        @DeleteMapping("/batch/condition")
    public Map<String, Object> batchDeleteContactRecordsByCondition(
            @RequestBody Map<String, Object> condition) {
        
        Long caseId = condition.get("caseId") != null ? 
                Long.valueOf(condition.get("caseId").toString()) : null;
        Long customerId = condition.get("customerId") != null ? 
                Long.valueOf(condition.get("customerId").toString()) : null;
        Long operatorId = condition.get("operatorId") != null ? 
                Long.valueOf(condition.get("operatorId").toString()) : null;
        String contactType = (String) condition.get("contactType");
        String contactResult = (String) condition.get("contactResult");
        
        // 从条件中提取参数
        LocalDate startDate = null; // 需要从condition中解析日期
        LocalDate endDate = null; // 需要从condition中解析日期
        boolean success = contactRecordService.batchDeleteContactsByCondition(caseId, startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量删除失败");
        }
        return response;
    }

    /**
     * 获取跟进记录
     */
        @GetMapping("/follow-up/{caseId}")
    public Map<String, Object> getFollowUpRecords(
            @PathVariable Long caseId,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        List<ContactRecord> records = contactRecordService.getFollowUpRecords(caseId, limit);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }
}