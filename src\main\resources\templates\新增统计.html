<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>新增统计</title>
  <!-- 引入Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- 引入Font Awesome -->
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <!-- 引入Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <!-- 引入日期选择器 -->
  <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
  <!-- 引入中文语言包 -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/l10n/zh.min.js"></script>
  
  <!-- Tailwind配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#4080FF',
            success: '#00B42A',
            warning: '#FF7D00',
            danger: '#F53F3F',
            info: '#86909C',
            'primary-light': '#E8F3FF',
            'bg-gray': '#F2F3F5',
            'border-gray': '#E5E6EB',
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
          boxShadow: {
            'card': '0 2px 8px 0 rgba(0, 0, 0, 0.08)',
            'hover': '0 4px 16px 0 rgba(0, 0, 0, 0.12)',
          }
        },
      }
    }
  </script>
  
  <!-- 自定义工具类 -->
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .table-shadow {
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
      }
      .btn-hover {
        @apply transition-all duration-200 hover:shadow-lg;
      }
      .input-focus {
        @apply focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none;
      }
      .fade-in {
        animation: fadeIn 0.3s ease-in-out;
      }
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
    }
  </style>
</head>

<body class="bg-gray-50 font-inter text-gray-800 min-h-screen">

  <!-- 主内容区 -->
  <main class="container mx-auto px-4 py-6">
    
    <!-- 查询筛选区 -->
    <section class="bg-white rounded-xl shadow-card p-5 mb-6 fade-in">
      <h3 class="text-lg font-semibold mb-4 flex items-center">
        <i class="fa fa-filter text-primary mr-2"></i>
        查询条件
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">作业员/组织</label>
          <select class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
            <option value="">全部</option>
            <option value="1">张三</option>
            <option value="2">李四</option>
            <option value="3">王五</option>
            <option value="4">客服一组</option>
            <option value="5">客服二组</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">客户姓名</label>
          <input type="text" placeholder="请输入客户姓名" class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">客户索引号</label>
          <input type="text" placeholder="请输入客户索引号" class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">次月还款时间</label>
          <div class="relative">
            <input type="text" id="nextPaymentDate" placeholder="选择日期" class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
            <i class="fa fa-calendar absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">记录时间</label>
          <div class="relative">
            <input type="text" id="recordDate" placeholder="选择日期范围" class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
            <i class="fa fa-calendar absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>
        
        <!-- 功能按钮区域 - 水平排列在次月还款时间下方，靠右对齐 -->
        <div class="md:col-span-3 flex justify-end space-x-3">
          
          <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 btn-hover flex items-center justify-center">
            <i class="fa fa-search mr-1"></i>
            查询
          </button>
          <button class="px-4 py-2 bg-white border border-primary text-primary rounded-lg hover:bg-primary-light btn-hover flex items-center justify-center">
            <i class="fa fa-refresh mr-1"></i>
            重置
          </button>
        </div>
      </div>
    </section>
    
    <!-- 勾选统计面板 - 水平排列 -->
    <section class="bg-white rounded-xl shadow-card p-4 mb-6 fade-in">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <!-- 左侧：次月还款金额和已还金额 -->
        <div class="flex flex-wrap items-center space-x-6">
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium">次月还款金额：</span>
            <span class="text-lg font-bold text-primary">¥2,586,420.00</span>
          </div>
          
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium">已还金额：</span>
            <span class="text-lg font-bold text-success">¥1,864,530.00</span>
          </div>
        </div>
        
        <!-- 右侧：勾选户数和勾选金额 -->
        <div class="flex flex-wrap items-center space-x-6">
          <div class="flex items-center space-x-2">
            <i class="fa fa-check-circle text-primary"></i>
            <span class="text-sm font-medium">勾选户数：</span>
            <span class="text-lg font-bold text-primary" id="selectedCount">0</span> 户
          </div>
          
          <div class="flex items-center space-x-2">
            <i class="fa fa-money text-primary"></i>
            <span class="text-sm font-medium">勾选金额：</span>
            <span class="text-lg font-bold text-primary" id="selectedAmount">¥0.00</span>
          </div>
          <button class="px-4 py-2 bg-danger text-white rounded-lg hover:bg-danger/90 btn-hover flex items-center justify-center" id="deleteBtn">
            <i class="fa fa-trash mr-1"></i>
            删除
          </button>
        </div>
      </div>
    </section>
    
    <!-- 数据展示区 -->
    <section class="bg-white rounded-xl shadow-card overflow-hidden mb-6 fade-in">
      <div class="overflow-x-auto">
        <table class="w-full table-shadow">
          <thead>
            <tr class="bg-gray-50">
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-primary focus:ring-primary">
                </div>
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户姓名</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户索引号</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">协商金额</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已还金额</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">次月还款金额</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">次月还款时间</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作业员/组织</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分期方案</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">记录时间</th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-4 py-4 whitespace-nowrap">
                <input type="checkbox" class="row-checkbox rounded border-gray-300 text-primary focus:ring-primary" data-amount="8000">
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img class="h-8 w-8 rounded-full object-cover" src="https://picsum.photos/id/1027/40/40" alt="用户头像">
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">李明</div>
                  </div>
                </div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">KH2023001</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">¥32,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-success">¥24,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-primary">¥8,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-07-15</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">张三</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">分期12个月</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                2023-06-10 09:30
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-secondary hover:text-secondary/80 transition-colors" title="编辑">
                    <i class="fa fa-pencil"></i>
                  </button>
                  <button class="text-danger hover:text-danger/80 transition-colors delete-row" title="删除">
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-4 py-4 whitespace-nowrap">
                <input type="checkbox" class="row-checkbox rounded border-gray-300 text-primary focus:ring-primary" data-amount="9000">
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img class="h-8 w-8 rounded-full object-cover" src="https://picsum.photos/id/1062/40/40" alt="用户头像">
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">王芳</div>
                  </div>
                </div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">KH2023002</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">¥45,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-success">¥18,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-primary">¥9,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-07-20</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">李四</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">分期24个月</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                2023-06-12 14:15
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-secondary hover:text-secondary/80 transition-colors" title="编辑">
                    <i class="fa fa-pencil"></i>
                  </button>
                  <button class="text-danger hover:text-danger/80 transition-colors delete-row" title="删除">
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-4 py-4 whitespace-nowrap">
                <input type="checkbox" class="row-checkbox rounded border-gray-300 text-primary focus:ring-primary" data-amount="7125">
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img class="h-8 w-8 rounded-full object-cover" src="https://picsum.photos/id/1074/40/40" alt="用户头像">
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">张伟</div>
                  </div>
                </div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">KH2023003</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">¥28,500.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-success">¥14,250.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-primary">¥7,125.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-07-10</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">张三</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">分期18个月</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                2023-06-08 11:45
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-secondary hover:text-secondary/80 transition-colors" title="编辑">
                    <i class="fa fa-pencil"></i>
                  </button>
                  <button class="text-danger hover:text-danger/80 transition-colors delete-row" title="删除">
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-4 py-4 whitespace-nowrap">
                <input type="checkbox" class="row-checkbox rounded border-gray-300 text-primary focus:ring-primary" data-amount="15500">
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img class="h-8 w-8 rounded-full object-cover" src="https://picsum.photos/id/1083/40/40" alt="用户头像">
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">刘静</div>
                  </div>
                </div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">KH2023004</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">¥62,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-success">¥31,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-primary">¥15,500.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-07-25</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">王五</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">分期24个月</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                2023-06-15 16:20
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-secondary hover:text-secondary/80 transition-colors" title="编辑">
                    <i class="fa fa-pencil"></i>
                  </button>
                  <button class="text-danger hover:text-danger/80 transition-colors delete-row" title="删除">
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-4 py-4 whitespace-nowrap">
                <input type="checkbox" class="row-checkbox rounded border-gray-300 text-primary focus:ring-primary" data-amount="4500">
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img class="h-8 w-8 rounded-full object-cover" src="https://picsum.photos/id/177/40/40" alt="用户头像">
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">赵强</div>
                  </div>
                </div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">KH2023005</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">¥18,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-success">¥9,000.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-primary">¥4,500.00</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-07-05</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">李四</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">分期12个月</div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                2023-06-05 10:10
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-secondary hover:text-secondary/80 transition-colors" title="编辑">
                    <i class="fa fa-pencil"></i>
                  </button>
                  <button class="text-danger hover:text-danger/80 transition-colors delete-row" title="删除">
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>
    
    <!-- 分页导航区 -->
    <section class="bg-white rounded-xl shadow-card p-4 flex flex-col md:flex-row items-center justify-between fade-in">
      <div class="text-sm text-gray-600 mb-4 md:mb-0">
        共 <span class="font-medium text-primary">500</span> 条，每页显示 <span class="font-medium text-primary">50</span> 条，共 <span class="font-medium text-primary">10</span> 页
      </div>
      
      <div class="flex items-center space-x-2">
        <button class="px-3 py-1 rounded-lg border border-gray-200 text-gray-600 hover:border-primary hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors" disabled>
          <i class="fa fa-angle-left"></i>
        </button>
        
        <button class="w-8 h-8 flex items-center justify-center rounded-lg bg-primary text-white">1</button>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">2</button>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">3</button>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">4</button>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">5</button>
        <span class="text-gray-400">...</span>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">10</button>
        
        <button class="px-3 py-1 rounded-lg border border-gray-200 text-gray-600 hover:border-primary hover:text-primary transition-colors">
          <i class="fa fa-angle-right"></i>
        </button>
        
        <div class="flex items-center space-x-2 ml-2">
          <span class="text-sm text-gray-600">前往第</span>
          <input type="number" min="1" max="10" value="1" class="w-12 h-8 px-2 rounded-lg border border-gray-200 text-center input-focus">
          <span class="text-sm text-gray-600">页</span>
          <button class="px-3 py-1 rounded-lg bg-primary text-white hover:bg-primary/90 btn-hover">确定</button>
        </div>
      </div>
    </section>
  </main>


  <!-- 删除确认模态框 -->
  <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-lg p-6 max-w-md w-full fade-in">
      <div class="text-center mb-4">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-danger/10 text-danger mb-4">
          <i class="fa fa-exclamation-triangle text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-2">确认删除</h3>
        <p class="text-gray-600" id="deleteMessage">确定要删除这条还款记录吗？此操作无法撤销。</p>
      </div>
      
      <div class="flex space-x-3 mt-6">
        <button id="cancelDelete" class="flex-1 px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 btn-hover">
          取消
        </button>
        <button id="confirmDelete" class="flex-1 px-4 py-2 bg-danger text-white rounded-lg hover:bg-danger/90 btn-hover">
          确认删除
        </button>
      </div>
    </div>
  </div>

  <script>
    // 初始化日期选择器
    flatpickr("#nextPaymentDate", {
      dateFormat: "Y-m-d",
      locale: "zh",
      defaultDate: new Date()
    });
    
    flatpickr("#recordDate", {
      dateFormat: "Y-m-d",
      mode: "range",
      locale: "zh",
      defaultDate: [new Date(), new Date()]
    });
    
    // 全选/取消全选功能
    document.getElementById('selectAll').addEventListener('change', function() {
      const checkboxes = document.querySelectorAll('.row-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
      updateSelectionStats();
    });
    
    // 监听行复选框变化，更新全选状态和统计信息
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const allCheckboxes = document.querySelectorAll('.row-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
        document.getElementById('selectAll').checked = allCheckboxes.length === checkedCheckboxes.length;
        updateSelectionStats();
      });
    });
    
    // 更新选择统计信息
    function updateSelectionStats() {
      const checkedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
      let selectedCount = checkedCheckboxes.length;
      let selectedAmount = 0;
      
      checkedCheckboxes.forEach(checkbox => {
        const amount = parseFloat(checkbox.getAttribute('data-amount'));
        if (!isNaN(amount)) {
          selectedAmount += amount;
        }
      });
      
      // 更新UI显示
      document.getElementById('selectedCount').textContent = selectedCount;
      document.getElementById('selectedAmount').textContent = `¥${selectedAmount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`;
      
      // 禁用删除按钮如果没有选中任何记录
      document.getElementById('deleteBtn').disabled = selectedCount === 0;
      if (selectedCount === 0) {
        document.getElementById('deleteBtn').classList.add('opacity-50', 'cursor-not-allowed');
      } else {
        document.getElementById('deleteBtn').classList.remove('opacity-50', 'cursor-not-allowed');
      }
    }
    
    // 删除功能
    const deleteModal = document.getElementById('deleteModal');
    const cancelDelete = document.getElementById('cancelDelete');
    const confirmDelete = document.getElementById('confirmDelete');
    const deleteMessage = document.getElementById('deleteMessage');
    const deleteBtn = document.getElementById('deleteBtn');
    
    // 批量删除按钮
    deleteBtn.addEventListener('click', function() {
      const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
      const selectedAmount = parseFloat(document.getElementById('selectedAmount').textContent.replace(/[^0-9.-]/g, ''));
      
      if (checkedCount === 0) {
        alert('请先选择要删除的记录');
        return;
      }
      
      deleteMessage.textContent = `确定要删除选中的 ${checkedCount} 条还款记录，合计金额 ¥${selectedAmount.toFixed(2)} 吗？此操作无法撤销。`;
      deleteModal.classList.remove('hidden');
    });
    
    // 行内删除按钮
    document.querySelectorAll('.delete-row').forEach(btn => {
      btn.addEventListener('click', function() {
        const row = this.closest('tr');
        const customerName = row.querySelector('td:nth-child(2) .text-gray-900').textContent;
        const amount = row.querySelector('td:nth-child(6) .text-primary').textContent.replace(/[^0-9.-]/g, '');
        
        deleteMessage.textContent = `确定要删除客户 "${customerName}" 的还款记录，金额 ¥${amount} 吗？此操作无法撤销。`;
        deleteModal.classList.remove('hidden');
      });
    });
    
    // 取消删除
    cancelDelete.addEventListener('click', function() {
      deleteModal.classList.add('hidden');
    });
    
    // 确认删除
    confirmDelete.addEventListener('click', function() {
      // 模拟删除操作
      const checkedRows = document.querySelectorAll('.row-checkbox:checked');
      if (checkedRows.length > 0) {
        checkedRows.forEach(row => {
          row.closest('tr').remove();
        });
      } else {
        // 如果是单个删除，找到最近的选中行
        const selectedRow = document.querySelector('.delete-row').closest('tr');
        if (selectedRow) {
          selectedRow.remove();
        }
      }
      
      // 重置选择状态和统计信息
      document.getElementById('selectAll').checked = false;
      document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.checked = false;
      });
      updateSelectionStats();
      deleteModal.classList.add('hidden');
      
      // 显示删除成功提示
      alert('删除成功！');
    });
    
    // 点击模态框外部关闭
    deleteModal.addEventListener('click', function(e) {
      if (e.target === deleteModal) {
        deleteModal.classList.add('hidden');
      }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && !deleteModal.classList.contains('hidden')) {
        deleteModal.classList.add('hidden');
      }
    });
  </script>
</body>
</html>