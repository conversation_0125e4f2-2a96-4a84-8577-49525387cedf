package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.CustomerInfo;
import com.cf.financing.service.ICustomerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户信息控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Api(tags = "客户信息管理")
@RestController
@RequestMapping("/api/customer-info")
@RequiredArgsConstructor
public class CustomerInfoController {

    private final ICustomerInfoService customerInfoService;

    /**
     * 分页查询客户列表
     */
    @ApiOperation("分页查询客户列表")
    @GetMapping("/page")
    public Map<String, Object> getCustomerPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("客户编号") @RequestParam(required = false) String customerNo,
            @ApiParam("客户姓名") @RequestParam(required = false) String customerName,
            @ApiParam("手机号") @RequestParam(required = false) String phone,
            @ApiParam("身份证号") @RequestParam(required = false) String idCard,
            @ApiParam("风险等级") @RequestParam(required = false) String riskLevel,
            @ApiParam("信用等级") @RequestParam(required = false) String creditLevel) {
        
        Page<CustomerInfo> page = new Page<>(current, size);
        IPage<CustomerInfo> result = customerInfoService.getCustomerPage(
                page, customerNo, customerName, phone, idCard, riskLevel, creditLevel
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 根据ID查询客户详情
     */
    @ApiOperation("根据ID查询客户详情")
    @GetMapping("/{customerId}")
    public Map<String, Object> getCustomerById(
            @ApiParam("客户ID") @PathVariable Long customerId) {
        
        CustomerInfo customerInfo = customerInfoService.getById(customerId);
        
        Map<String, Object> response = new HashMap<>();
        if (customerInfo != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", customerInfo);
        } else {
            response.put("code", 404);
            response.put("message", "客户不存在");
        }
        return response;
    }

    /**
     * 根据客户编号查询客户信息
     */
    @ApiOperation("根据客户编号查询客户信息")
    @GetMapping("/no/{customerNo}")
    public Map<String, Object> getCustomerByNo(
            @ApiParam("客户编号") @PathVariable String customerNo) {
        
        CustomerInfo customerInfo = customerInfoService.getCustomerByNo(customerNo);
        
        Map<String, Object> response = new HashMap<>();
        if (customerInfo != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", customerInfo);
        } else {
            response.put("code", 404);
            response.put("message", "客户不存在");
        }
        return response;
    }

    /**
     * 新增客户信息
     */
    @ApiOperation("新增客户信息")
    @PostMapping
    public Map<String, Object> addCustomer(@RequestBody CustomerInfo customerInfo) {
        
        boolean success = customerInfoService.save(customerInfo);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "新增成功");
            response.put("data", customerInfo);
        } else {
            response.put("code", 500);
            response.put("message", "新增失败");
        }
        return response;
    }

    /**
     * 更新客户信息
     */
    @ApiOperation("更新客户信息")
    @PutMapping("/{customerId}")
    public Map<String, Object> updateCustomer(
            @ApiParam("客户ID") @PathVariable Long customerId,
            @RequestBody CustomerInfo customerInfo) {
        
        customerInfo.setId(customerId);
        boolean success = customerInfoService.updateById(customerInfo);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "更新成功");
            response.put("data", customerInfo);
        } else {
            response.put("code", 500);
            response.put("message", "更新失败");
        }
        return response;
    }

    /**
     * 删除客户信息
     */
    @ApiOperation("删除客户信息")
    @DeleteMapping("/{customerId}")
    public Map<String, Object> deleteCustomer(
            @ApiParam("客户ID") @PathVariable Long customerId) {
        
        boolean success = customerInfoService.removeById(customerId);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "删除成功");
        } else {
            response.put("code", 500);
            response.put("message", "删除失败");
        }
        return response;
    }

    /**
     * 根据手机号查询客户信息
     */
    @ApiOperation("根据手机号查询客户信息")
    @GetMapping("/phone/{phone}")
    public Map<String, Object> getCustomerByPhone(
            @ApiParam("手机号") @PathVariable String phone) {
        
        CustomerInfo customerInfo = customerInfoService.getCustomerByPhone(phone);
        
        Map<String, Object> response = new HashMap<>();
        if (customerInfo != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", customerInfo);
        } else {
            response.put("code", 404);
            response.put("message", "客户不存在");
        }
        return response;
    }

    /**
     * 根据身份证号查询客户信息
     */
    @ApiOperation("根据身份证号查询客户信息")
    @GetMapping("/idcard/{idCard}")
    public Map<String, Object> getCustomerByIdCard(
            @ApiParam("身份证号") @PathVariable String idCard) {
        
        CustomerInfo customerInfo = customerInfoService.getCustomerByIdCard(idCard);
        
        Map<String, Object> response = new HashMap<>();
        if (customerInfo != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", customerInfo);
        } else {
            response.put("code", 404);
            response.put("message", "客户不存在");
        }
        return response;
    }

    /**
     * 获取客户统计信息
     */
    @ApiOperation("获取客户统计信息")
    @GetMapping("/statistics")
    public Map<String, Object> getCustomerStatistics() {
        
        Map<String, Object> statistics = customerInfoService.getCustomerStatistics();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 批量导入客户信息
     */
    @ApiOperation("批量导入客户信息")
    @PostMapping("/batch")
    public Map<String, Object> batchImportCustomers(@RequestBody List<CustomerInfo> customerList) {
        
        boolean success = customerInfoService.saveBatch(customerList);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量导入成功");
            response.put("data", customerList.size());
        } else {
            response.put("code", 500);
            response.put("message", "批量导入失败");
        }
        return response;
    }
}