package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 还款记录实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("repayment_record")
public class RepaymentRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 还款ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 案件ID
     */
    @TableField("case_id")
    private Long caseId;

    /**
     * 还款编号
     */
    @TableField("repayment_no")
    private String repaymentNo;

    /**
     * 还款金额
     */
    @TableField("repayment_amount")
    private BigDecimal repaymentAmount;

    /**
     * 还款日期
     */
    @TableField("repayment_date")
    private LocalDate repaymentDate;

    /**
     * 还款方式(BANK:银行转账,ALIPAY:支付宝,WECHAT:微信,CASH:现金)
     */
    @TableField("repayment_type")
    private String repaymentType;

    /**
     * 还款状态(SUCCESS:成功,FAILED:失败,PENDING:处理中)
     */
    @TableField("repayment_status")
    private String repaymentStatus;

    /**
     * 交易流水号
     */
    @TableField("transaction_no")
    private String transactionNo;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 操作员ID
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // 非数据库字段
    /**
     * 案件编号
     */
    @TableField(exist = false)
    private String caseNo;

    /**
     * 客户姓名
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 操作员姓名
     */
    @TableField(exist = false)
    private String operatorName;
}