/* 基础样式 */
.modal-overlay {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-container {
  transform: scale(0.95);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal-overlay.active {
  opacity: 1;
}

.modal-container.active {
  transform: scale(1);
  opacity: 1;
}

/* 分页控件样式 */
.modal-pagination button {
  transition: all 0.2s ease;
}

.modal-pagination button:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.modal-pagination button.active {
  background-color: #4CAF50;
  border-color: #4CAF50;
  color: white;
}    