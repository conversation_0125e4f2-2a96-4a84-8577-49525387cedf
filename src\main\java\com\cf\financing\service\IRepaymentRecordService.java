package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.RepaymentRecord;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 还款记录服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface IRepaymentRecordService extends IService<RepaymentRecord> {

    /**
     * 分页查询还款记录
     *
     * @param page 分页参数
     * @param caseId 案件ID
     * @param customerId 客户ID
     * @param repaymentNo 还款编号
     * @param repaymentType 还款类型
     * @param status 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param operatorId 操作员ID
     * @return 分页结果
     */
    IPage<RepaymentRecord> getRepaymentPage(
            Page<RepaymentRecord> page,
            Long caseId,
            Long customerId,
            String repaymentNo,
            String repaymentType,
            String status,
            LocalDate startDate,
            LocalDate endDate,
            Long operatorId
    );

    /**
     * 根据案件ID查询还款记录
     *
     * @param caseId 案件ID
     * @return 还款记录列表
     */
    List<RepaymentRecord> getRepaymentsByCaseId(Long caseId);

    /**
     * 根据客户ID查询还款记录
     *
     * @param customerId 客户ID
     * @return 还款记录列表
     */
    List<RepaymentRecord> getRepaymentsByCustomerId(Long customerId);

    /**
     * 根据还款编号查询还款记录
     *
     * @param repaymentNo 还款编号
     * @return 还款记录
     */
    RepaymentRecord getRepaymentByNo(String repaymentNo);

    /**
     * 根据交易流水号查询还款记录
     *
     * @param transactionNo 交易流水号
     * @return 还款记录
     */
    RepaymentRecord getRepaymentByTransactionNo(String transactionNo);

    /**
     * 创建还款记录
     *
     * @param repayment 还款记录
     * @return 是否成功
     */
    boolean createRepayment(RepaymentRecord repayment);

    /**
     * 更新还款记录
     *
     * @param repayment 还款记录
     * @return 是否成功
     */
    boolean updateRepayment(RepaymentRecord repayment);

    /**
     * 删除还款记录
     *
     * @param repaymentId 还款记录ID
     * @return 是否成功
     */
    boolean deleteRepayment(Long repaymentId);

    /**
     * 批量删除还款记录
     *
     * @param repaymentIds 还款记录ID列表
     * @return 是否成功
     */
    boolean batchDeleteRepayments(List<Long> repaymentIds);

    /**
     * 根据案件ID获取总还款金额
     *
     * @param caseId 案件ID
     * @return 总还款金额
     */
    BigDecimal getTotalRepaymentByCaseId(Long caseId);

    /**
     * 根据客户ID获取总还款金额
     *
     * @param customerId 客户ID
     * @return 总还款金额
     */
    BigDecimal getTotalRepaymentByCustomerId(Long customerId);

    /**
     * 获取还款统计信息
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    Map<String, Object> getRepaymentStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * 获取还款趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getRepaymentTrend(Integer days);

    /**
     * 获取还款类型统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 类型统计数据
     */
    List<Map<String, Object>> getRepaymentTypeStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * 获取今日还款记录
     *
     * @return 今日还款记录列表
     */
    List<RepaymentRecord> getTodayRepayments();

    /**
     * 获取待确认还款记录
     *
     * @return 待确认还款记录列表
     */
    List<RepaymentRecord> getPendingRepayments();

    /**
     * 批量更新还款状态
     *
     * @param repaymentIds 还款记录ID列表
     * @param status 状态
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> repaymentIds, String status, Long operatorId);

    /**
     * 检查还款编号是否存在
     *
     * @param repaymentNo 还款编号
     * @param excludeRepaymentId 排除的还款记录ID
     * @return 是否存在
     */
    boolean checkRepaymentNoExists(String repaymentNo, Long excludeRepaymentId);

    /**
     * 检查交易流水号是否存在
     *
     * @param transactionNo 交易流水号
     * @param excludeRepaymentId 排除的还款记录ID
     * @return 是否存在
     */
    boolean checkTransactionNoExists(String transactionNo, Long excludeRepaymentId);

    /**
     * 获取月度还款统计
     *
     * @param months 月份数
     * @return 月度统计数据
     */
    List<Map<String, Object>> getMonthlyRepaymentStatistics(Integer months);

    /**
     * 获取操作员还款排行
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 排行数据
     */
    List<Map<String, Object>> getOperatorRepaymentRanking(
            LocalDate startDate, LocalDate endDate, Integer limit
    );

    /**
     * 生成还款编号
     *
     * @return 还款编号
     */
    String generateRepaymentNo();
}