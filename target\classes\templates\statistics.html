<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>新增统计</title>
  <!-- 引入Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- 引入Font Awesome -->
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <!-- 引入Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <!-- 引入日期选择器 -->
  <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
  <!-- 引入中文语言包 -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/l10n/zh.min.js"></script>
  
  <!-- Tailwind配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#4080FF',
            success: '#00B42A',
            warning: '#FF7D00',
            danger: '#F53F3F',
            info: '#86909C',
            'primary-light': '#E8F3FF',
            'bg-gray': '#F2F3F5',
            'border-gray': '#E5E6EB',
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
          boxShadow: {
            'card': '0 2px 8px 0 rgba(0, 0, 0, 0.08)',
            'hover': '0 4px 16px 0 rgba(0, 0, 0, 0.12)',
          }
        },
      }
    }
  </script>
  
  <!-- 自定义工具类 -->
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .table-shadow {
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
      }
      .btn-hover {
        @apply transition-all duration-200 hover:shadow-lg;
      }
      .input-focus {
        @apply focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none;
      }
      .fade-in {
        animation: fadeIn 0.3s ease-in-out;
      }
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
    }
  </style>
</head>

<body class="bg-gray-50 font-inter text-gray-800 min-h-screen">

  <!-- 主内容区 -->
  <main class="container mx-auto px-4 py-6">
    
    <!-- 查询筛选区 -->
    <section class="bg-white rounded-xl shadow-card p-5 mb-6 fade-in">
      <h3 class="text-lg font-semibold mb-4 flex items-center">
        <i class="fa fa-filter text-primary mr-2"></i>
        查询条件
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">作业员/组织</label>
          <select class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
            <option value="">全部</option>
            <option value="1">张三</option>
            <option value="2">李四</option>
            <option value="3">王五</option>
            <option value="4">客服一组</option>
            <option value="5">客服二组</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">客户姓名</label>
          <input type="text" placeholder="请输入客户姓名" class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">客户索引号</label>
          <input type="text" placeholder="请输入客户索引号" class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">次月还款时间</label>
          <div class="relative">
            <input type="text" id="nextPaymentDate" placeholder="选择日期" class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
            <i class="fa fa-calendar absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">记录时间</label>
          <div class="relative">
            <input type="text" id="recordDate" placeholder="选择日期范围" class="w-full px-3 py-2 rounded-lg border border-gray-200 input-focus">
            <i class="fa fa-calendar absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>
        
        <!-- 功能按钮区域 - 水平排列在次月还款时间下方，靠右对齐 -->
        <div class="md:col-span-3 flex justify-end space-x-3">
          
          <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 btn-hover flex items-center justify-center">
            <i class="fa fa-search mr-1"></i>
            查询
          </button>
          <button class="px-4 py-2 bg-white border border-primary text-primary rounded-lg hover:bg-primary-light btn-hover flex items-center justify-center">
            <i class="fa fa-refresh mr-1"></i>
            重置
          </button>
        </div>
      </div>
    </section>
    
    <!-- 勾选统计面板 - 水平排列 -->
    <section class="bg-white rounded-xl shadow-card p-4 mb-6 fade-in">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <!-- 左侧：次月还款金额和已还金额 -->
        <div class="flex flex-wrap items-center space-x-6">
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium">次月还款金额：</span>
            <span class="text-lg font-bold text-primary">¥2,586,420.00</span>
          </div>
          
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium">已还金额：</span>
            <span class="text-lg font-bold text-success">¥1,864,530.00</span>
          </div>
        </div>
        
        <!-- 右侧：勾选户数和勾选金额 -->
        <div class="flex flex-wrap items-center space-x-6">
          <div class="flex items-center space-x-2">
            <i class="fa fa-check-circle text-primary"></i>
            <span class="text-sm font-medium">勾选户数：</span>
            <span class="text-lg font-bold text-primary" id="selectedCount">0</span> 户
          </div>
          
          <div class="flex items-center space-x-2">
            <i class="fa fa-money text-primary"></i>
            <span class="text-sm font-medium">勾选金额：</span>
            <span class="text-lg font-bold text-primary" id="selectedAmount">¥0.00</span>
          </div>
          <button class="px-4 py-2 bg-danger text-white rounded-lg hover:bg-danger/90 btn-hover flex items-center justify-center" id="deleteBtn">
            <i class="fa fa-trash mr-1"></i>
            删除
          </button>
        </div>
      </div>
    </section>
    
    <!-- 数据展示区 -->
    <section class="bg-white rounded-xl shadow-card overflow-hidden mb-6 fade-in">
      <div class="overflow-x-auto">
        <table class="w-full table-shadow">
          <thead>
            <tr class="bg-gray-50">
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-primary focus:ring-primary">
                </div>
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户姓名</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户索引号</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">协商金额</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已还金额</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">次月还款金额</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">次月还款时间</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作业员/组织</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分期方案</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">记录时间</th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200" id="tableBody">
            <!-- 数据将通过JavaScript动态生成 -->
          </tbody>
        </table>
      </div>
    </section>
    
    <!-- 分页导航区 -->
    <section class="bg-white rounded-xl shadow-card p-4 flex flex-col md:flex-row items-center justify-between fade-in">
      <div class="text-sm text-gray-600 mb-4 md:mb-0">
        共 <span class="font-medium text-primary">500</span> 条，每页显示 <span class="font-medium text-primary">50</span> 条，共 <span class="font-medium text-primary">10</span> 页
      </div>
      
      <div class="flex items-center space-x-2">
        <button class="px-3 py-1 rounded-lg border border-gray-200 text-gray-600 hover:border-primary hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors" disabled>
          <i class="fa fa-angle-left"></i>
        </button>
        
        <button class="w-8 h-8 flex items-center justify-center rounded-lg bg-primary text-white">1</button>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">2</button>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">3</button>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">4</button>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">5</button>
        <span class="text-gray-400">...</span>
        <button class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-light hover:text-primary transition-colors">10</button>
        
        <button class="px-3 py-1 rounded-lg border border-gray-200 text-gray-600 hover:border-primary hover:text-primary transition-colors">
          <i class="fa fa-angle-right"></i>
        </button>
        
        <div class="flex items-center space-x-2 ml-2">
          <span class="text-sm text-gray-600">前往第</span>
          <input type="number" min="1" max="10" value="1" class="w-12 h-8 px-2 rounded-lg border border-gray-200 text-center input-focus">
          <span class="text-sm text-gray-600">页</span>
          <button class="px-3 py-1 rounded-lg bg-primary text-white hover:bg-primary/90 btn-hover">确定</button>
        </div>
      </div>
    </section>
  </main>

  <!-- 删除确认模态框 -->
  <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-lg p-6 max-w-md w-full fade-in">
      <div class="text-center mb-4">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-danger/10 text-danger mb-4">
          <i class="fa fa-exclamation-triangle text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-2">确认删除</h3>
        <p class="text-gray-600" id="deleteMessage">确定要删除这条还款记录吗？此操作无法撤销。</p>
      </div>
      
      <div class="flex space-x-3 mt-6">
        <button id="cancelDelete" class="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
          取消
        </button>
        <button id="confirmDelete" class="flex-1 px-4 py-2 bg-danger text-white rounded-lg hover:bg-danger/90 transition-colors">
          确认删除
        </button>
      </div>
    </div>
  </div>

  <script>
    // 模拟数据
    const mockData = [
      {
        id: 1,
        customerName: '李明',
        customerId: 'KH2023001',
        negotiatedAmount: 32000,
        paidAmount: 24000,
        nextPaymentAmount: 8000,
        nextPaymentDate: '2023-07-15',
        operator: '张三',
        installmentPlan: '分期12个月',
        recordTime: '2023-06-10 09:30',
        avatar: 'https://picsum.photos/id/1027/40/40'
      },
      {
        id: 2,
        customerName: '王芳',
        customerId: 'KH2023002',
        negotiatedAmount: 45000,
        paidAmount: 18000,
        nextPaymentAmount: 9000,
        nextPaymentDate: '2023-07-20',
        operator: '李四',
        installmentPlan: '分期24个月',
        recordTime: '2023-06-12 14:15',
        avatar: 'https://picsum.photos/id/1062/40/40'
      },
      {
        id: 3,
        customerName: '张伟',
        customerId: 'KH2023003',
        negotiatedAmount: 28500,
        paidAmount: 14250,
        nextPaymentAmount: 7125,
        nextPaymentDate: '2023-07-10',
        operator: '张三',
        installmentPlan: '分期18个月',
        recordTime: '2023-06-08 11:45',
        avatar: 'https://picsum.photos/id/1074/40/40'
      },
      {
        id: 4,
        customerName: '刘静',
        customerId: 'KH2023004',
        negotiatedAmount: 62000,
        paidAmount: 31000,
        nextPaymentAmount: 15500,
        nextPaymentDate: '2023-07-25',
        operator: '王五',
        installmentPlan: '分期24个月',
        recordTime: '2023-06-15 16:20',
        avatar: 'https://picsum.photos/id/1083/40/40'
      },
      {
        id: 5,
        customerName: '赵强',
        customerId: 'KH2023005',
        negotiatedAmount: 18000,
        paidAmount: 9000,
        nextPaymentAmount: 4500,
        nextPaymentDate: '2023-07-05',
        operator: '李四',
        installmentPlan: '分期12个月',
        recordTime: '2023-06-05 10:10',
        avatar: 'https://picsum.photos/id/177/40/40'
      }
    ];

    // 渲染表格数据
    function renderTable() {
      const tableBody = document.getElementById('tableBody');
      tableBody.innerHTML = '';

      mockData.forEach(item => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 transition-colors';
        row.innerHTML = `
          <td class="px-4 py-4 whitespace-nowrap">
            <input type="checkbox" class="row-checkbox rounded border-gray-300 text-primary focus:ring-primary" data-amount="${item.nextPaymentAmount}" data-id="${item.id}">
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <img class="h-8 w-8 rounded-full object-cover" src="${item.avatar}" alt="用户头像">
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900">${item.customerName}</div>
              </div>
            </div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">${item.customerId}</div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">¥${item.negotiatedAmount.toLocaleString()}.00</div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="text-sm text-success">¥${item.paidAmount.toLocaleString()}.00</div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-primary">¥${item.nextPaymentAmount.toLocaleString()}.00</div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">${item.nextPaymentDate}</div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">${item.operator}</div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">${item.installmentPlan}</div>
          </td>
          <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
            ${item.recordTime}
          </td>
          <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
            <div class="flex justify-end space-x-2">
              <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                <i class="fa fa-eye"></i>
              </button>
              <button class="text-secondary hover:text-secondary/80 transition-colors" title="编辑">
                <i class="fa fa-pencil"></i>
              </button>
              <button class="text-danger hover:text-danger/80 transition-colors delete-row" title="删除" data-id="${item.id}">
                <i class="fa fa-trash"></i>
              </button>
            </div>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
      // 渲染表格
      renderTable();

      // 初始化日期选择器
      flatpickr("#nextPaymentDate", {
        dateFormat: "Y-m-d",
        locale: "zh"
      });

      flatpickr("#recordDate", {
        mode: "range",
        dateFormat: "Y-m-d",
        locale: "zh"
      });

      // 全选功能
      document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
          checkbox.checked = this.checked;
        });
        updateSelectedStats();
      });

      // 行选择功能
      document.addEventListener('change', function(e) {
        if (e.target.classList.contains('row-checkbox')) {
          updateSelectedStats();
        }
      });

      // 更新选中统计
      function updateSelectedStats() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        const count = checkedBoxes.length;
        let totalAmount = 0;

        checkedBoxes.forEach(checkbox => {
          totalAmount += parseFloat(checkbox.dataset.amount);
        });

        document.getElementById('selectedCount').textContent = count;
        document.getElementById('selectedAmount').textContent = `¥${totalAmount.toLocaleString()}.00`;
      }

      // 删除功能
      let deleteItemId = null;
      
      document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-row')) {
          deleteItemId = e.target.closest('.delete-row').dataset.id;
          document.getElementById('deleteModal').classList.remove('hidden');
        }
      });

      document.getElementById('cancelDelete').addEventListener('click', function() {
        document.getElementById('deleteModal').classList.add('hidden');
        deleteItemId = null;
      });

      document.getElementById('confirmDelete').addEventListener('click', function() {
        if (deleteItemId) {
          const index = mockData.findIndex(item => item.id == deleteItemId);
          if (index > -1) {
            mockData.splice(index, 1);
            renderTable();
          }
        }
        document.getElementById('deleteModal').classList.add('hidden');
        deleteItemId = null;
      });

      // 批量删除功能
      document.getElementById('deleteBtn').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        if (checkedBoxes.length === 0) {
          alert('请先选择要删除的记录');
          return;
        }
        
        document.getElementById('deleteMessage').textContent = `确定要删除选中的 ${checkedBoxes.length} 条记录吗？此操作无法撤销。`;
        document.getElementById('deleteModal').classList.remove('hidden');
        
        document.getElementById('confirmDelete').onclick = function() {
          const idsToDelete = Array.from(checkedBoxes).map(cb => cb.dataset.id);
          idsToDelete.forEach(id => {
            const index = mockData.findIndex(item => item.id == id);
            if (index > -1) {
              mockData.splice(index, 1);
            }
          });
          renderTable();
          updateSelectedStats();
          document.getElementById('deleteModal').classList.add('hidden');
        };
      });
    });
  </script>
</body>
</html>
