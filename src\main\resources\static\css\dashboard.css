/* Dashboard样式文件 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

body {
    background: linear-gradient(135deg, #0c2461 0%, #1e3799 100%);
    color: #f0f2f5;
    min-height: 100vh;
    padding: 20px;
    overflow-x: hidden;
    font-size: 14px;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-title h1 {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(90deg, #f6b93b, #fad390);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.header-title i {
    font-size: 26px;
    color: #f6b93b;
    background: rgba(12, 36, 97, 0.7);
    padding: 10px;
    border-radius: 50%;
}

.date-info {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

/* 顶部数据概览 */
.data-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.overview-card {
    padding: 16px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 110px;
}

.overview-card.primary {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(99, 102, 241, 0.25));
}

.overview-card.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.25), rgba(249, 115, 22, 0.25));
}

.overview-card.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.25), rgba(52, 211, 153, 0.25));
}

.overview-card.info {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.25), rgba(14, 165, 233, 0.25));
}

.metric {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.metric .label {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.85);
}

.metric .value {
    font-size: 20px;
    font-weight: 700;
}

.metric .trend {
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 15px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    width: fit-content;
}

.trend.up {
    background: rgba(39, 174, 96, 0.25);
    color: #27ae60;
}

.trend.down {
    background: rgba(231, 76, 60, 0.25);
    color: #e74c3c;
}

.icon i {
    font-size: 28px;
    opacity: 0.7;
}

/* 主体内容区 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }
}

/* 面板通用样式 - 固定高度300px */
.panel {
    background: rgba(20, 40, 100, 0.45);
    border-radius: 14px;
    padding: 16px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    height: 300px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-title {
    font-size: 18px;
    font-weight: 600;
    color: #f6b93b;
    display: flex;
    align-items: center;
    gap: 10px;
}

.panel-actions {
    display: flex;
    gap: 8px;
}

.btn {
    padding: 6px 12px;
    border-radius: 8px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn.active {
    background: #f6b93b;
    color: #0c2461;
}

select {
    padding: 6px 12px;
    border-radius: 8px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    cursor: pointer;
    font-size: 13px;
}

/* 面板内容区域 - 可滚动 */
.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 3px;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
    width: 8px;
}

.panel-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb {
    background: rgba(246, 185, 59, 0.6);
    border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: rgba(246, 185, 59, 0.8);
}

/* 排行榜样式 */
.ranking-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.top-performer, .performer {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.2s ease;
}

.top-performer {
    background: rgba(246, 185, 59, 0.15);
    border-left: 4px solid #f6b93b;
}

.top-performer:hover, .performer:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    background: rgba(12, 36, 97, 0.7);
    color: #f6b93b;
    margin-right: 12px;
}

.top-performer .rank {
    background: #f6b93b;
    color: #0c2461;
}

.user-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
}

.details {
    line-height: 1.3;
}

.name {
    font-weight: 600;
    font-size: 15px;
}

.team {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.performance-data {
    min-width: 120px;
    text-align: right;
}

.amount {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 15px;
}

.progress-container {
    position: relative;
    height: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #34d399);
    border-radius: 8px;
    transition: width 0.5s ease;
}

.progress-container span {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 11px;
    color: white;
    z-index: 1;
}

/* 图表容器 */
.chart-container {
    height: 100%;
    position: relative;
}

/* 案件分布表格样式 */
.case-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    margin-top: -10px;
}

.case-table th {
    text-align: left;
    padding: 12px;
    background-color: #0c2461;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: -10px;
    z-index: 20;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.case-table td {
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
}

.case-table tr:last-child td {
    border-bottom: none;
}

.case-table .highlight {
    color: #f6b93b;
    font-weight: 600;
}

.case-table .progress-cell {
    width: 40%;
}

.case-table .progress-bar {
    height: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    overflow: hidden;
    margin-top: 5px;
}

.case-table .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #6366f1);
    border-radius: 5px;
    transition: width 0.5s ease;
}

/* 顶部快速操作 */
.quick-actions-top {
    display: flex;
    gap: 10px;
    margin-left: auto;
    margin-right: 15px;
}

.action-btn-top {
    padding: 8px 15px;
    background: rgba(246, 185, 59, 0.2);
    border: 1px solid rgba(246, 185, 59, 0.4);
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #f6b93b;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.2s ease;
}

.action-btn-top:hover {
    background: rgba(246, 185, 59, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(246, 185, 59, 0.2);
}

.action-btn-top i {
    font-size: 14px;
}
