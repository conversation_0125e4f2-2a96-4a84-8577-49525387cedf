<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.ContactRecordMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.ContactRecord">
        <id column="id" property="id" />
        <result column="case_id" property="caseId" />
        <result column="contact_type" property="contactType" />
        <result column="contact_time" property="contactTime" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="contact_result" property="contactResult" />
        <result column="contact_content" property="contactContent" />
        <result column="promise_amount" property="promiseAmount" />
        <result column="promise_date" property="promiseDate" />
        <result column="operator_id" property="operatorId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 扩展结果映射（包含案件和客户信息） -->
    <resultMap id="ExtendedResultMap" type="com.cf.financing.entity.ContactRecord" extends="BaseResultMap">
        <result column="case_no" property="caseNo" />
        <result column="customer_name" property="customerName" />
        <result column="operator_name" property="operatorName" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, case_id, contact_type, contact_time, contact_person, contact_phone, contact_result, 
        contact_content, promise_amount, promise_date, operator_id, create_time
    </sql>

    <!-- 分页查询联系记录 -->
    <select id="selectContactRecordPage" resultMap="ExtendedResultMap">
        SELECT 
            cr.id, cr.case_id, cr.contact_type, cr.contact_time, cr.contact_person, cr.contact_phone,
            cr.contact_result, cr.contact_content, cr.promise_amount, cr.promise_date, cr.operator_id, cr.create_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM contact_record cr
        LEFT JOIN case_info c ON cr.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON cr.operator_id = u.id
        <where>
            <if test="caseId != null">
                AND cr.case_id = #{caseId}
            </if>
            <if test="customerName != null and customerName != ''">
                AND cu.customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="contactType != null and contactType != ''">
                AND cr.contact_type = #{contactType}
            </if>
            <if test="contactResult != null and contactResult != ''">
                AND cr.contact_result = #{contactResult}
            </if>
            <if test="operatorId != null">
                AND cr.operator_id = #{operatorId}
            </if>
            <if test="startTime != null">
                AND cr.contact_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND cr.contact_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY cr.create_time DESC
    </select>

    <!-- 根据案件ID查询联系记录 -->
    <select id="selectContactRecordsByCaseId" resultMap="ExtendedResultMap">
        SELECT
            cr.id, cr.case_id, cr.contact_type, cr.contact_time, cr.contact_person, cr.contact_phone,
            cr.contact_result, cr.contact_content, cr.promise_amount, cr.promise_date, cr.operator_id, cr.create_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM contact_record cr
        LEFT JOIN case_info c ON cr.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON cr.operator_id = u.id
        WHERE cr.case_id = #{caseId}
        ORDER BY cr.create_time DESC
    </select>

    <!-- 根据客户ID查询联系记录 -->
    <select id="selectContactRecordsByCustomerId" resultMap="ExtendedResultMap">
        SELECT
            cr.id, cr.case_id, cr.contact_type, cr.contact_time, cr.contact_person, cr.contact_phone,
            cr.contact_result, cr.contact_content, cr.promise_amount, cr.promise_date, cr.operator_id, cr.create_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM contact_record cr
        INNER JOIN case_info c ON cr.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON cr.operator_id = u.id
        WHERE c.customer_id = #{customerId}
        ORDER BY cr.create_time DESC
    </select>

    <!-- 根据操作员ID查询联系记录 -->
    <select id="selectContactRecordsByOperatorId" resultMap="ExtendedResultMap">
        SELECT
            cr.id, cr.case_id, cr.contact_type, cr.contact_time, cr.contact_person, cr.contact_phone,
            cr.contact_result, cr.contact_content, cr.promise_amount, cr.promise_date, cr.operator_id, cr.create_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM contact_record cr
        LEFT JOIN case_info c ON cr.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON cr.operator_id = u.id
        WHERE cr.operator_id = #{operatorId}
        <if test="startTime != null">
            AND cr.contact_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND cr.contact_time &lt;= #{endTime}
        </if>
        ORDER BY cr.create_time DESC
    </select>

    <!-- 获取案件的最后联系记录 -->
    <select id="selectLastContactRecordByCaseId" resultMap="ExtendedResultMap">
        SELECT 
            cr.id, cr.case_id, cr.contact_type, cr.contact_time, cr.contact_person, cr.contact_phone,
            cr.contact_result, cr.contact_content, cr.promise_amount, cr.promise_date, cr.operator_id, cr.create_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM contact_record cr
        LEFT JOIN case_info c ON cr.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON cr.operator_id = u.id
        WHERE cr.case_id = #{caseId}
        ORDER BY cr.create_time DESC
        LIMIT 1
    </select>

    <!-- 获取案件的联系次数 -->
    <select id="selectContactCountByCaseId" resultType="int">
        SELECT COUNT(*)
        FROM contact_record
        WHERE case_id = #{caseId}
    </select>

    <!-- 获取客户的联系次数 -->
    <select id="selectContactCountByCustomerId" resultType="int">
        SELECT COUNT(*)
        FROM contact_record cr
        INNER JOIN case_info c ON cr.case_id = c.id
        WHERE c.customer_id = #{customerId}
    </select>

    <!-- 获取联系统计信息 -->
    <select id="selectContactStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalContacts,
            COUNT(CASE WHEN contact_result = 'CONNECTED' THEN 1 END) as connectedContacts,
            COUNT(CASE WHEN contact_result = 'NOT_CONNECTED' THEN 1 END) as notConnectedContacts,
            COUNT(CASE WHEN contact_result = 'BUSY' THEN 1 END) as busyContacts,
            COUNT(CASE WHEN contact_result = 'REFUSED' THEN 1 END) as refusedContacts,
            COUNT(CASE WHEN promise_amount IS NOT NULL AND promise_amount &gt; 0 THEN 1 END) as promiseContacts,
            COALESCE(SUM(promise_amount), 0) as totalPromiseAmount,
            COUNT(CASE WHEN DATE(contact_time) = CURDATE() THEN 1 END) as todayContacts,
            ROUND(COUNT(CASE WHEN contact_result = 'CONNECTED' THEN 1 END) * 100.0 / COUNT(*), 2) as connectionRate
        FROM contact_record
        <where>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="startTime != null">
                AND contact_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND contact_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 获取联系结果统计 -->
    <select id="selectContactResultStats" resultType="map">
        SELECT
            contact_result as contactResult,
            COUNT(*) as contactCount,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM contact_record
                <where>
                    <if test="operatorId != null">
                        AND operator_id = #{operatorId}
                    </if>
                    <if test="startTime != null">
                        AND contact_time &gt;= #{startTime}
                    </if>
                    <if test="endTime != null">
                        AND contact_time &lt;= #{endTime}
                    </if>
                </where>
            ), 2) as percentage
        FROM contact_record
        <where>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="startTime != null">
                AND contact_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND contact_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY contact_result
        ORDER BY contactCount DESC
    </select>

    <!-- 获取联系类型统计 -->
    <select id="selectContactTypeStats" resultType="map">
        SELECT
            contact_type as contactType,
            COUNT(*) as contactCount,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM contact_record
                <where>
                    <if test="operatorId != null">
                        AND operator_id = #{operatorId}
                    </if>
                    <if test="startTime != null">
                        AND contact_time &gt;= #{startTime}
                    </if>
                    <if test="endTime != null">
                        AND contact_time &lt;= #{endTime}
                    </if>
                </where>
            ), 2) as percentage
        FROM contact_record
        <where>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="startTime != null">
                AND contact_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND contact_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY contact_type
        ORDER BY contactCount DESC
    </select>

    <!-- 获取联系趋势数据 -->
    <select id="selectContactTrendData" resultType="map">
        SELECT
            DATE(contact_time) as contactDate,
            COUNT(*) as contactCount,
            COUNT(CASE WHEN contact_result = 'CONNECTED' THEN 1 END) as connectedCount
        FROM contact_record
        WHERE contact_time &gt;= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        <if test="operatorId != null">
            AND operator_id = #{operatorId}
        </if>
        GROUP BY DATE(contact_time)
        ORDER BY contactDate
    </select>

    <!-- 获取今日联系记录 -->
    <select id="selectTodayContactRecords" resultMap="ExtendedResultMap">
        SELECT
            cr.id, cr.case_id, cr.contact_type, cr.contact_time, cr.contact_person, cr.contact_phone,
            cr.contact_result, cr.contact_content, cr.promise_amount, cr.promise_date, cr.operator_id, cr.create_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM contact_record cr
        LEFT JOIN case_info c ON cr.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON cr.operator_id = u.id
        WHERE DATE(cr.contact_time) = #{contactDate}
        <if test="operatorId != null">
            AND cr.operator_id = #{operatorId}
        </if>
        ORDER BY cr.create_time DESC
    </select>

    <!-- 获取有承诺还款的联系记录 -->
    <select id="selectPromiseRepaymentRecords" resultMap="ExtendedResultMap">
        SELECT
            cr.id, cr.case_id, cr.contact_type, cr.contact_time, cr.contact_person, cr.contact_phone,
            cr.contact_result, cr.contact_content, cr.promise_amount, cr.promise_date, cr.operator_id, cr.create_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM contact_record cr
        LEFT JOIN case_info c ON cr.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON cr.operator_id = u.id
        WHERE cr.promise_amount IS NOT NULL AND cr.promise_amount &gt; 0
        <if test="operatorId != null">
            AND cr.operator_id = #{operatorId}
        </if>
        <if test="promiseStartDate != null">
            AND DATE(cr.promise_date) &gt;= #{promiseStartDate}
        </if>
        <if test="promiseEndDate != null">
            AND DATE(cr.promise_date) &lt;= #{promiseEndDate}
        </if>
        ORDER BY cr.promise_date ASC
    </select>

    <!-- 获取操作员联系业绩排行 -->
    <select id="selectOperatorContactRanking" resultType="map">
        SELECT
            u.real_name as operatorName,
            COUNT(*) as totalContacts,
            COUNT(CASE WHEN cr.contact_result = 'CONNECTED' THEN 1 END) as connectedContacts,
            ROUND(COUNT(CASE WHEN cr.contact_result = 'CONNECTED' THEN 1 END) * 100.0 / COUNT(*), 2) as connectionRate,
            COUNT(CASE WHEN cr.promise_amount IS NOT NULL AND cr.promise_amount &gt; 0 THEN 1 END) as promiseContacts,
            COALESCE(SUM(cr.promise_amount), 0) as totalPromiseAmount
        FROM contact_record cr
        INNER JOIN sys_user u ON cr.operator_id = u.id
        WHERE cr.contact_time &gt;= #{startTime}
        AND cr.contact_time &lt;= #{endTime}
        GROUP BY cr.operator_id, u.real_name
        ORDER BY totalContacts DESC, connectionRate DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取联系效果分析 -->
    <select id="selectContactEffectAnalysis" resultType="map">
        SELECT
            COUNT(*) as totalContacts,
            COUNT(CASE WHEN contact_result = 'CONNECTED' THEN 1 END) as connectedContacts,
            ROUND(COUNT(CASE WHEN contact_result = 'CONNECTED' THEN 1 END) * 100.0 / COUNT(*), 2) as connectionRate,
            COUNT(CASE WHEN promise_amount IS NOT NULL AND promise_amount &gt; 0 THEN 1 END) as promiseContacts,
            ROUND(COUNT(CASE WHEN promise_amount IS NOT NULL AND promise_amount &gt; 0 THEN 1 END) * 100.0 / COUNT(CASE WHEN contact_result = 'CONNECTED' THEN 1 END), 2) as promiseRate,
            COALESCE(SUM(promise_amount), 0) as totalPromiseAmount,
            COALESCE(AVG(promise_amount), 0) as avgPromiseAmount
        FROM contact_record
        <where>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="startTime != null">
                AND contact_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND contact_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 获取月度联系统计 -->
    <select id="selectMonthlyContactStats" resultType="map">
        SELECT
            DATE_FORMAT(contact_time, '%Y-%m') as month,
            COUNT(*) as contactCount,
            COUNT(CASE WHEN contact_result = 'CONNECTED' THEN 1 END) as connectedCount,
            ROUND(COUNT(CASE WHEN contact_result = 'CONNECTED' THEN 1 END) * 100.0 / COUNT(*), 2) as connectionRate
        FROM contact_record
        WHERE contact_time &gt;= DATE_SUB(NOW(), INTERVAL #{months} MONTH)
        GROUP BY DATE_FORMAT(contact_time, '%Y-%m')
        ORDER BY month
    </select>

    <!-- 批量删除联系记录 -->
    <delete id="batchDeleteContactRecords">
        DELETE FROM contact_record
        WHERE id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <!-- 根据案件ID删除联系记录 -->
    <delete id="deleteContactRecordsByCaseId">
        DELETE FROM contact_record
        WHERE case_id = #{caseId}
    </delete>

    <!-- 获取需要跟进的联系记录 -->
    <select id="selectFollowUpRecords" resultMap="ExtendedResultMap">
        SELECT 
            cr.id, cr.case_id, cr.contact_type, cr.contact_time, cr.contact_person, cr.contact_phone,
            cr.contact_result, cr.contact_content, cr.promise_amount, cr.promise_date, cr.operator_id, cr.create_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM contact_record cr
        LEFT JOIN case_info c ON cr.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON cr.operator_id = u.id
        WHERE cr.promise_date IS NOT NULL 
        AND DATE(cr.promise_date) = #{followUpDate}
        <if test="operatorId != null">
            AND cr.operator_id = #{operatorId}
        </if>
        ORDER BY cr.promise_date ASC
    </select>

</mapper>