package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.SysUser;

import java.util.List;
import java.util.Map;

/**
 * 系统用户服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ISysUserService extends IService<SysUser> {

    /**
 * 分页查询用户信息
 *
 * @param page 分页参数
 * @param username 用户名
 * @param realName 真实姓名
 * @param phone 手机号
 * @param email 邮箱
 * @param status 状态
 * @param deptId 部门ID
 * @param roleId 角色ID
 * @return 用户分页数据
 */
    IPage<SysUser> getUserPage(
            Page<SysUser> page,
            String username,
            String realName,
            String phone,
            String email,
            String status,
            Long deptId,
            Long roleId
    );

    /**
 * 根据用户名查询用户详细信息
 *
 * @param username 用户名
 * @return 用户信息
 */
    SysUser getUserByUsername(String username);

    /**
 * 根据用户ID查询用户详细信息
 *
 * @param userId 用户ID
 * @return 用户信息
 */
    SysUser getUserById(Long userId);

    /**
 * 查询部门下的所有用户
 *
 * @param deptId 部门ID
 * @return 用户列表
 */
    List<SysUser> getUsersByDeptId(Long deptId);

    /**
 * 查询角色下的所有用户
 *
 * @param roleId 角色ID
 * @return 用户列表
 */
    List<SysUser> getUsersByRoleId(Long roleId);

    /**
 * 创建用户
 *
 * @param user 用户信息
 * @return 创建结果
 */
    boolean createUser(SysUser user);

    /**
 * 更新用户信息
 *
 * @param user 用户信息
 * @return 更新结果
 */
    boolean updateUser(SysUser user);

    /**
 * 删除用户
 *
 * @param userId 用户ID
 * @return 删除结果
 */
    boolean deleteUser(Long userId);

    /**
 * 批量删除用户
 *
 * @param userIds 用户ID列表
 * @return 删除结果
 */
    boolean batchDeleteUsers(List<Long> userIds);

    /**
 * 用户登录
 *
 * @param username 用户名
 * @param password 密码
 * @param loginIp 登录IP
 * @return 登录结果
 */
    SysUser login(String username, String password, String loginIp);

    /**
 * 更新用户登录信息
 *
 * @param userId 用户ID
 * @param loginIp 登录IP
 * @return 更新结果
 */
    boolean updateLoginInfo(Long userId, String loginIp);

    /**
 * 修改密码
 *
 * @param userId 用户ID
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 * @return 修改结果
 */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
 * 重置用户密码
 *
 * @param userId 用户ID
 * @param newPassword 新密码
 * @return 重置结果
 */
    boolean resetPassword(Long userId, String newPassword);

    /**
 * 启用/禁用用户
 *
 * @param userId 用户ID
 * @param status 状态
 * @return 更新结果
 */
    boolean updateUserStatus(Long userId, String status);

    /**
 * 批量更新用户状态
 *
 * @param userIds 用户ID列表
 * @param status 状态
 * @return 更新结果
 */
    boolean batchUpdateUserStatus(List<Long> userIds, String status);

    /**
 * 获取用户统计信息
 *
 * @param deptId 部门ID（可选）
 * @return 统计信息
 */
    Map<String, Object> getUserStatistics(Long deptId);

    /**
 * 检查用户名是否存在
 *
 * @param username 用户名
 * @param excludeUserId 排除的用户ID
 * @return 是否存在
 */
    boolean checkUsernameExists(String username, Long excludeUserId);

    /**
 * 检查手机号是否存在
 *
 * @param phone 手机号
 * @param excludeUserId 排除的用户ID
 * @return 是否存在
 */
    boolean checkPhoneExists(String phone, Long excludeUserId);

    /**
 * 检查邮箱是否存在
 *
 * @param email 邮箱
 * @param excludeUserId 排除的用户ID
 * @return 是否存在
 */
    boolean checkEmailExists(String email, Long excludeUserId);

    /**
 * 获取在线用户列表
 *
 * @return 在线用户列表
 */
    List<SysUser> getOnlineUsers();

    /**
 * 验证用户密码
 *
 * @param rawPassword 原始密码
 * @param encodedPassword 加密密码
 * @return 验证结果
 */
    boolean verifyPassword(String rawPassword, String encodedPassword);

    /**
 * 加密密码
 *
 * @param rawPassword 原始密码
 * @return 加密密码
 */
    String encodePassword(String rawPassword);

    /**
 * 生成用户编号
 *
 * @return 用户编号
 */
    String generateUserNo();
}