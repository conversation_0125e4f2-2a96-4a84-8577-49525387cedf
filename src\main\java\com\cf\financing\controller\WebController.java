package com.cf.financing.controller;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpSession;
import java.util.Random;

/**
 * Web控制器 - 处理页面路由
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Controller
public class WebController {

    /**
     * 首页重定向到登录页
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage(Model model, HttpSession session) {
        // 生成验证码
        String captcha = generateCaptcha();
        session.setAttribute("captcha", captcha);
        model.addAttribute("captcha", captcha);
        return "login";
    }

    // Spring Security会处理登录，这里不需要自定义登录逻辑

    /**
     * 首页仪表板
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        // 获取当前认证用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            model.addAttribute("user", authentication.getName());
            return "dashboard";
        }
        return "redirect:/login";
    }

    /**
     * 案池页面
     */
    @GetMapping("/case-pool")
    public String casePool(Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            model.addAttribute("user", authentication.getName());
            return "case-pool";
        }
        return "redirect:/login";
    }

    /**
     * 还款管理页面
     */
    @GetMapping("/repayment")
    public String repayment(Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            model.addAttribute("user", authentication.getName());
            return "repayment";
        }
        return "redirect:/login";
    }

    /**
     * 任务预测页面
     */
    @GetMapping("/task-prediction")
    public String taskPrediction(Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            model.addAttribute("user", authentication.getName());
            return "task-prediction";
        }
        return "redirect:/login";
    }

    /**
     * 换单查询页面
     */
    @GetMapping("/exchange-query")
    public String exchangeQuery(Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            model.addAttribute("user", authentication.getName());
            return "exchange-query";
        }
        return "redirect:/login";
    }

    /**
     * 新增统计页面
     */
    @GetMapping("/new-statistics")
    public String newStatistics(Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            model.addAttribute("user", authentication.getName());
            return "new-statistics";
        }
        return "redirect:/login";
    }

    // 登出由Spring Security处理，不需要自定义方法

    /**
     * 生成验证码
     */
    private String generateCaptcha() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder captcha = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            captcha.append(chars.charAt(random.nextInt(chars.length())));
        }
        return captcha.toString();
    }
}
