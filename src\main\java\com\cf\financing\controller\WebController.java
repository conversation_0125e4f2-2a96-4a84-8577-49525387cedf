package com.cf.financing.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpSession;
import java.util.Random;

/**
 * Web控制器 - 处理页面路由
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Controller
public class WebController {

    /**
     * 首页重定向到登录页
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage(Model model, HttpSession session) {
        // 生成验证码
        String captcha = generateCaptcha();
        session.setAttribute("captcha", captcha);
        model.addAttribute("captcha", captcha);
        return "login";
    }

    // 移除自定义登录处理，使用Spring Security默认处理

    /**
     * 首页仪表板
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        return "dashboard";
    }

    /**
     * 主页面
     */
    @GetMapping("/main")
    public String main(Model model) {
        // Spring Security会自动处理认证，这里直接返回main页面
        return "main";
    }

    /**
     * 首页内容页面（用于iframe嵌入）
     */
    @GetMapping("/home")
    public String home(Model model) {
        return "home";
    }

    /**
     * 案池页面
     */
    @GetMapping("/case-pool")
    public String casePool(Model model) {
        return "case-pool";
    }

    /**
     * 还款管理页面
     */
    @GetMapping("/repayment")
    public String repayment(Model model) {
        return "repayment";
    }

    /**
     * 作业清单页面
     */
    @GetMapping("/tasklist")
    public String tasklist(Model model) {
        return "tasklist";
    }

    /**
     * 任务预测页面
     */
    @GetMapping("/prediction")
    public String prediction(Model model) {
        return "prediction";
    }

    /**
     * 换单查询页面
     */
    @GetMapping("/exchange")
    public String exchange(Model model) {
        return "exchange";
    }

    /**
     * 新增统计页面
     */
    @GetMapping("/statistics")
    public String statistics(Model model) {
        return "statistics";
    }

    // 保持旧的路由以兼容性
    @GetMapping("/task-prediction")
    public String taskPredictionOld(Model model) {
        return "prediction";
    }

    @GetMapping("/exchange-query")
    public String exchangeQueryOld(Model model) {
        return "exchange";
    }

    @GetMapping("/new-statistics")
    public String newStatisticsOld(Model model) {
        return "statistics";
    }

    // 登出由Spring Security自动处理

    /**
     * 生成验证码
     */
    private String generateCaptcha() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder captcha = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            captcha.append(chars.charAt(random.nextInt(chars.length())));
        }
        return captcha.toString();
    }
}
