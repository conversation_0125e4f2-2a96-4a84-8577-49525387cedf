package com.cf.financing.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpSession;
import java.util.Random;

/**
 * Web控制器 - 处理页面路由
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Controller
public class WebController {

    /**
     * 首页重定向到登录页
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage(Model model, HttpSession session) {
        // 生成验证码
        String captcha = generateCaptcha();
        session.setAttribute("captcha", captcha);
        model.addAttribute("captcha", captcha);
        return "login";
    }

    /**
     * 处理登录请求
     */
    @PostMapping("/login")
    public String login(
            @RequestParam String username,
            @RequestParam String password,
            @RequestParam String captcha,
            @RequestParam(required = false) String rememberMe,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        // 验证验证码
        String sessionCaptcha = (String) session.getAttribute("captcha");
        if (sessionCaptcha == null || !sessionCaptcha.equalsIgnoreCase(captcha)) {
            model.addAttribute("error", "验证码不正确");
            model.addAttribute("username", username);
            String newCaptcha = generateCaptcha();
            session.setAttribute("captcha", newCaptcha);
            model.addAttribute("captcha", newCaptcha);
            return "login";
        }

        // 这里应该调用用户服务进行认证
        // 暂时使用硬编码的用户名密码进行测试
        if ("admin".equals(username) && "123456".equals(password)) {
            session.setAttribute("user", username);
            redirectAttributes.addFlashAttribute("success", "登录成功");
            return "redirect:/main";
        } else {
            model.addAttribute("error", "用户名或密码错误");
            model.addAttribute("username", username);
            String newCaptcha = generateCaptcha();
            session.setAttribute("captcha", newCaptcha);
            model.addAttribute("captcha", newCaptcha);
            return "login";
        }
    }

    /**
     * 首页仪表板
     */
    @GetMapping("/dashboard")
    public String dashboard(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        model.addAttribute("user", user);
        return "dashboard";
    }

    /**
     * 首页仪表板
     */
    @GetMapping("/main")
    public String main(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        model.addAttribute("user", user);
        return "main";
    }

    /**
     * 案池页面
     */
    @GetMapping("/case-pool")
    public String casePool(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        model.addAttribute("user", user);
        return "case-pool";
    }

    /**
     * 还款管理页面
     */
    @GetMapping("/repayment")
    public String repayment(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        model.addAttribute("user", user);
        return "repayment";
    }

    /**
     * 任务预测页面
     */
    @GetMapping("/task-prediction")
    public String taskPrediction(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        model.addAttribute("user", user);
        return "task-prediction";
    }

    /**
     * 换单查询页面
     */
    @GetMapping("/exchange-query")
    public String exchangeQuery(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        model.addAttribute("user", user);
        return "exchange-query";
    }

    /**
     * 新增统计页面
     */
    @GetMapping("/new-statistics")
    public String newStatistics(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        model.addAttribute("user", user);
        return "new-statistics";
    }

    /**
     * 登出
     */
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate();
        return "redirect:/login";
    }

    /**
     * 生成验证码
     */
    private String generateCaptcha() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder captcha = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            captcha.append(chars.charAt(random.nextInt(chars.length())));
        }
        return captcha.toString();
    }
}
