package com.cf.financing.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpSession;
import java.util.Random;

/**
 * Web控制器 - 处理页面路由
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Controller
public class WebController {

    /**
     * 首页重定向到登录页
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage(Model model, HttpSession session) {
        // 生成验证码
        String captcha = generateCaptcha();
        session.setAttribute("captcha", captcha);
        model.addAttribute("captcha", captcha);
        return "login";
    }

    // 移除自定义登录处理，使用Spring Security默认处理

    /**
     * 首页仪表板
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        return "dashboard";
    }

    /**
     * 主页面
     */
    @GetMapping("/main")
    public String main(Model model) {
        // Spring Security会自动处理认证，这里直接返回main页面
        return "main";
    }

    /**
     * 案池页面
     */
    @GetMapping("/case-pool")
    public String casePool(Model model) {
        return "case-pool";
    }

    /**
     * 还款管理页面
     */
    @GetMapping("/repayment")
    public String repayment(Model model) {
        return "repayment";
    }

    /**
     * 任务预测页面
     */
    @GetMapping("/task-prediction")
    public String taskPrediction(Model model) {
        return "task-prediction";
    }

    /**
     * 换单查询页面
     */
    @GetMapping("/exchange-query")
    public String exchangeQuery(Model model) {
        return "exchange-query";
    }

    /**
     * 新增统计页面
     */
    @GetMapping("/new-statistics")
    public String newStatistics(Model model) {
        return "new-statistics";
    }

    // 登出由Spring Security自动处理

    /**
     * 生成验证码
     */
    private String generateCaptcha() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder captcha = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            captcha.append(chars.charAt(random.nextInt(chars.length())));
        }
        return captcha.toString();
    }
}
