package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.Statistics;
import com.cf.financing.mapper.StatisticsMapper;
import com.cf.financing.service.IStatisticsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统计信息服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Service
public class StatisticsServiceImpl extends ServiceImpl<StatisticsMapper, Statistics> implements IStatisticsService {

    @Override
    public IPage<Statistics> getStatisticsPage(Page<Statistics> page,
                                              Integer statType,
                                              Long departmentId,
                                              Long userId,
                                              LocalDate startDate,
                                              LocalDate endDate) {
        return baseMapper.selectStatisticsPage(page, statType, departmentId, userId, startDate, endDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean generateDailyStatistics(LocalDate statDate, Long userId) {
        try {
            int count = baseMapper.generateDailyStatistics(statDate, userId);
            return count > 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean generateMonthlyStatistics(Integer year, Integer month, Long userId) {
        try {
            int count = baseMapper.generateMonthlyStatistics(year, month, userId);
            return count > 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoGenerateStatistics(LocalDate statDate) {
        try {
            // 生成日统计
            generateDailyStatistics(statDate, null);
            
            // 如果是月末，生成月统计
            LocalDate nextDay = statDate.plusDays(1);
            if (nextDay.getDayOfMonth() == 1) {
                generateMonthlyStatistics(statDate.getYear(), statDate.getMonthValue(), null);
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getDepartmentStatistics(LocalDate startDate,
                                                             LocalDate endDate,
                                                             Integer statType) {
        return baseMapper.selectDepartmentStatistics(startDate, endDate, statType);
    }

    @Override
    public List<Map<String, Object>> getUserRanking(LocalDate startDate,
                                                   LocalDate endDate,
                                                   String orderBy,
                                                   Integer limit) {
        return baseMapper.selectUserRanking(startDate, endDate, orderBy, limit);
    }

    @Override
    public List<Map<String, Object>> getTrendData(LocalDate startDate,
                                                 LocalDate endDate,
                                                 Integer statType,
                                                 Long userId,
                                                 Long departmentId) {
        return baseMapper.selectTrendData(startDate, endDate, statType, userId, departmentId);
    }

    @Override
    public Map<String, Object> getRealTimeStatistics(LocalDate statDate,
                                                     Long userId,
                                                     Long departmentId) {
        return baseMapper.selectRealTimeStatistics(statDate, userId, departmentId);
    }

    @Override
    public Map<String, Object> getComparisonData(LocalDate currentStartDate,
                                                LocalDate currentEndDate,
                                                LocalDate previousStartDate,
                                                LocalDate previousEndDate,
                                                Long userId,
                                                Long departmentId) {
        return baseMapper.selectComparisonData(currentStartDate, currentEndDate,
                previousStartDate, previousEndDate, userId, departmentId);
    }

    @Override
    public String exportStatistics(LocalDate startDate,
                                  LocalDate endDate,
                                  Integer statType,
                                  Long departmentId,
                                  Long userId) {
        // 这里应该实现Excel导出逻辑
        // 为了简化，返回一个模拟的文件路径
        String fileName = "statistics_" + startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")) +
                "_" + endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
        return "/exports/" + fileName;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStatisticsByDateRange(LocalDate startDate,
                                              LocalDate endDate,
                                              Integer statType) {
        try {
            int count = baseMapper.deleteStatisticsByDateRange(startDate, endDate, statType);
            return count >= 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recalculateStatistics(LocalDate startDate,
                                        LocalDate endDate,
                                        Long userId) {
        try {
            // 先删除现有数据
            deleteStatisticsByDateRange(startDate, endDate, null);
            
            // 重新生成统计数据
            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                generateDailyStatistics(currentDate, userId);
                currentDate = currentDate.plusDays(1);
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Map<String, Object> getStatisticsOverview(Long userId, Long departmentId) {
        Map<String, Object> overview = new HashMap<>();
        
        // 获取今日实时统计
        Map<String, Object> todayStats = getRealTimeStatistics(LocalDate.now(), userId, departmentId);
        overview.put("today", todayStats);
        
        // 获取本月统计
        LocalDate monthStart = LocalDate.now().withDayOfMonth(1);
        LocalDate monthEnd = LocalDate.now();
        List<Map<String, Object>> monthTrend = getTrendData(monthStart, monthEnd, 1, userId, departmentId);
        overview.put("monthTrend", monthTrend);
        
        // 获取用户排行（前10名）
        List<Map<String, Object>> userRanking = getUserRanking(monthStart, monthEnd, "recovered_amount", 10);
        overview.put("userRanking", userRanking);
        
        // 获取对比数据（本月vs上月）
        LocalDate lastMonthStart = monthStart.minusMonths(1);
        LocalDate lastMonthEnd = monthStart.minusDays(1);
        Map<String, Object> comparison = getComparisonData(monthStart, monthEnd,
                lastMonthStart, lastMonthEnd, userId, departmentId);
        overview.put("comparison", comparison);
        
        return overview;
    }
}