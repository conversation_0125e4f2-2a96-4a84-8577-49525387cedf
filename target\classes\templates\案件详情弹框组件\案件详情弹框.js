/**
 * 通用弹框组件
 * @param {Object} options - 弹框配置选项
 * @param {string} options.title - 弹框标题
 * @param {number} options.width - 弹框宽度(px)
 * @param {number} options.height - 弹框高度(px)
 * @param {string} options.className - 自定义样式类名
 * @param {function} options.renderContent - 内容渲染函数
 * @param {function} options.onClose - 关闭回调函数
 * @param {boolean} options.closable - 是否可关闭
 * @param {Array} options.pagination - 分页配置
 * @param {number} options.pagination.total - 总记录数
 * @param {number} options.pagination.pageSize - 每页记录数
 * @param {function} options.pagination.onPageChange - 页码变化回调
 */
class Modal {
  constructor(options) {
    this.options = {
      title: '弹框标题',
      width: 600,
      height: 400,
      className: '',
      renderContent: () => '',
      onClose: () => {},
      closable: true,
      pagination: null,
      ...options
    };
    
    this.currentPage = 1;
    this.init();
  }
  
  // 初始化弹框
  init() {
    this.createElements();
    this.render();
    this.bindEvents();
  }
  
  // 创建DOM元素
  createElements() {
    // 遮罩层
    this.overlay = document.createElement('div');
    this.overlay.className = 'modal-overlay fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
    
    // 弹框容器
    this.container = document.createElement('div');
    this.container.className = `modal-container bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300 ${this.options.className}`;
    this.container.style.width = `${this.options.width}px`;
    this.container.style.maxWidth = '90%';
    
    // 弹框头部
    this.header = document.createElement('div');
    this.header.className = 'modal-header bg-primary text-white px-6 py-3 flex justify-between items-center';
    
    // 标题
    this.titleElement = document.createElement('h3');
    this.titleElement.className = 'text-lg font-semibold';
    this.titleElement.textContent = this.options.title;
    
    // 关闭按钮
    this.closeButton = document.createElement('button');
    this.closeButton.className = 'text-white hover:text-gray-200 focus:outline-none';
    this.closeButton.innerHTML = '<i class="fa fa-times"></i>';
    
    // 内容区域
    this.content = document.createElement('div');
    this.content.className = 'modal-content p-4 overflow-auto';
    this.content.style.height = `${this.options.height - 80}px`; // 减去头部和底部高度
    
    // 底部区域
    this.footer = document.createElement('div');
    this.footer.className = 'modal-footer px-6 py-3 bg-gray-50 flex justify-end space-x-2';
    
    // 关闭按钮(底部)
    this.confirmButton = document.createElement('button');
    this.confirmButton.className = 'px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors';
    this.confirmButton.textContent = '关闭';
    
    // 分页控件(可选)
    this.paginationContainer = document.createElement('div');
    this.paginationContainer.className = 'flex items-center justify-between w-full';
    
    // 总记录数
    this.totalCountElement = document.createElement('span');
    this.totalCountElement.className = 'text-sm text-gray-600';
    
    // 分页按钮组
    this.paginationButtons = document.createElement('div');
    this.paginationButtons.className = 'flex space-x-1';
  }
  
  // 渲染弹框内容
  render() {
    // 构建头部
    this.header.appendChild(this.titleElement);
    if (this.options.closable) {
      this.header.appendChild(this.closeButton);
    }
    
    // 渲染自定义内容
    const content = this.options.renderContent({
      page: this.currentPage,
      pageSize: this.options.pagination?.pageSize || 10
    });
    
    if (typeof content === 'string') {
      this.content.innerHTML = content;
    } else if (content instanceof HTMLElement) {
      this.content.innerHTML = '';
      this.content.appendChild(content);
    }
    
    // 构建底部
    if (this.options.pagination) {
      this.renderPagination();
      this.footer.appendChild(this.paginationContainer);
    } else {
      this.footer.appendChild(this.confirmButton);
    }
    
    // 构建弹框
    this.container.appendChild(this.header);
    this.container.appendChild(this.content);
    this.container.appendChild(this.footer);
    this.overlay.appendChild(this.container);
    
    // 添加到文档
    document.body.appendChild(this.overlay);
    
    // 动画效果
    setTimeout(() => {
      this.container.classList.add('scale-100');
      this.container.classList.remove('scale-95', 'opacity-0');
    }, 10);
  }
  
  // 渲染分页控件
  renderPagination() {
    const { total, pageSize } = this.options.pagination;
    const totalPages = Math.ceil(total / pageSize);
    
    // 更新总记录数显示
    this.totalCountElement.textContent = `共 ${total} 条记录`;
    
    // 清空分页按钮
    this.paginationButtons.innerHTML = '';
    
    // 添加页码按钮
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(startPage + maxVisiblePages - 1, totalPages);
    
    // 调整起始页，确保总显示5个页码
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    // 上一页按钮
    const prevButton = document.createElement('button');
    prevButton.className = `px-3 py-1 rounded border ${this.currentPage === 1 ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-600 hover:bg-gray-50'}`;
    prevButton.innerHTML = '<i class="fa fa-angle-left"></i>';
    prevButton.disabled = this.currentPage === 1;
    prevButton.addEventListener('click', () => this.goToPage(this.currentPage - 1));
    this.paginationButtons.appendChild(prevButton);
    
    // 第一页按钮
    if (startPage > 1) {
      this.addPageButton(1);
      
      if (startPage > 2) {
        const ellipsis = document.createElement('span');
        ellipsis.className = 'px-2 py-1';
        ellipsis.textContent = '...';
        this.paginationButtons.appendChild(ellipsis);
      }
    }
    
    // 中间页码
    for (let i = startPage; i <= endPage; i++) {
      this.addPageButton(i);
    }
    
    // 最后一页按钮
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        const ellipsis = document.createElement('span');
        ellipsis.className = 'px-2 py-1';
        ellipsis.textContent = '...';
        this.paginationButtons.appendChild(ellipsis);
      }
      
      this.addPageButton(totalPages);
    }
    
    // 下一页按钮
    const nextButton = document.createElement('button');
    nextButton.className = `px-3 py-1 rounded border ${this.currentPage === totalPages ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 text-gray-600 hover:bg-gray-50'}`;
    nextButton.innerHTML = '<i class="fa fa-angle-right"></i>';
    nextButton.disabled = this.currentPage === totalPages;
    nextButton.addEventListener('click', () => this.goToPage(this.currentPage + 1));
    this.paginationButtons.appendChild(nextButton);
    
    // 添加到容器
    this.paginationContainer.innerHTML = '';
    this.paginationContainer.appendChild(this.totalCountElement);
    this.paginationContainer.appendChild(this.paginationButtons);
  }
  
  // 添加页码按钮
  addPageButton(pageNum) {
    const button = document.createElement('button');
    button.className = `px-3 py-1 rounded border ${pageNum === this.currentPage ? 'border-primary bg-primary text-white' : 'border-gray-300 text-gray-600 hover:bg-gray-50'}`;
    button.textContent = pageNum;
    button.addEventListener('click', () => this.goToPage(pageNum));
    this.paginationButtons.appendChild(button);
  }
  
  // 跳转到指定页码
  goToPage(pageNum) {
    if (pageNum < 1 || pageNum > Math.ceil(this.options.pagination.total / this.options.pagination.pageSize)) {
      return;
    }
    
    this.currentPage = pageNum;
    this.renderPagination();
    
    // 触发页码变化回调
    if (typeof this.options.pagination.onPageChange === 'function') {
      this.options.pagination.onPageChange(pageNum);
    }
    
    // 重新渲染内容
    const content = this.options.renderContent({
      page: this.currentPage,
      pageSize: this.options.pagination.pageSize
    });
    
    if (typeof content === 'string') {
      this.content.innerHTML = content;
    } else if (content instanceof HTMLElement) {
      this.content.innerHTML = '';
      this.content.appendChild(content);
    }
  }
  
  // 绑定事件
  bindEvents() {
    // 关闭按钮事件
    if (this.options.closable) {
      this.closeButton.addEventListener('click', () => this.close());
      this.confirmButton.addEventListener('click', () => this.close());
    }
    
    // 点击遮罩层关闭
    this.overlay.addEventListener('click', (e) => {
      if (e.target === this.overlay && this.options.closable) {
        this.close();
      }
    });
  }
  
  // 关闭弹框
  close() {
    this.container.classList.remove('scale-100');
    this.container.classList.add('scale-95', 'opacity-0');
    
    setTimeout(() => {
      if (this.overlay && this.overlay.parentNode) {
        this.overlay.parentNode.removeChild(this.overlay);
      }
      
      // 触发关闭回调
      if (typeof this.options.onClose === 'function') {
        this.options.onClose();
      }
    }, 300);
  }
  
  // 更新内容
  updateContent(content) {
    if (typeof content === 'string') {
      this.content.innerHTML = content;
    } else if (content instanceof HTMLElement) {
      this.content.innerHTML = '';
      this.content.appendChild(content);
    }
  }
  
  // 更新分页数据
  updatePagination(total, currentPage = 1) {
    if (this.options.pagination) {
      this.options.pagination.total = total;
      this.currentPage = currentPage;
      this.renderPagination();
    }
  }
}

// 导出模块
export default Modal;    