<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CF-作业系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #165DFF;
            --secondary-color: #FF7D00;
            --success-color: #00B42A;
            --warning-color: #FF7D00;
            --error-color: #F53F3F;
            --info-color: #86909C;
            --sidebar-bg: #0F172A;
            --sidebar-item: #64748B;
            --sidebar-item-hover: #CBD5E1;
            --sidebar-item-active: #FFFFFF;
            --content-bg: #F8FAFC;
            --nav-bg: #1E293B;
            --breadcrumb-bg: #E2E8F0;
        }

        .container {
            display: flex;
            height: 100vh;
            flex-direction: column;
            overflow: hidden;
        }

        .nav-bar-1 {
            width: 100%;
            height: 64px;
            background-color: var(--nav-bg);
            display: flex;
            z-index: 100;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logo-area {
            width: 280px;
            height: 100%;
            background-color: var(--sidebar-bg);
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-right: 1px solid rgba(255,255,255,0.1);
        }

        .logo-image {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo-image i {
            color: white;
            font-size: 24px;
        }

        .company-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--sidebar-item-active);
            letter-spacing: 0.5px;
        }

        .system-messages {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 0 20px;
            overflow: hidden;
            position: relative;
        }

        .messages-container {
            flex: 1;
            height: 32px;
            overflow: hidden;
            position: relative;
        }

        .message-list {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .message-item {
            height: 32px;
            line-height: 32px;
            color: var(--sidebar-item-active);
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 10px;
            border-radius: 4px;
        }

        .message-info {
            background-color: rgba(22, 93, 255, 0.2);
        }

        .message-warning {
            background-color: rgba(255, 125, 0, 0.2);
        }

        .user-info {
            width: 300px;
            height: 100%;
            background-color: var(--sidebar-bg);
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 20px;
            color: var(--sidebar-item-active);
            border-left: 1px solid rgba(255,255,255,0.1);
            position: relative;
        }

        .greeting-container {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }

        .notification-bell {
            margin-right: 25px;  /* 铃铛左移5px */
            font-size: 24px;
            cursor: pointer;
            position: relative;
            transition: transform 0.3s;
            color: white;
        }

        .notification-bell:hover {
            transform: scale(1.2);
        }

        .notification-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #F53F3F;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
        }

        .notification-panel {
            position: fixed;
            top: 64px;
            right: -320px;
            width: 320px;
            height: calc(100vh - 64px);
            background-color: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s;
            z-index: 1000;
            overflow-y: auto;
        }

        .notification-panel.active {
            right: 0;
        }

        .notification-header {
            padding: 16px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
        }

        .close-notification {
            cursor: pointer;
            color: #64748B;
        }

        .close-notification:hover {
            color: #F53F3F;
        }

        .notification-list {
            padding: 16px;
        }

        .notification-item {
            padding: 12px;
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .notification-item:hover {
            background-color: #f8fafc;
        }

        .notification-item.unread {
            background-color: rgba(22, 93, 255, 0.05);
        }

        .notification-time {
            font-size: 12px;
            color: #86909C;
            margin-bottom: 4px;
        }

        .notification-content {
            font-size: 14px;
            color: #333333;
        }

        .user-avatar {
            font-size: 32px !important;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .user-avatar:hover {
            transform: scale(1.2);
        }

        .nav-bar-2 {
            width: calc(100% - 280px);
            height: 40px;
            background-color: var(--breadcrumb-bg);
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #333333;
            margin-left: 280px;
            z-index: 99;
            padding: 0 10px;
            transition: all 0.3s;
            border-bottom: 1px solid #CBD5E1;
        }

        .sidebar-toggle {
            margin-right: 10px;
            cursor: pointer;
            padding: 4px 8px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
            display: flex;
            align-items: center;
            transition: all 0.2s;
        }

        .sidebar {
            width: 280px;
            height: calc(100vh - 64px);
            background-color: var(--sidebar-bg);
            position: fixed;
            top: 64px;
            left: 0;
            overflow-y: auto;
            transition: transform 0.3s;
            z-index: 98;
        }

        .sidebar.collapsed {
            transform: translateX(-280px);
        }

        .menu-item {
            padding: 14px 24px;
            cursor: pointer;
            transition: all 0.2s;
            color: var(--sidebar-item);
            display: flex;
            align-items: center;
            border-left: 3px solid transparent;
            text-decoration: none;
        }

        .menu-item:hover {
            background-color: rgba(100, 116, 139, 0.1);
            color: var(--sidebar-item-hover);
        }

        .menu-item.active {
            background-color: rgba(22, 93, 255, 0.1);
            color: var(--sidebar-item-active);
            border-left-color: var(--primary-color);
        }

        .menu-item i {
            width: 24px;
            margin-right: 12px;
            font-size: 16px;
            text-align: center;
        }

        .content {
            flex: 1;
            margin-left: 270px;
            background-color: var(--content-bg);
            padding: 15px;  /* 调整内容区边距 */
            transition: all 0.3s;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }

        .content.expanded {
            margin-left: 0;
            width: 100%;
        }

        .page-container {
            padding: 15px;  /* 调整页面内容边距 */
            height: 100%;  /* 修复高度计算问题 */
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            overflow: auto;
            display: none;
        }

        .page-container.active {
            display: block;
        }

        .profile-form {
            max-width: 500px;
            margin: 20px auto;
            padding: 24px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close-btn {
            cursor: pointer;
            font-size: 20px;
            color: var(--info-color);
            transition: color 0.3s;
        }

        .close-btn:hover {
            color: var(--error-color);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--sidebar-item);
        }

        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
        }

        input[readonly] {
            background: #f8fafc;
            cursor: not-allowed;
        }

        button[type="submit"] {
            background-color: var(--primary-color);
            color: white;
            padding: 8px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button[type="submit"]:hover {
            background-color: #1146d6;
        }

        .tab-nav {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 20px;
        }

        .tab-item {
            padding: 6px 12px;
            background: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            border: 1px solid #e2e8f0;
            transition: all 0.2s;
        }

        .tab-item.active {
            border-color: var(--primary-color);
            background: rgba(22, 93, 255, 0.1);
        }

        .tab-close {
            color: var(--info-color);
            padding-left: 8px;
            margin-left: 8px;
            border-left: 1px solid #e2e8f0;
            transition: all 0.2s;
        }

        .tab-close:hover {
            color: var(--error-color);
            transform: scale(1.1);
        }

        /* 首页特定样式 */
        .dashboard-content {
            background: linear-gradient(135deg, #0c2461 0%, #1e3799 100%);
            color: #f0f2f5;
            padding: 20px;
            border-radius: 8px;
            height: 100%;
            overflow-y: auto;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(90deg, #f6b93b, #fad390);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-title i {
            font-size: 26px;
            color: #f6b93b;
            background: rgba(12, 36, 97, 0.7);
            padding: 10px;
            border-radius: 50%;
        }

        .date-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        /* 顶部数据概览 */
        .data-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .overview-card {
            padding: 16px;
            border-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(6px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: 110px;
        }

        .overview-card.primary {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(99, 102, 241, 0.25));
        }

        .overview-card.warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.25), rgba(249, 115, 22, 0.25));
        }

        .overview-card.success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.25), rgba(52, 211, 153, 0.25));
        }

        .overview-card.info {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.25), rgba(14, 165, 233, 0.25));
        }

        .metric {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .metric .label {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.85);
        }

        .metric .value {
            font-size: 20px;
            font-weight: 700;
        }

        .metric .trend {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            width: fit-content;
        }

        .trend.up {
            background: rgba(39, 174, 96, 0.25);
            color: #27ae60;
        }

        .trend.down {
            background: rgba(231, 76, 60, 0.25);
            color: #e74c3c;
        }

        .icon i {
            font-size: 28px;
            opacity: 0.7;
        }

        /* 主体内容区 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        .left-column, .right-column {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 面板通用样式 */
        .panel {
            background: rgba(20, 40, 100, 0.45);
            border-radius: 14px;
            padding: 16px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(6px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            height: 300px;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #f6b93b;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border-radius: 8px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn.active {
            background: #f6b93b;
            color: #0c2461;
        }

        select {
            padding: 6px 12px;
            border-radius: 8px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
        }

        /* 面板内容区域 */
        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 3px;
        }

        /* 滚动条样式 */
        .panel-content::-webkit-scrollbar {
            width: 8px;
        }

        .panel-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        .panel-content::-webkit-scrollbar-thumb {
            background: rgba(246, 185, 59, 0.6);
            border-radius: 4px;
        }

        .panel-content::-webkit-scrollbar-thumb:hover {
            background: rgba(246, 185, 59, 0.8);
        }

        /* 排行榜样式 */
        .ranking-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .top-performer, .performer {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.2s ease;
        }

        .top-performer {
            background: rgba(246, 185, 59, 0.15);
            border-left: 4px solid #f6b93b;
        }

        .top-performer:hover, .performer:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .rank {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            background: rgba(12, 36, 97, 0.7);
            color: #f6b93b;
            margin-right: 12px;
        }

        .top-performer .rank {
            background: #f6b93b;
            color: #0c2461;
        }

        .user-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        .details {
            line-height: 1.3;
        }

        .name {
            font-weight: 600;
            font-size: 15px;
        }

        .team {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .performance-data {
            min-width: 120px;
            text-align: right;
        }

        .amount {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 15px;
        }

        .progress-container {
            position: relative;
            height: 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #34d399);
            border-radius: 8px;
            transition: width 0.5s ease;
        }

        .progress-container span {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 11px;
            color: white;
            z-index: 1;
        }

        /* 图表容器 */
        .chart-container {
            height: 100%;
            position: relative;
        }

        /* 案件分布表格样式 */
        .case-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            margin-top: -10px;
        }

        .case-table th {
            text-align: left;
            padding: 12px;
            background-color: #0c2461;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: -10px;
            z-index: 20;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .case-table td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.9);
        }

        .case-table tr:last-child td {
            border-bottom: none;
        }

        .case-table .highlight {
            color: #f6b93b;
            font-weight: 600;
        }

        .case-table .progress-cell {
            width: 40%;
        }

        .case-table .progress-bar {
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            margin-top: 5px;
        }

        .case-table .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #6366f1);
            border-radius: 5px;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-bar-1">
            <div class="logo-area">
                <div class="logo-image">
                    <i class="fa fa-bank"></i>
                </div>
                <div class="company-name">CF-作业系统</div>
            </div>
            
            <div class="system-messages">
                <div class="messages-container">
                    <div class="message-list">
                        <div class="message-item message-info">系统公告：系统运行正常</div>
                        <div class="message-item message-warning">警告：今日有3个待处理事项</div>
                    </div>
                </div>
            </div>
            
            <div class="user-info">
                <div class="greeting-container">
                    <div class="notification-bell" onclick="toggleNotificationPanel()">
                        <i class="fa fa-bell"></i>
                        <span class="notification-count" id="notificationCount">3</span>
                    </div>
                    <span class="greeting-text" id="greetingText"></span>
                    <i class="fa fa-user-circle-o user-avatar" onclick="showProfile()"></i>
                </div>
                <div class="user-details">
                    <div class="user-name" th:text="${user != null ? user : '用户'}">用户</div>
                    <div class="user-id">在线</div>
                </div>
            </div>
        </div>

        <!-- 通知面板 -->
        <div class="notification-panel" id="notificationPanel">
            <div class="notification-header">
                <div class="notification-title">系统通知</div>
                <div class="close-notification" onclick="toggleNotificationPanel()">
                    <i class="fa fa-times"></i>
                </div>
            </div>
            <div class="notification-list" id="notificationList">
                <div class="notification-item unread">
                    <div class="notification-time">刚刚</div>
                    <div class="notification-content">您有3个待处理的还款提醒</div>
                </div>
                <div class="notification-item unread">
                    <div class="notification-time">10分钟前</div>
                    <div class="notification-content">系统检测到异常登录尝试</div>
                </div>
                <div class="notification-item unread">
                    <div class="notification-time">今天 09:30</div>
                    <div class="notification-content">您的个人信息需要更新</div>
                </div>
                <div class="notification-item">
                    <div class="notification-time">昨天 16:45</div>
                    <div class="notification-content">本月业绩目标已完成80%</div>
                </div>
                <div class="notification-item">
                    <div class="notification-time">2025-05-27</div>
                    <div class="notification-content">系统更新已完成</div>
                </div>
            </div>
        </div>

        <div class="nav-bar-2">
            <div class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fa fa-bars"></i>
            </div>
            <div class="tab-nav" id="tabNav"></div>
        </div>

        <div class="sidebar" id="sidebar">
            <div class="menu-items">
                <div class="menu-item active" data-id="home" onclick="showPage('home')">
                    <i class="fa fa-home"></i> 首页
                </div>
                <a href="/case-pool" class="menu-item" data-id="casepool">
                    <i class="fa fa-folder-open"></i> 案池管理
                </a>
                <a href="/repayment" class="menu-item" data-id="repayment">
                    <i class="fa fa-credit-card"></i> 还款管理
                </a>
                <div class="menu-item" data-id="tasklist" onclick="showPage('tasklist')">
                    <i class="fa fa-tasks"></i> 作业清单
                </div>
                <div class="menu-item" data-id="statistics" onclick="showPage('statistics')">
                    <i class="fa fa-bar-chart"></i> 数据统计
                </div>
                <div class="menu-item" data-id="prediction" onclick="showPage('prediction')">
                    <i class="fa fa-line-chart"></i> 任务预测
                </div>
                <div class="menu-item" data-id="orderchange" onclick="showPage('orderchange')">
                    <i class="fa fa-exchange"></i> 换单记录
                </div>
                <div class="menu-item" data-id="system" onclick="showPage('system')">
                    <i class="fa fa-cog"></i> 系统管理
                </div>
                <a href="/logout" class="menu-item" data-id="logout">
                    <i class="fa fa-sign-out"></i> 退出登录
                </a>
            </div>
        </div>

        <div class="content" id="content">
            <div id="page-home" class="page-container active">
                <div class="dashboard-content">
                    <!-- 首页头部 -->
                    <div class="dashboard-header">
                        <!-- <div class="header-title">
                            <i class="fas fa-chart-line"></i>
                            <h1>金融催收数据统计首页</h1>
                        </div> -->
                        <div class="date-info" id="currentDateTime">
                            2023年12月15日 星期五 14:30
                        </div>
                    </div>

                    <!-- 顶部数据概览 -->
                    <div class="data-overview">
                        <div class="overview-card primary">
                            <div class="metric">
                                <div class="label">总委托金额</div>
                                <div class="value" id="totalAmount">¥8.74M</div>
                                <div class="trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+12.5% 较上月</span>
                                </div>
                            </div>
                            <div class="icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>

                        <div class="overview-card warning">
                            <div class="metric">
                                <div class="label">总案件数</div>
                                <div class="value" id="totalCases">2,587</div>
                                <div class="trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+8.3% 较上月</span>
                                </div>
                            </div>
                            <div class="icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                        </div>

                        <div class="overview-card success">
                            <div class="metric">
                                <div class="label">今日回收</div>
                                <div class="value" id="todayRecovery">¥356.8K</div>
                                <div class="trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+15.2% 较昨日</span>
                                </div>
                            </div>
                            <div class="icon">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>

                        <div class="overview-card info">
                            <div class="metric">
                                <div class="label">平均回收率</div>
                                <div class="value" id="avgRecoveryRate">74.6%</div>
                                <div class="trend down">
                                    <i class="fas fa-arrow-down"></i>
                                    <span>-2.1% 较上月</span>
                                </div>
                            </div>
                            <div class="icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 主体内容区 -->
                    <div class="main-content">
                        <!-- 左侧内容 -->
                        <div class="left-column">
                            <!-- 业绩排行榜 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3 class="panel-title"><i class="fas fa-trophy"></i> 榜上有名</h3>
                                    <div class="panel-actions">
                                        <button class="btn active">个人</button>
                                        <button class="btn">团队</button>
                                        <select>
                                            <option>今日</option>
                                            <option>本周</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="panel-content">
                                    <div class="ranking-list" id="rankingList">
                                        <!-- 排行榜数据将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 回收趋势 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3 class="panel-title"><i class="fas fa-chart-line"></i> 回收趋势</h3>
                                    <div class="panel-actions">
                                        <button class="btn active">金额</button>
                                        <button class="btn">户数</button>
                                        <select>
                                            <option>近7天</option>
                                            <option>近30天</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="panel-content">
                                    <div class="chart-container">
                                        <canvas id="trendChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧内容 -->
                        <div class="right-column">
                            <!-- 团队绩效 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3 class="panel-title"><i class="fas fa-users"></i> 团队绩效</h3>
                                    <div class="panel-actions">
                                        <select>
                                            <option>本月</option>
                                            <option>本季度</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="panel-content">
                                    <div class="chart-container">
                                        <canvas id="teamChart"></canvas>
                                    </div>
                                </div>
                            </div>

                            <!-- 案件分布 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3 class="panel-title"><i class="fas fa-boxes"></i> 案件分布</h3>
                                    <div class="panel-actions">
                                        <button class="btn active">部门</button>
                                        <button class="btn">个人</button>
                                    </div>
                                </div>
                                <div class="panel-content">
                                    <table class="case-table" id="caseDistributionTable">
                                        <thead>
                                            <tr>
                                                <th>团队/个人</th>
                                                <th>在案户数</th>
                                                <th>委托金额</th>
                                                <th>完成进度</th>
                                            </tr>
                                        </thead>
                                        <tbody id="caseDistributionBody">
                                            <!-- 案件分布数据将通过JavaScript动态加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="page-profile" class="page-container">
                <div class="profile-form">
                    <div class="form-header">
                        <h3>个人信息管理</h3>
                        <i class="fa fa-times close-btn" onclick="closeProfile()"></i>
                    </div>
                    <form onsubmit="updateProfile(event)">
                        <div class="form-group">
                            <label>工号：</label>
                            <input type="text" value="10086" readonly>
                        </div>
                        <div class="form-group">
                            <label>姓名：</label>
                            <input type="text" value="张三" readonly>
                        </div>
                        <div class="form-group">
                            <label>部门：</label>
                            <input type="text" value="信贷管理部" readonly>
                        </div>
                        <div class="form-group">
                            <label>手机号码：</label>
                            <input type="tel" id="mobile" value="13800138000" required>
                        </div>
                        <div class="form-group">
                            <label>新密码：</label>
                            <input type="password" id="newPassword" placeholder="留空则不修改">
                        </div>
                        <div class="form-group">
                            <label>确认密码：</label>
                            <input type="password" id="confirmPassword" placeholder="再次输入新密码">
                        </div>
                        <button type="submit">保存修改</button>
                    </form>
                </div>
            </div>

            <div id="page-system" class="page-container"><h3>系统管理</h3></div>
            <div id="page-casepool" class="page-container"><h3>案池管理</h3></div>
            <div id="page-tasklist" class="page-container"><h3>作业清单</h3></div>
            <div id="page-repayment" class="page-container"><h3>还款管理</h3></div>
            <div id="page-statistics" class="page-container"><h3>数据统计</h3></div>
            <div id="page-prediction" class="page-container"><h3>任务预测</h3></div>
            <div id="page-orderchange" class="page-container"><h3>换单记录</h3></div>
            <div id="page-orderrequest" class="page-container"><h3>索单管理</h3></div>
        </div>
    </div>

    <script>
        // 系统状态管理
        let openedPages = ['home'];
        let currentPage = 'home';
        let isSidebarCollapsed = false;
        let notifications = [
            {id: 1, time: '刚刚', content: '您有3个待处理的还款提醒', read: false},
            {id: 2, time: '10分钟前', content: '系统检测到异常登录尝试', read: false},
            {id: 3, time: '今天 09:30', content: '您的个人信息需要更新', read: false},
            {id: 4, time: '昨天 16:45', content: '本月业绩目标已完成80%', read: true},
            {id: 5, time: '2025-05-27', content: '系统更新已完成', read: true}
        ];

        // 侧边栏切换
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            const navBar2 = document.querySelector('.nav-bar-2');
            
            isSidebarCollapsed = !isSidebarCollapsed;
            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');
            
            if (isSidebarCollapsed) {
                navBar2.style.width = '100%';
                navBar2.style.marginLeft = '0';
                content.style.width = 'calc(100% - 2px)';
            } else {
                navBar2.style.width = 'calc(100% - 280px)';
                navBar2.style.marginLeft = '280px';
                content.style.width = 'calc(100% - 280px - 2px)';
            }
        }

        // 页面切换
        function showPage(pageId) {
            currentPage = pageId;
            if (!openedPages.includes(pageId)) {
                openedPages.push(pageId);
            }

            document.querySelectorAll('.page-container').forEach(page => {
                page.classList.remove('active');
            });

            const targetPage = document.getElementById(`page-${pageId}`);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.id === pageId) {
                    item.classList.add('active');
                }
            });

            updateTabs();
        }

        // 个人信息管理
        function showProfile() {
            if (!openedPages.includes('profile')) {
                openedPages.push('profile');
            }
            currentPage = 'profile';
            updateTabs();
            document.querySelectorAll('.page-container').forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById('page-profile').classList.add('active');
        }

        function closeProfile() {
            const index = openedPages.indexOf('profile');
            if (index > -1) {
                openedPages.splice(index, 1);
            }
            showPage(openedPages[openedPages.length - 1]);
        }

        // 标签导航
        function updateTabs() {
            const tabNav = document.getElementById('tabNav');
            tabNav.innerHTML = '';
            
            openedPages.forEach(pageId => {
                const tab = document.createElement('div');
                tab.className = `tab-item ${currentPage === pageId ? 'active' : ''}`;
                tab.innerHTML = `
                    <span>${getPageName(pageId)}</span>
                    ${pageId !== 'home' ? `<i class="fa fa-times tab-close" onclick="event.stopPropagation();closeTab('${pageId}')"></i>` : ''}
                `;
                tab.onclick = () => showPage(pageId);
                tabNav.appendChild(tab);
            });
        }

        function closeTab(pageId) {
            if (pageId === 'home') return;
            
            const index = openedPages.indexOf(pageId);
            if (index > -1) {
                openedPages.splice(index, 1);
                
                if (currentPage === pageId) {
                    currentPage = openedPages[openedPages.length - 1];
                    showPage(currentPage);
                }
                
                if (pageId === 'profile') {
                    document.getElementById('page-profile').classList.remove('active');
                }
                
                updateTabs();
            }
        }

        // 通知功能
        function toggleNotificationPanel() {
            const panel = document.getElementById('notificationPanel');
            panel.classList.toggle('active');
            
            // 标记所有通知为已读
            if (panel.classList.contains('active')) {
                const unreadCount = notifications.filter(n => !n.read).length;
                if (unreadCount > 0) {
                    notifications.forEach(n => n.read = true);
                    document.getElementById('notificationCount').textContent = '0';
                    document.querySelectorAll('.notification-item.unread').forEach(item => {
                        item.classList.remove('unread');
                    });
                }
            }
        }

        // 初始化消息滚动
        function initMessageScroll() {
            const messageList = document.querySelector('.message-list');
            const items = messageList.children;
            
            // 初始位置设置
            messageList.style.transform = 'translateY(0)';
            let currentIndex = 0;
            
            setInterval(() => {
                currentIndex = (currentIndex + 1) % items.length;
                messageList.style.transform = `translateY(-${currentIndex * 32}px)`;
            }, 3000);
        }

        // 辅助函数
        function getPageName(pageId) {
            const pageNames = {
                home: '首页',
                profile: '个人信息',
                system: '系统管理',
                casepool: '案池管理',
                tasklist: '作业清单',
                repayment: '还款管理',
                statistics: '数据统计',
                prediction: '任务预测',
                orderchange: '换单记录',
                orderrequest: '索单管理'
            };
            return pageNames[pageId] || pageId;
        }

        function updateProfile(event) {
            event.preventDefault();
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword && newPassword !== confirmPassword) {
                alert('两次输入的密码不一致！');
                return;
            }

            alert('信息更新成功');
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        }

        function updateGreeting() {
            const hours = new Date().getHours();
            let greeting = '夜深了';
            if (hours < 6) greeting = '凌晨好';
            else if (hours < 9) greeting = '早上好';
            else if (hours < 12) greeting = '上午好';
            else if (hours < 14) greeting = '中午好';
            else if (hours < 18) greeting = '下午好';
            else if (hours < 22) greeting = '晚上好';
            document.getElementById('greetingText').textContent = greeting;
        }

        // 首页数据初始化
        function initDashboard() {
            updateDateTime();
            loadRankingData();
            initCharts();
            loadCaseDistribution();

            // 定时更新数据
            setInterval(updateDateTime, 1000);
            setInterval(updateDashboardData, 30000); // 30秒更新一次数据
        }

        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long',
                hour: '2-digit',
                minute: '2-digit'
            };
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                dateTimeElement.textContent = now.toLocaleDateString('zh-CN', options);
            }
        }

        function loadRankingData() {
            const rankingData = [
                { rank: 1, name: '张三', team: '催收一组', amount: '¥125.6K', progress: 95 },
                { rank: 2, name: '李四', team: '催收二组', amount: '¥98.3K', progress: 87 },
                { rank: 3, name: '王五', team: '催收一组', amount: '¥89.7K', progress: 82 },
                { rank: 4, name: '赵六', team: '催收三组', amount: '¥76.2K', progress: 78 },
                { rank: 5, name: '钱七', team: '催收二组', amount: '¥68.9K', progress: 74 }
            ];

            const rankingList = document.getElementById('rankingList');
            if (rankingList) {
                rankingList.innerHTML = rankingData.map(item => `
                    <div class="${item.rank === 1 ? 'top-performer' : 'performer'}">
                        <div class="rank">${item.rank}</div>
                        <div class="user-info">
                            <div class="avatar">${item.name.charAt(0)}</div>
                            <div class="details">
                                <div class="name">${item.name}</div>
                                <div class="team">${item.team}</div>
                            </div>
                        </div>
                        <div class="performance-data">
                            <div class="amount">${item.amount}</div>
                            <div class="progress-container">
                                <div class="progress" style="width: ${item.progress}%"></div>
                                <span>${item.progress}%</span>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        }

        function loadCaseDistribution() {
            const caseData = [
                { name: '催收一组', cases: 456, amount: '¥2.34M', progress: 78 },
                { name: '催收二组', cases: 389, amount: '¥1.98M', progress: 65 },
                { name: '催收三组', cases: 298, amount: '¥1.56M', progress: 72 },
                { name: '催收四组', cases: 234, amount: '¥1.23M', progress: 58 },
                { name: '催收五组', cases: 189, amount: '¥0.98M', progress: 69 }
            ];

            const tbody = document.getElementById('caseDistributionBody');
            if (tbody) {
                tbody.innerHTML = caseData.map(item => `
                    <tr>
                        <td class="highlight">${item.name}</td>
                        <td>${item.cases}</td>
                        <td>${item.amount}</td>
                        <td class="progress-cell">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${item.progress}%"></div>
                            </div>
                            ${item.progress}%
                        </td>
                    </tr>
                `).join('');
            }
        }

        function initCharts() {
            // 趋势图表
            const trendCtx = document.getElementById('trendChart');
            if (trendCtx) {
                new Chart(trendCtx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                        datasets: [{
                            label: '回收金额',
                            data: [320, 450, 380, 520, 460, 380, 420],
                            borderColor: '#f6b93b',
                            backgroundColor: 'rgba(246, 185, 59, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255,255,255,0.1)' },
                                ticks: { color: 'rgba(255,255,255,0.8)' }
                            },
                            x: {
                                grid: { color: 'rgba(255,255,255,0.1)' },
                                ticks: { color: 'rgba(255,255,255,0.8)' }
                            }
                        }
                    }
                });
            }

            // 团队绩效图表
            const teamCtx = document.getElementById('teamChart');
            if (teamCtx) {
                new Chart(teamCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['催收一组', '催收二组', '催收三组', '催收四组', '催收五组'],
                        datasets: [{
                            data: [30, 25, 20, 15, 10],
                            backgroundColor: [
                                '#f6b93b',
                                '#3b82f6',
                                '#10b981',
                                '#f59e0b',
                                '#ef4444'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: { color: 'rgba(255,255,255,0.8)' }
                            }
                        }
                    }
                });
            }
        }

        function updateDashboardData() {
            // 模拟数据更新
            const elements = {
                totalAmount: document.getElementById('totalAmount'),
                totalCases: document.getElementById('totalCases'),
                todayRecovery: document.getElementById('todayRecovery'),
                avgRecoveryRate: document.getElementById('avgRecoveryRate')
            };

            // 随机更新数据（实际项目中应该从API获取）
            if (elements.totalAmount) {
                const currentAmount = parseFloat(elements.totalAmount.textContent.replace(/[¥MK,]/g, ''));
                const newAmount = (currentAmount + (Math.random() - 0.5) * 0.1).toFixed(2);
                elements.totalAmount.textContent = `¥${newAmount}M`;
            }
        }

        // 初始化
        window.addEventListener('DOMContentLoaded', () => {
            updateGreeting();
            initMessageScroll();
            updateTabs();
            initDashboard(); // 初始化首页数据
        });
    </script>
</body>
</html>