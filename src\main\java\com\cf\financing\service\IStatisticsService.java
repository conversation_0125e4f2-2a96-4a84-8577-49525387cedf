package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.Statistics;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 统计信息服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface IStatisticsService extends IService<Statistics> {

    /**
     * 分页查询统计信息
     *
     * @param page 分页参数
     * @param statType 统计类型
     * @param departmentId 部门ID
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页结果
     */
    IPage<Statistics> getStatisticsPage(Page<Statistics> page,
                                       Integer statType,
                                       Long departmentId,
                                       Long userId,
                                       LocalDate startDate,
                                       LocalDate endDate);

    /**
     * 生成日统计数据
     *
     * @param statDate 统计日期
     * @param userId 用户ID（可选，为空则统计所有用户）
     * @return 是否成功
     */
    boolean generateDailyStatistics(LocalDate statDate, Long userId);

    /**
     * 生成月统计数据
     *
     * @param year 年份
     * @param month 月份
     * @param userId 用户ID（可选，为空则统计所有用户）
     * @return 是否成功
     */
    boolean generateMonthlyStatistics(Integer year, Integer month, Long userId);

    /**
     * 自动生成统计数据（定时任务调用）
     *
     * @param statDate 统计日期
     * @return 是否成功
     */
    boolean autoGenerateStatistics(LocalDate statDate);

    /**
     * 查询部门统计汇总
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型
     * @return 部门统计结果
     */
    List<Map<String, Object>> getDepartmentStatistics(LocalDate startDate,
                                                      LocalDate endDate,
                                                      Integer statType);

    /**
     * 查询用户排行榜
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param orderBy 排序字段
     * @param limit 限制数量
     * @return 用户排行结果
     */
    List<Map<String, Object>> getUserRanking(LocalDate startDate,
                                            LocalDate endDate,
                                            String orderBy,
                                            Integer limit);

    /**
     * 查询趋势数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型
     * @param userId 用户ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 趋势数据
     */
    List<Map<String, Object>> getTrendData(LocalDate startDate,
                                          LocalDate endDate,
                                          Integer statType,
                                          Long userId,
                                          Long departmentId);

    /**
     * 查询实时统计数据
     *
     * @param statDate 统计日期
     * @param userId 用户ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 实时统计结果
     */
    Map<String, Object> getRealTimeStatistics(LocalDate statDate,
                                              Long userId,
                                              Long departmentId);

    /**
     * 查询对比数据
     *
     * @param currentStartDate 当前期间开始日期
     * @param currentEndDate 当前期间结束日期
     * @param previousStartDate 对比期间开始日期
     * @param previousEndDate 对比期间结束日期
     * @param userId 用户ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 对比数据
     */
    Map<String, Object> getComparisonData(LocalDate currentStartDate,
                                         LocalDate currentEndDate,
                                         LocalDate previousStartDate,
                                         LocalDate previousEndDate,
                                         Long userId,
                                         Long departmentId);

    /**
     * 导出统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型
     * @param departmentId 部门ID（可选）
     * @param userId 用户ID（可选）
     * @return 导出文件路径
     */
    String exportStatistics(LocalDate startDate,
                           LocalDate endDate,
                           Integer statType,
                           Long departmentId,
                           Long userId);

    /**
     * 删除指定日期范围的统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型（可选）
     * @return 是否成功
     */
    boolean deleteStatisticsByDateRange(LocalDate startDate,
                                       LocalDate endDate,
                                       Integer statType);

    /**
     * 重新计算统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userId 用户ID（可选）
     * @return 是否成功
     */
    boolean recalculateStatistics(LocalDate startDate,
                                 LocalDate endDate,
                                 Long userId);

    /**
     * 获取统计概览数据
     *
     * @param userId 用户ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 概览数据
     */
    Map<String, Object> getStatisticsOverview(Long userId, Long departmentId);
}