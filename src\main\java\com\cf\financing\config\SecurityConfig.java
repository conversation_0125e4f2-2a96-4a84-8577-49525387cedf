package com.cf.financing.config;

import com.cf.financing.service.CustomUserDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    private CustomUserDetailsService userDetailsService;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf().disable()  // 禁用CSRF保护（开发环境）
            .authorizeHttpRequests((requests) -> requests
                .antMatchers("/", "/login", "/css/**", "/js/**", "/images/**", "/fonts/**",
                           "/api/**", "/druid/**", "/captcha/**", "/swagger-ui/**",
                           "/v3/api-docs/**", "/doc.html", "/dashboard", "/layout","/case-pool",
                           "/repayment", "/task-prediction", "/exchange-query",
                           "/new-statistics", "/logout", "/home").permitAll()
                .antMatchers("/dashboard").authenticated()
                .anyRequest().authenticated()
            )
            .formLogin((form) -> form
                .loginPage("/login")
                .defaultSuccessUrl("/dashboard", true)
                .failureUrl("/login?error")
                .permitAll()
            )
            .logout((logout) -> logout
                .logoutUrl("/logout")
                .logoutSuccessUrl("/login?logout")
                .permitAll())
            .headers(headers -> headers
                .frameOptions().sameOrigin() // 允许同源iframe嵌入
            )
            .userDetailsService(userDetailsService);

        return http.build();
    }
}
