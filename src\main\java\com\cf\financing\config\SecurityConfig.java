package com.cf.financing.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security 配置类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF保护
            .csrf().disable()
            
            // 配置CORS
            .cors().configurationSource(corsConfigurationSource())
            
            .and()
            
            // 配置会话管理为无状态
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS)

            .and()

            // 配置请求授权
            .authorizeRequests()
            // 允许登录页面和登录处理无需认证
            .antMatchers("/", "/login").permitAll()
            // 允许静态资源无需认证
            .antMatchers("/css/**", "/js/**", "/images/**", "/fonts/**").permitAll()
            // 允许API接口
            .antMatchers("/api/**").permitAll()
            // 允许Druid监控页面
            .antMatchers("/druid/**").permitAll()
            // 允许验证码刷新
            .antMatchers("/captcha/**").permitAll()
            // 允许Swagger相关路径（如果启用）
            .antMatchers("/swagger-ui/**", "/v3/api-docs/**", "/doc.html").permitAll()
            // 允许所有页面路由（暂时开放，后续可以根据需要调整）
            .antMatchers("/dashboard", "/case-pool", "/repayment", "/task-prediction", "/exchange-query", "/new-statistics", "/logout").permitAll()
            // 其他请求需要认证
            .anyRequest().authenticated()

            .and()

            // 禁用默认登录页面
            .formLogin().disable()

            // 禁用HTTP Basic认证
            .httpBasic().disable();
    }

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
