package com.cf.financing.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests((requests) -> requests
                .antMatchers("/", "/login", "/css/**", "/js/**", "/images/**", "/fonts/**",
                           "/api/**", "/druid/**", "/captcha/**", "/swagger-ui/**",
                           "/v3/api-docs/**", "/doc.html", "/dashboard", "/layout","/case-pool",
                           "/repayment", "/task-prediction", "/exchange-query",
                           "/new-statistics", "/logout").permitAll()
                .anyRequest().authenticated()
            )
            .formLogin((form) -> form
                .loginPage("/login")
                .permitAll()
            )
            .logout((logout) -> logout.permitAll());

        return http.build();
    }
}
