<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>还款管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 40px);
        }
        
        header {
            background: linear-gradient(135deg, #1a6dcc, #0d4a9e);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo i {
            font-size: 28px;
        }
        
        .logo h1 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4d9df8;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            color: white;
        }
        
        .filter-section {
            padding: 20px 30px;
            border-bottom: 1px solid #eaeff5;
            flex-shrink: 0;
        }
        
        .filter-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-title i {
            color: #1a6dcc;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }
        
        .filter-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1px;
        }
        
        .filter-item label {
            font-size: 14px;
            color: #5a6d80;
            width: 100px;
            flex-shrink: 0;
            font-weight: 500;
        }
        
        .filter-item input, .filter-item select {
            padding: 12px 12px;
            border: 1px solid #dce1e8;
            border-radius: 8px;
            font-size: 14px;
            background: #fafbfc;
            transition: all 0.3s;
            flex: 1;
            min-width: 0;
        }
        
        .filter-item input:focus, .filter-item select:focus {
            outline: none;
            border-color: #4d9df8;
            box-shadow: 0 0 0 3px rgba(77, 157, 248, 0.15);
        }
        
        /* 时间范围选择器 */
        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }
        
        .date-range input {
            flex: 1;
        }
        
        .date-range span {
            color: #7a8a9c;
            font-size: 13px;
            flex-shrink: 0;
        }
        
        .filter-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
        }
        
        .btn:hover::after {
            opacity: 1;
        }
        
        .btn-query {
            background: linear-gradient(135deg, #1a6dcc, #0d4a9e);
            color: white;
        }
        
        .btn-reset {
            background: #f1f4f8;
            color: #5a6d80;
        }
        
        .btn-query:hover {
            background: linear-gradient(135deg, #0d5cb6, #0a3d87);
            transform: translateY(-2px);
        }
        
        .btn-reset:hover {
            background: #e4e9f0;
        }
        
        .stats-section {
            padding: 10px 30px;
            background: #f9fbfd;
            border-bottom: 1px solid #eaeff5;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }
        
        .stats-overview {
            display: flex;
            gap: 30px;
        }
        
        .stat-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #5a6d80;
            font-weight: 500;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .stats-selected {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, #ff6b6b, #ff4757);
            color: white;
            padding: 8px 20px;
            font-size: 13px;
        }
        
        .btn-delete:hover {
            background: linear-gradient(135deg, #e55a5a, #e53e4e);
            transform: translateY(-2px);
        }
        
        .table-section {
            padding: 0 30px 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0 10px;
        }
        
        .table-title {
            font-size: 17px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .table-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn-export {
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            font-size: 13px;
        }
        
        .btn-export:hover {
            background: #219653;
        }
        
        .table-container {
            height: 300px;
            overflow: auto;
            border: 1px solid #eaeff5;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
            margin-top: 10px;
            flex-grow: 1;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        thead {
            background: linear-gradient(135deg, #f8fafd, #eef2f7);
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        th {
            padding: 14px 15px;
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            border-bottom: 2px solid #eaeff5;
        }
        
        tbody tr {
            border-bottom: 1px solid #eaeff5;
            transition: background 0.2s;
        }
        
        tbody tr:hover {
            background: #f9fbfd;
        }
        
        td {
            padding: 12px 15px;
            font-size: 13px;
            color: #4a5a6a;
            text-align: center;
        }
        
        .checkbox-cell {
            width: 50px;
            text-align: center;
        }
        
        .action-cell {
            text-align: center;
            width: 100px;
        }
        
        .action-cell a {
            text-decoration: none;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
        }
        
        .action-cell .view-link {
            color: #1a6dcc;
            background: #e6f2ff;
        }
        
        .action-cell .view-link:hover {
            background: #d4e5ff;
            transform: scale(1.1);
        }
        
        .action-cell .delete-link {
            color: #ff6b6b;
            background: #ffebee;
        }
        
        .action-cell .delete-link:hover {
            background: #ffd4d9;
            transform: scale(1.1);
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 15px;
            gap: 8px;
            padding: 0 10px;
        }
        
        .pagination button {
            padding: 6px 12px;
            background: #f1f4f8;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 13px;
        }
        
        .pagination button:hover {
            background: #e4e9f0;
        }
        
        .pagination button.active {
            background: #1a6dcc;
            color: white;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 40px;
            color: #7a8a9c;
            text-align: center;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dce1e8;
        }
        
        .empty-state p {
            font-size: 16px;
            max-width: 400px;
            line-height: 1.6;
        }
        
        @media (max-width: 1200px) {
            .filter-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .filter-item label {
                width: 80px;
            }
        }
        
        @media (max-width: 900px) {
            .filter-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .stats-overview {
                flex-direction: column;
                gap: 10px;
            }
            
            .stats-selected {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
        
        @media (max-width: 600px) {
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .stats-section {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .table-actions {
                flex-direction: column;
                width: 100%;
            }
            
            .table-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .filter-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .filter-item label {
                width: auto;
                margin-bottom: 5px;
            }
            
            .filter-item input, 
            .filter-item select {
                width: 100%;
            }
            
            .date-range {
                flex-direction: column;
                align-items: stretch;
                gap: 5px;
            }
            
            .date-range span {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        
        <section class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                <span>筛选条件</span>
            </div>
            
            <div class="filter-grid">
                <div class="filter-item">
                    <label>作业员/组织</label>
                    <input type="text" placeholder="请输入作业员或组织">
                </div>
                
                <div class="filter-item">
                    <label>委托机构</label>
                    <select>
                        <option>全部机构</option>
                        <option>中国工商银行</option>
                        <option>中国建设银行</option>
                        <option>中国银行</option>
                        <option>招商银行</option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label>批次号</label>
                    <input type="text" placeholder="请输入批次号">
                </div>
                
                <div class="filter-item">
                    <label>客户索引号</label>
                    <input type="text" placeholder="请输入客户索引号">
                </div>
                
                <div class="filter-item">
                    <label>客户姓名</label>
                    <input type="text" placeholder="请输入客户姓名">
                </div>
                
                <div class="filter-item">
                    <label>案件类型</label>
                    <select>
                        <option>全部类型</option>
                        <option>信用卡还款</option>
                        <option>贷款还款</option>
                        <option>分期还款</option>
                        <option>提前还款</option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label>持卡人代码</label>
                    <input type="text" placeholder="请输入持卡人代码">
                </div>
                
                <div class="filter-item">
                    <label>还款时间</label>
                    <div class="date-range">
                        <input type="date" id="start-date">
                        <span>至</span>
                        <input type="date" id="end-date">
                    </div>
                </div>
            </div>
            
            <div class="filter-buttons">
                <button class="btn btn-query" data-tooltip="执行当前筛选条件"><i class="fas fa-search"></i> 查询</button>
                <button class="btn btn-reset" data-tooltip="清除所有筛选条件"><i class="fas fa-redo"></i> 重置</button>
            </div>
        </section>
        
        <section class="stats-section">
            <div class="stats-overview">
                <div class="stat-item">
                    <div class="stat-label">还款总户数：</div>
                    <div class="stat-value" id="total-count">0户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">还款总金额：</div>
                    <div class="stat-value" id="total-amount">¥0.00</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">勾选户数：</div>
                    <div class="stat-value" id="selected-count">0户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">勾选金额：</div>
                    <div class="stat-value" id="selected-amount">¥0.00</div>
                </div>
            </div>
            
            <div class="stats-selected">
                <button class="btn btn-delete" id="batch-delete" data-tooltip="删除选中的还款记录"><i class="fas fa-trash-alt"></i> 删除</button>
                <button class="btn btn-export"><i class="fas fa-file-export"></i> 导出数据</button>
            </div>
        </section>
        
        <section class="table-section">
            
            <div class="table-container">
                <table id="repayment-table">
                    <thead>
                        <tr>
                            <th class="checkbox-cell"><input type="checkbox" id="select-all"></th>
                            <th>客户姓名</th>
                            <th>委托机构</th>
                            <th>批次号</th>
                            <th>客户索引号</th>
                            <th>持卡人代码</th>
                            <th>案件类型</th>
                            <th>还款金额</th>
                            <th>还款时间</th>
                            <th>组织</th>
                            <th>作业员</th>
                            <th>工号</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="table-body">
                        <!-- 表格数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 翻页控件 -->
            <div class="pagination">
                <button><i class="fas fa-chevron-left"></i></button>
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>4</button>
                <button><i class="fas fa-chevron-right"></i></button>
            </div>
        </section>
    </div>
    
    <script>
        // 模拟还款数据
        const repaymentData = [
            {
                id: 1,
                customer: "张明",
                institution: "中国工商银行",
                batch: "BATCH202305001",
                index: "CI1001",
                cardCode: "CARD001",
                type: "信用卡还款",
                amount: 30000.00,
                date: "2023-05-15 14:30",
                org: "上海分行",
                operator: "李经理",
                employeeId: "EMP1001"
            },
            {
                id: 2,
                customer: "王芳",
                institution: "招商银行",
                batch: "BATCH202305002",
                index: "CI1002",
                cardCode: "CARD002",
                type: "贷款还款",
                amount: 25000.00,
                date: "2023-05-16 10:15",
                org: "北京分行",
                operator: "张经理",
                employeeId: "EMP1002"
            },
            {
                id: 3,
                customer: "李华",
                institution: "中国建设银行",
                batch: "BATCH202305003",
                index: "CI1003",
                cardCode: "CARD003",
                type: "分期还款",
                amount: 15800.00,
                date: "2023-05-17 09:45",
                org: "广州分行",
                operator: "王经理",
                employeeId: "EMP1003"
            },
            {
                id: 4,
                customer: "陈晓",
                institution: "中国银行",
                batch: "BATCH202305004",
                index: "CI1004",
                cardCode: "CARD004",
                type: "提前还款",
                amount: 42000.00,
                date: "2023-05-18 16:20",
                org: "深圳分行",
                operator: "赵经理",
                employeeId: "EMP1004"
            },
            {
                id: 5,
                customer: "刘伟",
                institution: "工商银行",
                batch: "BATCH202305005",
                index: "CI1005",
                cardCode: "CARD005",
                type: "信用卡还款",
                amount: 18500.00,
                date: "2023-05-19 11:30",
                org: "杭州分行",
                operator: "钱经理",
                employeeId: "EMP1005"
            },
            {
                id: 6,
                customer: "林静",
                institution: "农业银行",
                batch: "BATCH202305006",
                index: "CI1006",
                cardCode: "CARD006",
                type: "贷款还款",
                amount: 32000.00,
                date: "2023-05-20 13:45",
                org: "成都分行",
                operator: "孙经理",
                employeeId: "EMP1006"
            },
            {
                id: 7,
                customer: "赵强",
                institution: "交通银行",
                batch: "BATCH202305007",
                index: "CI1007",
                cardCode: "CARD007",
                type: "分期还款",
                amount: 21500.00,
                date: "2023-05-21 15:20",
                org: "南京分行",
                operator: "周经理",
                employeeId: "EMP1007"
            },
            {
                id: 8,
                customer: "杨帆",
                institution: "浦发银行",
                batch: "BATCH202305008",
                index: "CI1008",
                cardCode: "CARD008",
                type: "提前还款",
                amount: 28700.00,
                date: "2023-05-22 09:30",
                org: "武汉分行",
                operator: "吴经理",
                employeeId: "EMP1008"
            },
            {
                id: 9,
                customer: "周敏",
                institution: "民生银行",
                batch: "BATCH202305009",
                index: "CI1009",
                cardCode: "CARD009",
                type: "信用卡还款",
                amount: 19800.00,
                date: "2023-05-23 11:15",
                org: "西安分行",
                operator: "郑经理",
                employeeId: "EMP1009"
            },
            {
                id: 10,
                customer: "吴涛",
                institution: "光大银行",
                batch: "BATCH202305010",
                index: "CI1010",
                cardCode: "CARD010",
                type: "贷款还款",
                amount: 35200.00,
                date: "2023-05-24 14:50",
                org: "重庆分行",
                operator: "王经理",
                employeeId: "EMP1010"
            }
        ];

        document.addEventListener('DOMContentLoaded', function() {
            // DOM元素
            const selectAll = document.getElementById('select-all');
            const tableBody = document.getElementById('table-body');
            const batchDeleteBtn = document.getElementById('batch-delete');
            const selectedCountEl = document.getElementById('selected-count');
            const selectedAmountEl = document.getElementById('selected-amount');
            const totalCountEl = document.getElementById('total-count');
            const totalAmountEl = document.getElementById('total-amount');
            const btnQuery = document.querySelector('.btn-query');
            const btnReset = document.querySelector('.btn-reset');
            const startDateInput = document.getElementById('start-date');
            const endDateInput = document.getElementById('end-date');
            
            // 初始化表格
            function renderTable(data) {
                tableBody.innerHTML = '';
                
                if (data.length === 0) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="13">
                                <div class="empty-state">
                                    <i class="fas fa-file-alt"></i>
                                    <p>未找到符合条件的还款记录<br>请尝试调整筛选条件</p>
                                </div>
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                data.forEach(item => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="checkbox-cell">
                            <input type="checkbox" class="row-checkbox" data-id="${item.id}" data-amount="${item.amount}">
                        </td>
                        <td>${item.customer}</td>
                        <td>${item.institution}</td>
                        <td>${item.batch}</td>
                        <td>${item.index}</td>
                        <td>${item.cardCode}</td>
                        <td>${item.type}</td>
                        <td>¥${item.amount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                        <td>${item.date}</td>
                        <td>${item.org}</td>
                        <td>${item.operator}</td>
                        <td>${item.employeeId}</td>
                        <td class="action-cell">
                            <a href="#" class="view-link" title="查看"><i class="fas fa-eye"></i></a>
                            <a href="#" class="delete-link" title="删除"><i class="fas fa-trash-alt"></i></a>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
                
                // 添加事件监听器
                addEventListeners();
                // 更新统计数据
                updateStats(data);
            }
            
            // 添加事件监听器
            function addEventListeners() {
                const checkboxes = document.querySelectorAll('.row-checkbox');
                
                // 全选/反选功能
                selectAll.addEventListener('change', function() {
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedStats();
                });
                
                // 单个复选框改变时更新统计
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        // 如果取消单个复选框，取消全选状态
                        if (!this.checked && selectAll.checked) {
                            selectAll.checked = false;
                        }
                        updateSelectedStats();
                    });
                });
                
                // 单行删除功能
                document.querySelectorAll('.delete-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const row = this.closest('tr');
                        const id = row.querySelector('.row-checkbox').dataset.id;
                        const name = row.cells[1].textContent;
                        
                        if (confirm(`确定要删除 ${name} 的还款记录吗？`)) {
                            row.style.opacity = '0';
                            row.style.transition = 'opacity 0.3s ease';
                            
                            setTimeout(() => {
                                row.remove();
                                // 重新加载数据（在实际应用中应从服务器删除）
                                const newData = repaymentData.filter(item => item.id != id);
                                renderTable(newData);
                            }, 300);
                        }
                    });
                });
                
                // 查看详情功能
                document.querySelectorAll('.view-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const row = this.closest('tr');
                        const name = row.cells[1].textContent;
                        const amount = row.cells[7].textContent;
                        const time = row.cells[8].textContent;
                        
                        alert(`查看详情：${name}\n金额：${amount}\n时间：${time}\n跳转至案件详情页面，只可查看不能更改操作！`);
                    });
                });
            }
            
            // 更新统计数据
            function updateStats(data) {
                // 更新总户数
                totalCountEl.textContent = `${data.length}户`;
                
                // 更新总金额
                const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);
                totalAmountEl.textContent = `¥${totalAmount.toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                })}`;
                
                // 重置勾选统计
                selectedCountEl.textContent = '0户';
                selectedAmountEl.textContent = '¥0.00';
                selectAll.checked = false;
            }
            
            // 更新勾选统计
            function updateSelectedStats() {
                const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
                const selectedCount = selectedCheckboxes.length;
                
                // 更新勾选户数
                selectedCountEl.textContent = selectedCount + '户';
                
                // 计算选中金额
                let totalAmount = 0;
                selectedCheckboxes.forEach(checkbox => {
                    totalAmount += parseFloat(checkbox.dataset.amount);
                });
                
                // 更新勾选金额
                selectedAmountEl.textContent = '¥' + totalAmount.toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }
            
            // 批量删除功能
            batchDeleteBtn.addEventListener('click', function() {
                const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
                const selectedCount = selectedCheckboxes.length;
                
                if (selectedCount === 0) {
                    alert('请至少选择一条记录进行删除！');
                    return;
                }
                
                if (confirm(`确定要删除选中的 ${selectedCount} 条记录吗？`)) {
                    const idsToDelete = [];
                    
                    selectedCheckboxes.forEach(checkbox => {
                        const row = checkbox.closest('tr');
                        idsToDelete.push(parseInt(checkbox.dataset.id));
                        row.style.opacity = '0';
                        row.style.transition = 'opacity 0.3s ease';
                    });
                    
                    setTimeout(() => {
                        // 重新加载数据（在实际应用中应从服务器删除）
                        const newData = repaymentData.filter(item => !idsToDelete.includes(item.id));
                        renderTable(newData);
                    }, 300);
                }
            });
            
            // 查询按钮事件
            btnQuery.addEventListener('click', function() {
                const startDate = startDateInput.value;
                const endDate = endDateInput.value;
                
                // 简单的时间范围过滤
                let filteredData = repaymentData;
                
                if (startDate || endDate) {
                    filteredData = repaymentData.filter(item => {
                        const itemDate = item.date.split(' ')[0]; // 只取日期部分
                        return (!startDate || itemDate >= startDate) && 
                               (!endDate || itemDate <= endDate);
                    });
                }
                
                renderTable(filteredData);
            });
            
            // 重置按钮事件
            btnReset.addEventListener('click', function() {
                // 重置筛选表单
                document.querySelectorAll('.filter-section input').forEach(input => {
                    if (input.type !== 'checkbox') {
                        input.value = '';
                    }
                });
                
                document.querySelectorAll('.filter-section select').forEach(select => {
                    select.selectedIndex = 0;
                });
                
                // 重新加载所有数据
                renderTable(repaymentData);
            });
            
            // 初始化页面
            renderTable(repaymentData);
            
            // 设置默认日期范围（最近一个月）
            const today = new Date();
            const oneMonthAgo = new Date();
            oneMonthAgo.setMonth(today.getMonth() - 1);
            
            startDateInput.valueAsDate = oneMonthAgo;
            endDateInput.valueAsDate = today;
        });
    </script>
</body>
</html>