package com.cf.financing.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 验证码控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/captcha")
public class CaptchaController {

    /**
     * 刷新验证码
     */
    @GetMapping("/refresh")
    public Map<String, Object> refreshCaptcha(HttpSession session) {
        String captcha = generateCaptcha();
        session.setAttribute("captcha", captcha);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("captcha", captcha);
        return result;
    }

    /**
     * 生成验证码
     */
    private String generateCaptcha() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder captcha = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            captcha.append(chars.charAt(random.nextInt(chars.length())));
        }
        return captcha.toString();
    }
}
