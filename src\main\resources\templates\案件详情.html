<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件管理系统 - 信息管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #2c3e50);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 40px);
            max-width: 1600px;
            margin: 0 auto;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            border-radius: 12px;
            overflow: hidden;
            background: #fff;
            gap: 1px;
        }
        
        /* 左侧内容区域 */
        .left-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 1px;
            overflow: hidden;
            gap: 1px;
        }
        
        /* 右侧目录框 - 改造为目录 */
        .directory {
            width: 320px;
            background: linear-gradient(145deg, #2c3e50, #1a2a6c);
            color: white;
            padding: 0;
            border-radius: 12px 0 0 12px;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .directory h2 {
            font-size: 1.8rem;
            margin-bottom: 0;
            padding: 15px 20px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            text-align: left;/* 左对齐标题 */
            background: rgba(0, 0, 0, 0.15);
        }
        
        /* 目录头部 */
        .case-list-header {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            padding: 12px 15px;
            background: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .case-header-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .case-header-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        /* 目录内容 */
        .case-list-content {
            flex: 1;
            overflow-y: auto;
            padding: 5px 0;
        }
        
        .case-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            padding: 12px 15px;
            margin: 5px 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            background: rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .case-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(3px);
        }
        
        .case-item.active {
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }
        
        .case-item div {
            text-align: center;
            padding: 2px 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .client-name {
            font-weight: 600;
            color: #3498db;
            cursor: pointer;
        }
        
        .client-name:hover {
            text-decoration: underline;
        }
        
        .amount {
            font-weight: 500;
        }
        
        .days {
            font-weight: 500;
            color: #ff6b6b;
        }
        
        /* 目录底部 */
        .case-list-footer {
            padding: 12px 15px;
            background: rgba(0, 0, 0, 0.2);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .case-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .case-pagination button {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.85rem;
        }
        
        .case-pagination button:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .case-pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .case-page-info {
            text-align: center;
            font-size: 0.9rem;
            color: #ddd;
        }
        
        .page-size {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.85rem;
        }
        
        .page-size select {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            padding: 3px 6px;
        }
        
        /* 信息框体 */
        .info-box {
            height: 40%;
            display: flex;
            flex-direction: column;
            border: 1px solid #e1e4e8;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            margin-bottom: 1px;
        }
        
        /* 主信息框高度调整 */
        .main-info {
            height: 80%;
            padding: 8px 12px;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e4e8;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1px;
        }
        
        /* 辅助信息框高度调整 */
        .sub-info {
            height: 20%;
            padding: 4px 6px;
            background: #f1f3f5;
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 1px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 0;
            padding: 1px;
        }
        
        .info-label {
            font-weight: 600;
            color: #2c3e50;
            white-space: nowrap;
            min-width: 50px;
            font-size: 0.85rem;
        }
        
        .main-info .info-label {
            font-size: 0.85rem;
        }
        
        .sub-info .info-label {
            font-size: 0.6rem;
        }
        
        .info-value {
            color: #495057;
            padding: 1px 3px;
            font-size: 0.8rem;
            background-color: transparent;
            border: none;
            border-radius: 4px;
            min-height: 15px;
            display: flex;
            align-items: center;
            flex: 1;
            word-break: break-all;
        }
        
        .main-info .info-value {
            font-size: 0.8rem;
        }

        .sub-info .info-value {
            font-size: 0.5rem;
        }
        
        .info-value.editable {
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .info-value.editable:hover {
            background-color: #f0f7ff;
            border-color: #3498db;
        }
        
        .info-value input {
            background-color: #fff;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            padding: 0.5px; 
            font-size: 0.5rem;
            width: 100%;
        }
        
        .section-title {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 6px;
            color: #2c3e50;
            font-size: 0.9rem;
            font-weight: 600;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 3px;
            grid-column: 1 / -1;
        }
        
        .section-title i {
            color: #3498db;
            font-size: 1rem;
        }
        
        .sub-info .section-title {
            display: none;
        }
        
        /* 跟进框体 */
        .follow-box {
            height: 25%;
            display: flex;
            border: 1px solid #e1e4e8;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            margin-bottom: 1px;
        }
        
        .button-box {
            width: 180px;
            background: #f8f9fa;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            border-right: 1px solid #e1e4e8;
        }
        
        .action-btn {
            padding: 10px;
            border: none;
            border-radius: 4px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.8rem;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .action-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }
        
        .action-btn.contact { background: #3498db; }
        .action-btn.statistics { background: #2ecc71; }
        .action-btn.assignment { background: #e74c3c; }
        .action-btn.history { background: #9b59b6; }
        
        .follow-content {
            flex: 1;
            padding: 12px;
            overflow: hidden;
            background: white;
            display: flex;
            flex-direction: column;
        }
        
        .follow-form {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            align-items: center;
        }
        
        .form-field {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.75rem;
            min-width: 80px;
            text-align: left;
        }
        
        .form-control {
            width: 110px;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 3px;
            font-size: 0.75rem;
            transition: all 0.2s;
        }
        
        .form-control:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.2);
        }
        
        .required::after {
            content: "*";
            color: #e74c3c;
            margin-left: 2px;
        }
        
        /* 开关按钮样式 - 变大变宽 */
        .switch-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 22px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #2ecc71;
        }
        
        input:checked + .slider:before {
            transform: translateX(18px);
        }
        
        /* 跟进记录框 */
        .follow-record-box {
            grid-column: 1 / -1;
            margin-top: 10px;
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            gap: 1px;
        }
        
        textarea {
            width: 800px;
            height: 100px;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.8rem;
            resize: none;
            line-height: 1.4;
        }
        
        textarea:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.2);
        }
        
        .submit-btn {
            padding: 8px 20px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background-color 0.3s;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            height: auto;
            position: relative;
            left:-150px;
        }
        
        .submit-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        
        /* 记录展示框 - 优化居中对称 */
        .record-box {
            height: 35%;
            border: 1px solid #e1e4e8;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .record-header {
            padding: 12px 16px;
            background: #2c3e50;
            color: white;
            display: grid;
            grid-template-columns: 25px repeat(5, 1fr) 2.5fr repeat(5, 1fr);
            gap: 5px;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 0.75rem;
            overflow-x: auto;
            white-space: nowrap;
        }
        
        .record-header > div {
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px;
        }
        
        .record-content {
            flex: 1;
            overflow-y: auto;
            background: white;
        }
        
        .record-row {
            display: grid;
            grid-template-columns: 25px repeat(5, 1fr) 2.5fr repeat(5, 1fr);
            gap: 5px;
            padding: 10px 16px;
            border-bottom: 1px solid #e1e4e8;
            transition: background 0.2s;
            font-size: 0.75rem;
            align-items: center;
        }
        
        .record-row:nth-child(even) {
            background: #f8fafc;
        }
        
        .record-row:hover {
            background: #e3f2fd;
        }
        
        /* 修复：移除 overflow: hidden 和 text-overflow 属性 */
        .record-cell {
            white-space: nowrap;
            padding: 8px 5px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            position: relative; /* 添加相对定位 */
        }
        
        .follow-record {
            max-height: 2.4em;
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            white-space: normal;
            cursor: pointer;
            text-align: center;
            width: 100%;
        }
        
        .follow-record:hover {
            background-color: #f0f7ff;
        }
        
        .delete-btn {
            padding: 6px 12px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: all 0.2s;
        }
        
        .delete-btn:hover {
            background-color: #c0392b;
            transform: scale(1.05);
        }
        
        .record-status {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 0.75rem;
            text-align: center;
            font-weight: 500;
            width: 100%;
        }
        
        .status-continue {
            background: #d4edda;
            color: #155724;
        }
        
        .status-empty {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-highlight {
            background: #fff3cd;
            color: #856404;
        }
        
        .pagination {
            padding: 12px;
            background: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            border-top: 1px solid #e1e4e8;
        }
        
        .pagination button {
            padding: 8px 16px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.9rem;
        }
        
        .pagination button:hover {
            background: #2980b9;
        }
        
        .pagination button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .page-info {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        
        /* 提示框 - 修复定位问题 */
        .tooltip {
            position: relative;
            cursor: pointer;
            display: flex;
            justify-content: center;
            width: 100%;
            height: 100%;
        }
        
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 100; /* 提高层级 */
            bottom: 100%; /* 在元素上方 */
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8rem;
            pointer-events: none;
        }
        
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        
        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: #fff;
            padding: 25px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
            box-shadow: 0 4px 20px rgba(0,0,0,0.25);
        }
        
        .modal-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: center;
        }
        
        .modal-title i {
            color: #e74c3c;
        }
        
        .modal-message {
            margin-bottom: 20px;
            line-height: 1.5;
            text-align: center;
        }
        
        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
            min-width: 100px;
        }
        
        .modal-btn.cancel {
            background-color: #95a5a6;
            color: white;
        }
        
        .modal-btn.confirm {
            background-color: #e74c3c;
            color: white;
        }
        
        .modal-btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        
        /* 响应式调整 */
        @media (max-width: 1600px) {
            .record-header, .record-row {
                grid-template-columns: 25px repeat(5, 1fr) 2.5fr repeat(5, 1fr);
                font-size: 0.7rem;
            }
        }
        
        @media (max-width: 1400px) {
            .record-header, .record-row {
                font-size: 0.65rem;
            }
        }
        
        @media (max-width: 1200px) {
            .record-header, .record-row {
                display: flex;
                flex-wrap: wrap;
                padding: 8px;
            }
            
            .record-cell {
                flex: 1 0 25%;
                min-width: 120px;
                padding: 8px;
                border-bottom: 1px dashed #eee;
                text-align: left;
                align-items: flex-start;
                justify-content: flex-start;
            }
            
            .record-cell:first-child {
                min-width: 25px;
            }
            
            .record-row .record-cell:last-child {
                border-bottom: none;
            }
            
            .record-header {
                display: none;
            }
            
            .main-info {
                grid-template-columns: repeat(4, 1fr);
            }
            
            .sub-info {
                grid-template-columns: repeat(5, 1fr);
            }
            
            .follow-form {
                grid-template-columns: repeat(2, 1fr);
            }
            
            textarea {
                width: 100%;
            }
            
            .case-item {
                font-size: 0.85rem;
                padding: 10px;
            }
        }
        
        @media (max-width: 992px) {
            .main-info {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .sub-info {
                grid-template-columns: repeat(4, 1fr);
            }
            
            .case-list-header {
                font-size: 0.9rem;
            }
        }
        
        @media (max-width: 900px) {
            .follow-record-box {
                flex-direction: column;
                align-items: stretch;
            }
            
            .submit-btn {
                align-self: flex-end;
                margin-top: 10px;
                left: 0;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: auto;
            }
            
            .directory {
                width: 100%;
                height: auto;
                border-radius: 12px 12px 0 0;
            }
            
            .left-content {
                height: auto;
            }
            
            .info-box, .follow-box, .record-box {
                height: auto;
            }
            
            .follow-box {
                flex-direction: column;
            }
            
            .button-box {
                width: 100%;
                flex-direction: row;
                flex-wrap: wrap;
                border-right: none;
                border-bottom: 1px solid #e1e4e8;
            }
            
            .main-info {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .sub-info {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .info-label {
                min-width: 45px;
            }
            
            .follow-form {
                grid-template-columns: 1fr;
            }
            
            .form-control {
                width: 100%;
            }
            
            .form-field {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .form-label {
                text-align: left;
                min-width: auto;
            }
            
            .case-list-header {
                font-size: 0.8rem;
            }
            
            .case-item {
                font-size: 0.8rem;
                padding: 8px;
            }
        }
        
        @media (max-width: 576px) {
            .main-info {
                grid-template-columns: 1fr;
            }
            
            .sub-info {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .info-label {
                min-width: 40px;
                font-size: 0.65rem;
                margin-right: 2px;
            }
            
            .info-value {
                font-size: 0.65rem;
                padding: 2px 4px;
            }
            
            .case-item {
                grid-template-columns: 1fr;
                gap: 5px;
            }
            
            .case-header-item {
                font-size: 0.75rem;
            }
            
            .case-pagination {
                flex-wrap: wrap;
                gap: 5px;
                justify-content: center;
            }
            
            .case-pagination button {
                padding: 4px 8px;
                font-size: 0.75rem;
            }
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c5c5c5;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* 目录框滚动条 */
        .case-list-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .case-list-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .case-list-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧内容区域 -->
        <div class="left-content">
            <!-- 信息框体 -->
            <div class="info-box">
                <div class="main-info">
                    <div class="section-title">
                        <i class="fas fa-info-circle"></i>
                        <h3>案件详情</h3>
                    </div>
                    
                    <!-- 主信息字段 -->
                    <div class="info-item">
                        <div class="info-label">客户姓名：</div>
                        <div class="info-value editable" id="clientName">张三</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">信用额度：</div>
                        <div class="info-value">¥50,000</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">批次号：</div>
                        <div class="info-value">P20230517</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">诉讼标签：</div>
                        <div class="info-value">否</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">客户索引号：</div>
                        <div class="info-value">S2437189585845542489</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">委托机构：</div>
                        <div class="info-value">XX银行</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">余额OPS：</div>
                        <div class="info-value">35,892.54</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">委托开始日：</div>
                        <div class="info-value">2023-01-01</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">保留次数：</div>
                        <div class="info-value">3</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">持卡人代码：</div>
                        <div class="info-value editable">CH2023001</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">委托金额：</div>
                        <div class="info-value">¥35,892.54</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">本金OPS：</div>
                        <div class="info-value">32,500.00</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">委托结束日：</div>
                        <div class="info-value">2023-12-31</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">投诉标签：</div>
                        <div class="info-value">无</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">身份证号：</div>
                        <div class="info-value editable">310101199001010000</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">委托本金：</div>
                        <div class="info-value">¥32,500.00</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">专项类型：</div>
                        <div class="info-value">消费金融</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最后跟进日期：</div>
                        <div class="info-value">2023-05-15</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">专项标签：</div>
                        <div class="info-value">高风险</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">户籍城市：</div>
                        <div class="info-value editable">上海市</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">案件类型：</div>
                        <div class="info-value">信用卡逾期</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">开卡日期：</div>
                        <div class="info-value">2020-05-15</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最后缴款日期：</div>
                        <div class="info-value">2023-04-20</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">新旧案标志：</div>
                        <div class="info-value">新案</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">工作单位：</div>
                        <div class="info-value editable">XX科技有限公司</div>
                    </div>
                </div>
                <div class="sub-info">
                    <!-- 辅助信息区域 -->
                    <div class="info-item">
                        <div class="info-label">职业：</div>
                        <div class="info-value">企业员工</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">客户当月主动还款金额：</div>
                        <div class="info-value">¥1,200.00</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最后缴款日期：</div>
                        <div class="info-value">2023-04-20</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">当前逾期时段：</div>
                        <div class="info-value">M2</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">委托时月龄分档：</div>
                        <div class="info-value">M2</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">智能语音标签：</div>
                        <div class="info-value">还款意愿低</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">客户年龄：</div>
                        <div class="info-value">35</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">客户前日主动还款金额：</div>
                        <div class="info-value">¥0.00</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最后跟进日期：</div>
                        <div class="info-value">2023-05-15</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">委托时逾期时段：</div>
                        <div class="info-value">M2</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">个分履约标签：</div>
                        <div class="info-value">一般</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">账户号后七位：</div>
                        <div class="info-value">8765432</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">个性化履约状态：</div>
                        <div class="info-value">正常</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">目标时段：</div>
                        <div class="info-value">M2</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">是否诉讼（含风险代理）：</div>
                        <div class="info-value">否</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">委托时金额段：</div>
                        <div class="info-value">3万-5万</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">是否诉讼（结佣）：</div>
                        <div class="info-value">否</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">个性化分期状态：</div>
                        <div class="info-value">未申请</div>
                    </div>
                </div>
            </div>
            
            <!-- 跟进框体 -->
            <div class="follow-box">
                <div class="button-box">
                    <button class="action-btn contact">
                        <i class="fas fa-address-book"></i>
                        联系人
                    </button>
                    <button class="action-btn statistics">
                        <i class="fas fa-chart-line"></i>
                        新增统计
                    </button>
                    <button class="action-btn assignment">
                        <i class="fas fa-clipboard-list"></i>
                        分派记录
                    </button>
                    <button class="action-btn history">
                        <i class="fas fa-history"></i>
                        历史记录
                    </button>
                </div>
                <div class="follow-content">
                    <form class="follow-form" id="followForm">
                        <!-- 第一行：四个字段 -->
                        <div class="form-field">
                            <label class="form-label required">被叫号码：</label>
                            <input type="text" class="form-control" id="calledNumber" required placeholder="输入被叫号码">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label required">主叫号码：</label>
                            <input type="text" class="form-control" id="callingNumber" required placeholder="输入主叫号码">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label required">跟进状态：</label>
                            <select class="form-control" id="followStatus" required>
                                <option value="" disabled selected>请选择</option>
                                <option value="继续跟进">继续跟进</option>
                                <option value="空号停机">空号停机</option>
                                <option value="重点跟进">重点跟进</option>
                                <option value="谈方案">谈方案</option>
                                <option value="线下分期">线下分期</option>
                                <option value="个性化分期">个性化分期</option>
                                <option value="待减免">待减免</option>
                                <option value="投诉倾向">投诉倾向</option>
                                <option value="要求退案">要求退案</option>
                                <option value="退回案池">退回案池</option>
                            </select>
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">失联查询结果：</label>
                            <select class="form-control" id="disconnectResult">
                                <option value="未申请" selected>未申请</option>
                                <option value="成功">成功</option>
                                <option value="失败">失败</option>
                                <option value="查询中">查询中</option>
                            </select>
                        </div>
                        
                        <!-- 第二行：三个字段 -->
                        <div class="form-field">
                            <label class="form-label">还款金额：</label>
                            <input type="number" class="form-control" id="repaymentAmount" min="0" step="0.01" placeholder="输入金额">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">还款时间：</label>
                            <input type="date" class="form-control" id="repaymentDate">
                        </div>
                        
                        <div class="form-field">
                            <label class="form-label">是否标记换单：</label>
                            <div class="switch-container">
                                <label class="switch">
                                    <input type="checkbox" id="markSwitch">
                                    <span class="slider"></span>
                                </label>
                                <span id="switchStatus">否</span>
                            </div>
                        </div>
                        
                        <!-- 空位置 -->
                        <div class="form-field"></div>
                        
                        <!-- 跟进记录框和提交按钮 -->
                        <div class="follow-record-box">
                            <textarea id="followRecord" placeholder="请输入跟进情况..."></textarea>
                            <button type="button" class="submit-btn" id="submitBtn">提交记录</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 记录展示框 -->
            <div class="record-box">
                <div class="record-header">
                    <div>序号</div>
                    <div>跟进时间</div>
                    <div>被叫号码</div>
                    <div>主叫号码</div>
                    <div>还款金额</div>
                    <div>还款时间</div>
                    <div>跟进记录</div>
                    <div>跟进状态</div>
                    <div>失联查询结果</div>
                    <div>是否标记换单</div>
                    <div>作业员</div>
                    <div>操作</div>
                </div>
                <div class="record-content" id="recordContent">
                    <!-- 记录将通过JS动态添加 -->
                </div>
                <div class="pagination">
                    <button id="prevBtn" disabled>
                        <i class="fas fa-chevron-left"></i>
                        上一页
                    </button>
                    <span class="page-info">第 1 页，共 1 页</span>
                    <button id="nextBtn" disabled>
                        下一页
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 右侧目录框 - 改造为案件列表 -->
        <div class="directory">
            <h2><i class="fas fa-list"></i> 目录</h2>
            <div class="case-list-header">
                <div class="case-header-item sortable" data-sort="name">
                    <span>客户姓名</span>
                    <i class="fas fa-sort"></i>
                </div>
                <div class="case-header-item sortable" data-sort="amount">
                    <span>委托金额</span>
                    <i class="fas fa-sort"></i>
                </div>
                <div class="case-header-item sortable" data-sort="days">
                    <span>未跟进天数</span>
                    <i class="fas fa-sort"></i>
                </div>
            </div>
            <div class="case-list-content" id="caseListContent">
                <!-- 案件列表将通过JS动态填充 -->
            </div>
            <div class="case-list-footer">
                <div class="case-pagination">
                    <button id="casePrevPage" disabled>
                        <i class="fas fa-chevron-left"></i>
                        上一页
                    </button>
                    <button id="casePrevItem" disabled>上一条</button>
                    <button id="caseNextItem">下一条</button>
                    <button id="caseNextPage">下一页
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="case-page-info" id="casePageInfo">显示 0 条中的 0 - 0 条</div>
                <div class="page-size">
                    <span>每页显示：</span>
                    <select id="pageSizeSelect">
                        <option value="10">10</option>
                        <option value="20" selected>20</option>
                        <option value="30">30</option>
                        <option value="50">50</option>
                    </select>
                    <span>条</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 删除确认模态框 -->
    <div class="modal" id="deleteModal">
        <div class="modal-content">
            <div class="modal-title">
                <i class="fas fa-exclamation-triangle"></i>
                <span>删除记录确认</span>
            </div>
            <div class="modal-message">
                您确定要删除此条跟进记录吗？此操作需要经理级以上权限，且删除后将无法恢复。
            </div>
            <div class="modal-buttons">
                <button class="modal-btn cancel" id="cancelDelete">取消</button>
                <button class="modal-btn confirm" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化变量
            let recordCounter = 1;
            let currentRecordId = null;
            const users = [
                { name: "张经理", id: "M1001" },
                { name: "李主管", id: "S2002" },
                { name: "王专员", id: "A3003" },
                { name: "赵助理", id: "A4004" }
            ];
            
            // 分页控制功能
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const pageInfo = document.querySelector('.page-info');
            
            let currentPage = 1;
            const totalPages = 1;
            
            function updatePagination() {
                pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
                prevBtn.disabled = currentPage === 1;
                nextBtn.disabled = currentPage === totalPages;
            }
            
            prevBtn.addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    updatePagination();
                }
            });
            
            nextBtn.addEventListener('click', function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    updatePagination();
                }
            });
            
            // 可编辑项点击事件
            const editableItems = document.querySelectorAll('.info-value.editable');
            editableItems.forEach(item => {
                item.addEventListener('click', function() {
                    const currentValue = this.textContent;
                    this.innerHTML = `<input type="text" value="${currentValue}" style="width:100%; border:1px solid #e1e4e8; background:#fff; border-radius:4px; padding:2px; font-size:0.5rem;">`;
                    
                    const input = this.querySelector('input');
                    input.focus();
                    
                    input.addEventListener('blur', function() {
                        item.textContent = this.value;
                        
                        // 如果编辑的是客户姓名，更新目录框中的对应客户姓名
                        if (item.id === 'clientName') {
                            updateClientNameInDirectory(this.value);
                        }
                    });
                    
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            item.textContent = this.value;
                            
                            // 如果编辑的是客户姓名，更新目录框中的对应客户姓名
                            if (item.id === 'clientName') {
                                updateClientNameInDirectory(this.value);
                            }
                            
                            this.blur();
                        }
                    });
                });
            });
            
            // 目录项点击事件
            const directoryItems = document.querySelectorAll('.directory li');
            directoryItems.forEach(item => {
                item.addEventListener('click', function() {
                    directoryItems.forEach(li => li.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 日期选择器限制为今天及以前
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('repaymentDate').max = today;
            
            // 开关状态显示
            const markSwitch = document.getElementById('markSwitch');
            const switchStatus = document.getElementById('switchStatus');
            
            markSwitch.addEventListener('change', function() {
                switchStatus.textContent = this.checked ? '是' : '否';
            });
            
            // 跟进状态变化时控制开关可用性
            const followStatus = document.getElementById('followStatus');
            
            followStatus.addEventListener('change', function() {
                const status = this.value;
                if (status === '空号停机' || status === '继续跟进') {
                    markSwitch.disabled = false;
                } else {
                    markSwitch.checked = false;
                    markSwitch.disabled = true;
                    switchStatus.textContent = '否';
                }
            });
            
            // 获取随机用户
            function getRandomUser() {
                const randomIndex = Math.floor(Math.random() * users.length);
                return users[randomIndex];
            }
            
            // 格式化时间（包含时分秒）
            function formatFullDateTime(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                
                return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
            }
            
            // 格式化简短日期
            function formatShortDate(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}/${month}/${day}`;
            }
            
            // 表单提交功能
            const submitBtn = document.getElementById('submitBtn');
            const followForm = document.getElementById('followForm');
            const recordContent = document.getElementById('recordContent');
            const maxRepayment = 35892.54;
            
            submitBtn.addEventListener('click', function() {
                // 验证必填项
                if (!followForm.checkValidity()) {
                    alert('请填写必填项：被叫号码、主叫号码和跟进状态！');
                    return;
                }
                
                // 验证还款金额和时间
                const repaymentAmount = parseFloat(document.getElementById('repaymentAmount').value);
                const repaymentDate = document.getElementById('repaymentDate').value;
                
                if (repaymentAmount && !repaymentDate) {
                    alert('填写了还款金额必须选择还款时间！');
                    return;
                }
                
                if (repaymentAmount > maxRepayment) {
                    alert(`还款金额不能超过委托金额 ¥${maxRepayment.toFixed(2)}！`);
                    return;
                }
                
                // 获取表单数据
                const calledNumber = document.getElementById('calledNumber').value;
                const callingNumber = document.getElementById('callingNumber').value;
                const followStatusVal = document.getElementById('followStatus').value;
                const disconnectResult = document.getElementById('disconnectResult').value;
                const repaymentAmountValue = repaymentAmount || 0;
                const repaymentDateValue = repaymentDate || '未填写';
                const markSwitchValue = markSwitch.checked ? '是' : '否';
                const followRecord = document.getElementById('followRecord').value || '无';
                const user = getRandomUser();
                
                // 创建新记录
                const now = new Date();
                const fullDateTime = formatFullDateTime(now);
                const shortDate = formatShortDate(now);
                
                // 添加到记录展示框顶部
                const newRecord = document.createElement('div');
                newRecord.className = 'record-row';
                newRecord.dataset.id = `record-${Date.now()}`;
                
                // 计算倒序序号（总记录数+1）
                const recordCount = recordContent.children.length;
                const reverseIndex = recordCount + 1;
                
                newRecord.innerHTML = `
                    <div class="record-cell">${reverseIndex}</div>
                    <div class="record-cell">
                        <div class="tooltip">
                            ${shortDate}
                            <span class="tooltiptext">${fullDateTime}</span>
                        </div>
                    </div>
                    <div class="record-cell called-number" data-number="${calledNumber}">${calledNumber}</div>
                    <div class="record-cell calling-number" data-number="${callingNumber}">${callingNumber}</div>
                    <div class="record-cell">¥${repaymentAmountValue.toFixed(2)}</div>
                    <div class="record-cell">${repaymentDateValue}</div>
                    <div class="record-cell">
                        <div class="follow-record" title="${followRecord}">${followRecord}</div>
                    </div>
                    <div class="record-cell">
                        <span class="record-status ${getStatusClass(followStatusVal)}">${followStatusVal}</span>
                    </div>
                    <div class="record-cell">${disconnectResult}</div>
                    <div class="record-cell">${markSwitchValue}</div>
                    <div class="record-cell">${user.name} (${user.id})</div>
                    <div class="record-cell">
                        <button class="delete-btn">删除</button>
                    </div>
                `;
                
                recordContent.insertBefore(newRecord, recordContent.firstChild);
                
                // 更新所有记录的序号
                updateRecordNumbers();
                
                // 添加删除事件
                newRecord.querySelector('.delete-btn').addEventListener('click', function() {
                    openDeleteModal(newRecord.dataset.id);
                });
                
                // 添加号码点击事件 - 分别处理被叫号码和主叫号码
                newRecord.querySelector('.called-number').addEventListener('click', function() {
                    document.getElementById('calledNumber').value = this.dataset.number;
                });
                
                newRecord.querySelector('.calling-number').addEventListener('click', function() {
                    document.getElementById('callingNumber').value = this.dataset.number;
                });
                
                // 添加跟进记录双击事件
                newRecord.querySelector('.follow-record').addEventListener('dblclick', function() {
                    const text = this.textContent;
                    navigator.clipboard.writeText(text).then(() => {
                        alert('跟进记录已复制到剪贴板');
                    });
                });
                
                // 显示成功消息
                alert('跟进记录提交成功！');
                
                // 重置表单（保留失联查询结果）
                followForm.reset();
                document.getElementById('disconnectResult').value = disconnectResult;
                switchStatus.textContent = '否';
            });
            
            // 更新所有记录的序号（倒序显示）
            function updateRecordNumbers() {
                const records = document.querySelectorAll('.record-content .record-row');
                records.forEach((record, index) => {
                    // 倒序编号：第一条记录为总记录数，最后一条为1
                    record.querySelector('.record-cell:first-child').textContent = records.length - index;
                });
            }
            
            // 获取状态样式类
            function getStatusClass(status) {
                switch(status) {
                    case '继续跟进': return 'status-continue';
                    case '空号停机': return 'status-empty';
                    case '重点跟进': return 'status-highlight';
                    default: return '';
                }
            }
            
            // 删除记录模态框
            const deleteModal = document.getElementById('deleteModal');
            const cancelDeleteBtn = document.getElementById('cancelDelete');
            const confirmDeleteBtn = document.getElementById('confirmDelete');
            
            function openDeleteModal(recordId) {
                currentRecordId = recordId;
                deleteModal.style.display = 'flex';
            }
            
            function closeDeleteModal() {
                deleteModal.style.display = 'none';
                currentRecordId = null;
            }
            
            cancelDeleteBtn.addEventListener('click', closeDeleteModal);
            
            confirmDeleteBtn.addEventListener('click', function() {
                if (currentRecordId) {
                    const recordToDelete = document.querySelector(`.record-row[data-id="${currentRecordId}"]`);
                    if (recordToDelete) {
                        recordToDelete.remove();
                        alert('记录已删除');
                        updateRecordNumbers();
                    }
                }
                closeDeleteModal();
            });
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === deleteModal) {
                    closeDeleteModal();
                }
            });
            
            // 初始化页面
            updatePagination();
            
            // 添加示例记录
            const now = new Date();
            for (let i = 0; i < 5; i++) {
                const pastDate = new Date(now);
                pastDate.setDate(now.getDate() - i - 1);
                pastDate.setHours(9 + i, 30 + i, 15 + i); // 添加时分秒
                
                const fullDateTime = formatFullDateTime(pastDate);
                const shortDate = formatShortDate(pastDate);
                
                const user = getRandomUser();
                const record = document.createElement('div');
                record.className = 'record-row';
                record.dataset.id = `record-example-${i}`;
                
                // 倒序序号
                const reverseIndex = 5 - i;
                
                record.innerHTML = `
                    <div class="record-cell">${reverseIndex}</div>
                    <div class="record-cell">
                        <div class="tooltip">
                            ${shortDate}
                            <span class="tooltiptext">${fullDateTime}</span>
                        </div>
                    </div>
                    <div class="record-cell called-number" data-number="1380013800${i}">1380013800${i}</div>
                    <div class="record-cell calling-number" data-number="1390013900${i}">1390013900${i}</div>
                    <div class="record-cell">¥${(1000 + i*200).toFixed(2)}</div>
                    <div class="record-cell">2023-05-${15+i}</div>
                    <div class="record-cell">
                        <div class="follow-record" title="客户表示将在下周一还款，需要确认具体金额和时间">客户表示将在下周一还款，需要确认具体金额和时间</div>
                    </div>
                    <div class="record-cell">
                        <span class="record-status ${i % 2 === 0 ? 'status-continue' : 'status-highlight'}">${i % 2 === 0 ? '继续跟进' : '重点跟进'}</span>
                    </div>
                    <div class="record-cell">${i % 3 === 0 ? '未申请' : '成功'}</div>
                    <div class="record-cell">${i % 4 === 0 ? '是' : '否'}</div>
                    <div class="record-cell">${user.name} (${user.id})</div>
                    <div class="record-cell">
                        <button class="delete-btn">删除</button>
                    </div>
                `;
                
                recordContent.appendChild(record);
                
                // 添加删除事件
                record.querySelector('.delete-btn').addEventListener('click', function() {
                    openDeleteModal(record.dataset.id);
                });
                
                // 添加号码点击事件 - 分别处理被叫号码和主叫号码
                record.querySelector('.called-number').addEventListener('click', function() {
                    document.getElementById('calledNumber').value = this.dataset.number;
                });
                
                record.querySelector('.calling-number').addEventListener('click', function() {
                    document.getElementById('callingNumber').value = this.dataset.number;
                });
                
                // 添加跟进记录双击事件
                record.querySelector('.follow-record').addEventListener('dblclick', function() {
                    const text = "客户表示将在下周一还款，需要确认具体金额和时间";
                    navigator.clipboard.writeText(text).then(() => {
                        alert('跟进记录已复制到剪贴板');
                    });
                });
            }
            
            // 案件列表数据
            const caseData = [
                { id: 1, name: "张三", amount: "¥35,892.54", days: 5 },
                { id: 2, name: "李四", amount: "¥28,450.00", days: 3 },
                { id: 3, name: "王五", amount: "¥42,300.00", days: 7 },
                { id: 4, name: "赵六", amount: "¥19,800.00", days: 1 },
                { id: 5, name: "钱七", amount: "¥56,700.00", days: 12 },
                { id: 6, name: "孙八", amount: "¥32,100.00", days: 0 },
                { id: 7, name: "周九", amount: "¥25,600.00", days: 8 },
                { id: 8, name: "吴十", amount: "¥38,900.00", days: 4 },
                { id: 9, name: "郑十一", amount: "¥21,300.00", days: 6 },
                { id: 10, name: "王十二", amount: "¥45,200.00", days: 9 },
                { id: 11, name: "刘十三", amount: "¥29,700.00", days: 2 },
                { id: 12, name: "陈十四", amount: "¥37,400.00", days: 11 },
                { id: 13, name: "杨十五", amount: "¥23,800.00", days: 3 },
                { id: 14, name: "黄十六", amount: "¥41,500.00", days: 7 },
                { id: 15, name: "赵十七", amount: "¥33,200.00", days: 5 },
                { id: 16, name: "周十八", amount: "¥27,900.00", days: 10 },
                { id: 17, name: "吴十九", amount: "¥39,600.00", days: 4 },
                { id: 18, name: "郑二十", amount: "¥22,100.00", days: 8 },
                { id: 19, name: "王二十一", amount: "¥48,300.00", days: 6 },
                { id: 20, name: "刘二十二", amount: "¥31,700.00", days: 2 },
                { id: 21, name: "陈二十三", amount: "¥36,800.00", days: 9 },
                { id: 22, name: "杨二十四", amount: "¥24,500.00", days: 1 },
                { id: 23, name: "黄二十五", amount: "¥43,900.00", days: 11 }
            ];
            
            // 案件列表分页控制
            let caseCurrentPage = 1;
            let casePageSize = 20;
            let caseCurrentIndex = 0;
            let caseSortField = 'name';
            let caseSortOrder = 'asc';
            
            const caseListContent = document.getElementById('caseListContent');
            const casePrevPageBtn = document.getElementById('casePrevPage');
            const caseNextPageBtn = document.getElementById('caseNextPage');
            const casePrevItemBtn = document.getElementById('casePrevItem');
            const caseNextItemBtn = document.getElementById('caseNextItem');
            const casePageInfo = document.getElementById('casePageInfo');
            const pageSizeSelect = document.getElementById('pageSizeSelect');
            const sortableHeaders = document.querySelectorAll('.case-header-item.sortable');
            
            // 初始化案件列表
            renderCaseList();
            
            // 案件列表排序
            sortableHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const field = this.dataset.sort;
                    
                    if (field === caseSortField) {
                        // 切换排序顺序
                        caseSortOrder = caseSortOrder === 'asc' ? 'desc' : 'asc';
                    } else {
                        // 设置新的排序字段
                        caseSortField = field;
                        caseSortOrder = 'asc';
                    }
                    
                    // 更新排序图标
                    sortableHeaders.forEach(h => {
                        const icon = h.querySelector('i');
                        if (h.dataset.sort === caseSortField) {
                            icon.className = caseSortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
                        } else {
                            icon.className = 'fas fa-sort';
                        }
                    });
                    
                    // 重新渲染列表
                    renderCaseList();
                });
            });
            
            // 分页控制
            casePrevPageBtn.addEventListener('click', function() {
                if (caseCurrentPage > 1) {
                    caseCurrentPage--;
                    renderCaseList();
                }
            });
            
            caseNextPageBtn.addEventListener('click', function() {
                const totalPages = Math.ceil(caseData.length / casePageSize);
                if (caseCurrentPage < totalPages) {
                    caseCurrentPage++;
                    renderCaseList();
                }
            });
            
            // 上下条控制
            casePrevItemBtn.addEventListener('click', function() {
                if (caseCurrentIndex > 0) {
                    caseCurrentIndex--;
                    renderCaseList();
                }
            });
            
            caseNextItemBtn.addEventListener('click', function() {
                if (caseCurrentIndex < caseData.length - 1) {
                    caseCurrentIndex++;
                    renderCaseList();
                }
            });
            
            // 每页显示条数变更
            pageSizeSelect.addEventListener('change', function() {
                casePageSize = parseInt(this.value);
                caseCurrentPage = 1;
                renderCaseList();
            });
            
            // 渲染案件列表
            function renderCaseList() {
                // 排序数据
                const sortedData = [...caseData].sort((a, b) => {
                    if (caseSortField === 'name') {
                        return caseSortOrder === 'asc' 
                            ? a.name.localeCompare(b.name) 
                            : b.name.localeCompare(a.name);
                    } else if (caseSortField === 'amount') {
                        const amountA = parseFloat(a.amount.replace(/[^\d.]/g, ''));
                        const amountB = parseFloat(b.amount.replace(/[^\d.]/g, ''));
                        return caseSortOrder === 'asc' ? amountA - amountB : amountB - amountA;
                    } else if (caseSortField === 'days') {
                        return caseSortOrder === 'asc' ? a.days - b.days : b.days - a.days;
                    }
                    return 0;
                });
                
                // 计算分页
                const startIndex = (caseCurrentPage - 1) * casePageSize;
                const endIndex = Math.min(startIndex + casePageSize, sortedData.length);
                const pageData = sortedData.slice(startIndex, endIndex);
                
                // 清空当前列表
                caseListContent.innerHTML = '';
                
                // 添加案件项
                pageData.forEach((item, index) => {
                    const caseItem = document.createElement('div');
                    caseItem.className = 'case-item';
                    caseItem.dataset.id = item.id;
                    
                    if (item.id === 1) {
                        caseItem.classList.add('active');
                    }
                    
                    caseItem.innerHTML = `
                        <div class="client-name" data-id="${item.id}">${item.name}</div>
                        <div class="amount">${item.amount}</div>
                        <div class="days">${item.days}天</div>
                    `;
                    
                    caseListContent.appendChild(caseItem);
                    
                    // 添加点击事件
                    caseItem.addEventListener('click', function() {
                        // 设置活动项
                        document.querySelectorAll('.case-item').forEach(i => i.classList.remove('active'));
                        this.classList.add('active');
                        
                        // 更新当前索引
                        caseCurrentIndex = startIndex + index;
                        
                        // 更新详情区域
                        updateCaseDetails(item);
                    });
                    
                    // 添加客户姓名点击事件（跳转详情）
                    caseItem.querySelector('.client-name').addEventListener('click', function(e) {
                        e.stopPropagation();
                        const id = this.dataset.id;
                        showClientDetails(id);
                    });
                });
                
                // 更新分页信息
                updateCasePaginationInfo(sortedData.length);
                
                // 更新按钮状态
                updateCaseButtons(sortedData.length);
            }
            
            // 更新分页信息
            function updateCasePaginationInfo(total) {
                const start = (caseCurrentPage - 1) * casePageSize + 1;
                const end = Math.min(caseCurrentPage * casePageSize, total);
                casePageInfo.textContent = `显示 ${total} 条中的 ${start} - ${end} 条`;
            }
            
            // 更新按钮状态
            function updateCaseButtons(total) {
                const totalPages = Math.ceil(total / casePageSize);
                
                casePrevPageBtn.disabled = caseCurrentPage <= 1;
                caseNextPageBtn.disabled = caseCurrentPage >= totalPages;
                
                casePrevItemBtn.disabled = caseCurrentIndex <= 0;
                caseNextItemBtn.disabled = caseCurrentIndex >= total - 1;
            }
            
            // 更新案件详情
            function updateCaseDetails(item) {
                // 这里模拟更新详情区域
                document.getElementById('clientName').textContent = item.name;
                // 实际应用中会更新更多字段
            }
            
            // 显示客户详情
            function showClientDetails(id) {
                // 这里模拟跳转到客户详情页
                alert(`正在跳转到客户ID: ${id} 的详情页面...`);
                // 实际应用中会使用 window.location.href 或其他方式跳转
            }
            
            // 更新目录框中的客户姓名
            function updateClientNameInDirectory(newName) {
                const activeCase = document.querySelector('.case-item.active');
                if (activeCase) {
                    const clientNameElement = activeCase.querySelector('.client-name');
                    if (clientNameElement) {
                        clientNameElement.textContent = newName;
                    }
                }
            }
        });
    </script>
</body>
</html>