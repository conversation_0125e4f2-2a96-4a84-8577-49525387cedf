package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.Statistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 统计信息 Mapper 接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface StatisticsMapper extends BaseMapper<Statistics> {

    /**
     * 分页查询统计信息
     *
     * @param page 分页参数
     * @param statType 统计类型
     * @param departmentId 部门ID
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页结果
     */
    IPage<Statistics> selectStatisticsPage(Page<Statistics> page,
                                           @Param("statType") Integer statType,
                                           @Param("departmentId") Long departmentId,
                                           @Param("userId") Long userId,
                                           @Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate);

    /**
     * 生成日统计数据
     *
     * @param statDate 统计日期
     * @param userId 用户ID（可选，为空则统计所有用户）
     * @return 生成的统计记录数
     */
    int generateDailyStatistics(@Param("statDate") LocalDate statDate,
                               @Param("userId") Long userId);

    /**
     * 生成月统计数据
     *
     * @param year 年份
     * @param month 月份
     * @param userId 用户ID（可选，为空则统计所有用户）
     * @return 生成的统计记录数
     */
    int generateMonthlyStatistics(@Param("year") Integer year,
                                 @Param("month") Integer month,
                                 @Param("userId") Long userId);

    /**
     * 查询部门统计汇总
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型
     * @return 部门统计结果
     */
    List<Map<String, Object>> selectDepartmentStatistics(@Param("startDate") LocalDate startDate,
                                                         @Param("endDate") LocalDate endDate,
                                                         @Param("statType") Integer statType);

    /**
     * 查询用户排行榜
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param orderBy 排序字段（recovered_amount, recovery_rate, contact_count等）
     * @param limit 限制数量
     * @return 用户排行结果
     */
    List<Map<String, Object>> selectUserRanking(@Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate,
                                               @Param("orderBy") String orderBy,
                                               @Param("limit") Integer limit);

    /**
     * 查询趋势数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型
     * @param userId 用户ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 趋势数据
     */
    List<Map<String, Object>> selectTrendData(@Param("startDate") LocalDate startDate,
                                             @Param("endDate") LocalDate endDate,
                                             @Param("statType") Integer statType,
                                             @Param("userId") Long userId,
                                             @Param("departmentId") Long departmentId);

    /**
     * 查询实时统计数据
     *
     * @param statDate 统计日期
     * @param userId 用户ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 实时统计结果
     */
    Map<String, Object> selectRealTimeStatistics(@Param("statDate") LocalDate statDate,
                                                 @Param("userId") Long userId,
                                                 @Param("departmentId") Long departmentId);

    /**
     * 查询对比数据
     *
     * @param currentStartDate 当前期间开始日期
     * @param currentEndDate 当前期间结束日期
     * @param previousStartDate 对比期间开始日期
     * @param previousEndDate 对比期间结束日期
     * @param userId 用户ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 对比数据
     */
    Map<String, Object> selectComparisonData(@Param("currentStartDate") LocalDate currentStartDate,
                                            @Param("currentEndDate") LocalDate currentEndDate,
                                            @Param("previousStartDate") LocalDate previousStartDate,
                                            @Param("previousEndDate") LocalDate previousEndDate,
                                            @Param("userId") Long userId,
                                            @Param("departmentId") Long departmentId);

    /**
     * 删除指定日期范围的统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型（可选）
     * @return 删除数量
     */
    int deleteStatisticsByDateRange(@Param("startDate") LocalDate startDate,
                                   @Param("endDate") LocalDate endDate,
                                   @Param("statType") Integer statType);
}