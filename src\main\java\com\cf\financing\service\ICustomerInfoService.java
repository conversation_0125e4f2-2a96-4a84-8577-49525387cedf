package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.CustomerInfo;

import java.util.List;
import java.util.Map;

/**
 * 客户信息服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ICustomerInfoService extends IService<CustomerInfo> {

    /**
     * 分页查询客户信息
     *
     * @param page 分页参数
     * @param customerNo 客户编号
     * @param customerName 客户姓名
     * @param phone 手机号
     * @param idCard 身份证号
     * @param riskLevel 风险等级
     * @param creditLevel 信用等级
     * @return 分页结果
     */
    IPage<CustomerInfo> getCustomerPage(
            Page<CustomerInfo> page,
            String customerNo,
            String customerName,
            String phone,
            String idCard,
            String riskLevel,
            String creditLevel
    );

    /**
     * 根据ID查询客户详情
     *
     * @param customerId 客户ID
     * @return 客户信息
     */
    CustomerInfo getCustomerById(Long customerId);

    /**
     * 根据身份证号查询客户
     *
     * @param idCard 身份证号
     * @return 客户信息
     */
    CustomerInfo getCustomerByIdCard(String idCard);

    /**
     * 根据手机号查询客户
     *
     * @param phone 手机号
     * @return 客户信息
     */
    CustomerInfo getCustomerByPhone(String phone);

    /**
     * 根据客户编号查询客户
     *
     * @param customerNo 客户编号
     * @return 客户信息
     */
    CustomerInfo getCustomerByNo(String customerNo);

    /**
     * 创建客户
     *
     * @param customer 客户信息
     * @return 是否成功
     */
    boolean createCustomer(CustomerInfo customer);

    /**
     * 更新客户信息
     *
     * @param customer 客户信息
     * @return 是否成功
     */
    boolean updateCustomer(CustomerInfo customer);

    /**
     * 删除客户
     *
     * @param customerId 客户ID
     * @return 是否成功
     */
    boolean deleteCustomer(Long customerId);

    /**
     * 批量删除客户
     *
     * @param customerIds 客户ID列表
     * @return 是否成功
     */
    boolean batchDeleteCustomers(List<Long> customerIds);

    /**
     * 获取客户统计信息
     *
     * @return 统计数据
     */
    Map<String, Object> getCustomerStatistics();

    /**
     * 获取风险等级分布
     *
     * @return 风险等级分布数据
     */
    List<Map<String, Object>> getRiskLevelDistribution();

    /**
     * 获取信用等级分布
     *
     * @return 信用等级分布数据
     */
    List<Map<String, Object>> getCreditLevelDistribution();

    /**
     * 获取高风险客户列表
     *
     * @param limit 限制数量
     * @return 高风险客户列表
     */
    List<CustomerInfo> getHighRiskCustomers(Integer limit);

    /**
     * 获取逾期金额最高的客户列表
     *
     * @param limit 限制数量
     * @return 客户列表
     */
    List<CustomerInfo> getTopOverdueCustomers(Integer limit);

    /**
     * 批量更新客户风险等级
     *
     * @param customerIds 客户ID列表
     * @param riskLevel 风险等级
     * @return 是否成功
     */
    boolean batchUpdateRiskLevel(List<Long> customerIds, String riskLevel);

    /**
     * 批量更新客户信用等级
     *
     * @param customerIds 客户ID列表
     * @param creditLevel 信用等级
     * @return 是否成功
     */
    boolean batchUpdateCreditLevel(List<Long> customerIds, String creditLevel);

    /**
     * 检查身份证号是否存在
     *
     * @param idCard 身份证号
     * @param excludeCustomerId 排除的客户ID
     * @return 是否存在
     */
    boolean checkIdCardExists(String idCard, Long excludeCustomerId);

    /**
     * 检查客户编号是否存在
     *
     * @param customerNo 客户编号
     * @param excludeCustomerId 排除的客户ID
     * @return 是否存在
     */
    boolean checkCustomerNoExists(String customerNo, Long excludeCustomerId);

    /**
     * 获取月度新增客户趋势
     *
     * @param months 月份数
     * @return 趋势数据
     */
    List<Map<String, Object>> getMonthlyNewCustomerTrend(Integer months);

    /**
     * 客户搜索
     *
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 客户列表
     */
    List<CustomerInfo> searchCustomers(String keyword, Integer limit);

    /**
     * 生成客户编号
     *
     * @return 客户编号
     */
    String generateCustomerNo();
}