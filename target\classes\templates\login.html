<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <title>CF-作业系统 | 登录</title>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#002b5c',
                        secondary: '#ffd700',
                        accent: '#0066cc',
                        neutral: '#f4f9ff',
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .bg-glass {
                backdrop-filter: blur(12px);
                background: rgba(255, 255, 255, 0.1);
            }
            .text-shadow {
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            }

            .animate-gradient {
                background-size: 400% 400%;
                animation: gradientFlow 15s ease infinite;
            }
            @keyframes gradientFlow {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }
        }
    </style>
</head>
<body class="font-inter bg-neutral min-h-screen flex flex-col">
    <!-- 顶部区域 - LOGO和企业名称 -->
    <header class="bg-primary text-secondary py-4 px-8 flex items-center shadow-lg relative z-10">
        <div class="container mx-auto flex items-center">
            <div class="flex items-center space-x-3">
                <img th:src="@{/images/logo.png}" alt="企业Logo" class="h-12 w-auto">
                <h1 class="text-2xl font-bold text-shadow">CF 智慧作业系统</h1>
            </div>
        </div>
    </header>

    <!-- 中间区域 - 企业文化和登录表单 -->
    <main class="flex-grow flex flex-col md:flex-row relative overflow-hidden animate-gradient bg-gradient-to-br from-primary to-accent">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-secondary"></div>
            <div class="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-accent"></div>
        </div>

        <!-- 中左区域 - 企业文化 -->
        <section class="w-full md:w-1/2 p-8 md:p-16 flex items-start justify-center relative z-10">
            <div class="max-w-xl mt-8 md:mt-0">
                <h2 class="text-4xl font-bold text-white mb-6 border-l-4 border-secondary pl-4">企业文化</h2>
                <div class="text-white/90 text-lg leading-relaxed space-y-4">
                    <p>我们始终秉持着创新、协作、责任与卓越的核心价值观。</p>
                    <p>发展是我们的使命，服务是我们的承诺、诚信是我们的基石，创新是我们的动力。</p>
                    <p>我们相信，只有通过不断的创新和卓越的服务，才能赢得客户的信任和尊重。</p>
                </div>
            </div>
        </section>

        <!-- 中右区域 - 登录表单 -->
        <section class="w-full md:w-1/2 p-8 md:p-16 flex items-center justify-center">
            <div class="w-full max-w-md bg-glass rounded-2xl p-8 shadow-2xl border border-white/20">
                <h3 class="text-2xl font-bold text-white mb-8 text-center">用户登录</h3>
                
                <!-- 显示错误信息 -->
                <div th:if="${error}" class="mb-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg">
                    <p class="text-red-200 text-sm flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span th:text="${error}">登录失败</span>
                    </p>
                </div>
                
                <!-- 显示成功信息 -->
                <div th:if="${success}" class="mb-4 p-3 bg-green-500/20 border border-green-500/50 rounded-lg">
                    <p class="text-green-200 text-sm flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span th:text="${success}">操作成功</span>
                    </p>
                </div>
                
                <form th:action="@{/login}" method="post" id="loginForm" class="space-y-6">
                    <!-- 用户名输入 -->
                    <div class="space-y-2">
                        <label for="username" class="block text-white/80 text-sm font-medium">用户名</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-white/50"></i>
                            </div>
                            <input 
                                type="text" 
                                id="username" 
                                name="username"
                                th:value="${username}"
                                class="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all"
                                placeholder="请输入您的用户名"
                                required
                            >
                        </div>
                    </div>

                    <!-- 密码输入 -->
                    <div class="space-y-2">
                        <label for="password" class="block text-white/80 text-sm font-medium">密码</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-white/50"></i>
                            </div>
                            <input 
                                type="password" 
                                id="password" 
                                name="password"
                                class="w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all"
                                placeholder="请输入密码"
                                required
                            >
                            <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center text-white/50 hover:text-white">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 验证码 -->
                    <div class="space-y-2">
                        <label for="captcha" class="block text-white/80 text-sm font-medium">验证码</label>
                        <div class="flex gap-3">
                            <div class="relative flex-1">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-shield-alt text-white/50"></i>
                                </div>
                                <input
                                    type="text"
                                    id="captcha"
                                    name="captcha"
                                    class="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all"
                                    placeholder="请输入验证码"
                                    required
                                >
                            </div>
                            <div id="captchaDisplay" class="flex items-center justify-center px-4 bg-white/20 border border-white/30 rounded-lg cursor-pointer hover:bg-white/30 transition-all">
                                <span id="captchaCode" class="font-mono text-white tracking-wider" th:text="${captcha}">X9K4</span>
                            </div>
                        </div>
                    </div>

                    <!-- 记住我 -->
                    <div class="flex justify-between items-center text-sm">
                        <label class="flex items-center text-white/70">
                            <input type="checkbox" name="remember-me" class="mr-2 rounded bg-white/10 border-white/20 text-secondary focus:ring-secondary/50">
                            记住密码
                        </label>
                        <a href="#" class="text-white/70 hover:text-white transition-colors">忘记密码?</a>
                    </div>

                    <!-- 登录按钮 -->
                    <button 
                        type="submit" 
                        class="w-full py-3 bg-secondary text-primary font-semibold rounded-lg hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all transform hover:translate-y-[-2px] active:translate-y-0"
                    >
                        立即登录
                    </button>
                </form>
            </div>
        </section>
    </main>

    <!-- 底部区域 - 企业文化标语 -->
    <footer class="bg-primary text-secondary py-6 px-8 text-center">
        <p class="text-lg font-medium">做大、做强、做尊敬、做永恒</p>
    </footer>

    <script th:src="@{/js/login.js}"></script>
</body>
</html>
