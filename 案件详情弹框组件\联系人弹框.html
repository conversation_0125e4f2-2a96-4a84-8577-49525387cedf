<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人添加栏弹框</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6', // 浅蓝色主色调
                        danger: '#EF4444',  // 红色删除按钮
                        neutral: {
                            100: '#F3F4F6',
                            200: '#E5E7EB',
                            300: '#D1D5DB',
                            700: '#374151',
                            800: '#1F2937',
                            900: '#111827'
                        }
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .modal-shadow {
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            }
            .table-hover-row:hover {
                background-color: rgba(59, 130, 246, 0.05);
            }
        }
    </style>
</head>
<body class="bg-neutral-100 font-inter min-h-screen flex items-center justify-center p-4">
    <!-- 模态框背景 -->
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <!-- 模态框主体 -->
        <div class="bg-white rounded-xl w-full max-w-4xl modal-shadow overflow-hidden transform transition-all duration-300">
            <!-- 模态框头部 -->
            <div class="flex justify-between items-center px-6 py-4 border-b border-neutral-200">
                <h3 class="text-lg font-semibold text-neutral-800">联系人添加栏弹框</h3>
                <button id="closeModal" class="text-neutral-500 hover:text-neutral-700 transition-colors p-1">
                    <i class="fa fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- 模态框内容 -->
            <div class="p-6">
                <!-- 表单区域 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="space-y-2 md:col-span-1">
                        <label for="name" class="block text-sm font-medium text-neutral-700">姓名</label>
                        <input type="text" id="name" class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-all" placeholder="请输入姓名">
                    </div>
                    
                    <div class="space-y-2 md:col-span-1">
                        <label for="relationship" class="block text-sm font-medium text-neutral-700">关系</label>
                        <input type="text" id="relationship" class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-all" placeholder="请输入关系">
                    </div>
                    
                    <div class="space-y-2 md:col-span-1">
                        <label for="phone" class="block text-sm font-medium text-neutral-700">联系电话</label>
                        <input type="tel" id="phone" class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-all" placeholder="请输入联系电话">
                    </div>
                    
                    <div class="md:col-span-1 flex items-end justify-end">
                        <button id="saveContact" class="bg-primary hover:bg-primary/90 text-white font-medium py-2 px-6 rounded-lg transition-all flex items-center gap-2 shadow-sm hover:shadow">
                            <i class="fa fa-save"></i>
                            <span>保存</span>
                        </button>
                    </div>
                </div>
                
                <!-- 表格区域 -->
                <div class="overflow-x-auto max-h-[300px] overflow-y-auto">
                    <table class="min-w-full divide-y divide-neutral-200">
                        <thead class="bg-neutral-100">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-700 uppercase tracking-wider">序号</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-700 uppercase tracking-wider">姓名</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-700 uppercase tracking-wider">关系</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-700 uppercase tracking-wider">联系电话</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-neutral-700 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-neutral-200">
                            <tr class="table-hover-row">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-700">1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-900">张三</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-700">朋友</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-700">13555888835</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                    <button class="text-danger hover:text-danger/80 font-medium transition-colors flex items-center justify-end gap-1">
                                        <i class="fa fa-trash-o"></i>
                                        <span>删除</span>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 关闭模态框功能
        document.getElementById('closeModal').addEventListener('click', function() {
            const modal = document.querySelector('.fixed');
            modal.classList.add('opacity-0', 'pointer-events-none');
            setTimeout(() => {
                modal.remove();
            }, 300);
        });
        
        // 保存联系人功能
        document.getElementById('saveContact').addEventListener('click', function() {
            const name = document.getElementById('name').value;
            const relationship = document.getElementById('relationship').value;
            const phone = document.getElementById('phone').value;
            
            if (!name || !relationship || !phone) {
                alert('请填写完整联系人信息');
                return;
            }
            
            // 这里可以添加实际的保存逻辑
            alert('联系人信息已保存');
        });
    </script>
</body>
</html>