package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 案件信息实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("case_info")
public class CaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 案件ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 案件编号
     */
    @TableField("case_no")
    private String caseNo;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private Long customerId;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 借款金额
     */
    @TableField("loan_amount")
    private BigDecimal loanAmount;

    /**
     * 逾期金额
     */
    @TableField("overdue_amount")
    private BigDecimal overdueAmount;

    /**
     * 逾期天数
     */
    @TableField("overdue_days")
    private Integer overdueDays;

    /**
     * 逾期日期
     */
    @TableField("overdue_date")
    private LocalDate overdueDate;

    /**
     * 案件状态(PENDING:待处理,PROCESSING:处理中,COMPLETED:已完成,CLOSED:已关闭)
     */
    @TableField("case_status")
    private String caseStatus;

    /**
     * 案件等级(S1,S2,S3,M1,M2,M3)
     */
    @TableField("case_level")
    private String caseLevel;

    /**
     * 分配催收员ID
     */
    @TableField("assign_user_id")
    private Long assignUserId;

    /**
     * 分配时间
     */
    @TableField("assign_time")
    private LocalDateTime assignTime;

    /**
     * 最后联系时间
     */
    @TableField("last_contact_time")
    private LocalDateTime lastContactTime;

    /**
     * 下次联系时间
     */
    @TableField("next_contact_time")
    private LocalDateTime nextContactTime;

    /**
     * 联系结果
     */
    @TableField("contact_result")
    private String contactResult;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记(0:正常,1:删除)
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 非数据库字段
    /**
     * 客户姓名
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 客户手机号
     */
    @TableField(exist = false)
    private String customerPhone;

    /**
     * 分配催收员姓名
     */
    @TableField(exist = false)
    private String assignUserName;

    /**
     * 已还款金额
     */
    @TableField(exist = false)
    private BigDecimal repaidAmount;

    /**
     * 剩余欠款
     */
    @TableField(exist = false)
    private BigDecimal remainingAmount;

    /**
     * 联系次数
     */
    @TableField(exist = false)
    private Integer contactCount;
}