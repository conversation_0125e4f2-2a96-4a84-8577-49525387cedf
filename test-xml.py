#!/usr/bin/env python3
import xml.etree.ElementTree as ET
import sys

def validate_xml(file_path):
    try:
        tree = ET.parse(file_path)
        print(f"✓ XML file '{file_path}' is well-formed")
        return True
    except ET.ParseError as e:
        print(f"✗ XML parsing error in '{file_path}': {e}")
        return False
    except Exception as e:
        print(f"✗ Error reading '{file_path}': {e}")
        return False

if __name__ == "__main__":
    xml_files = [
        "src/main/resources/mapper/ContactRecordMapper.xml",
        "src/main/resources/mapper/CaseInfoMapper.xml",
        "src/main/resources/mapper/RepaymentRecordMapper.xml"
    ]

    all_valid = True
    for xml_file in xml_files:
        if not validate_xml(xml_file):
            all_valid = False

    if all_valid:
        print("All XML validations passed!")
        sys.exit(0)
    else:
        print("Some XML validations failed!")
        sys.exit(1)
