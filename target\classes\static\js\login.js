// 登录页面JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    // 密码显示/隐藏切换
    const togglePassword = document.getElementById('togglePassword');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }

    // 刷新验证码
    const captchaDisplay = document.getElementById('captchaDisplay');
    if (captchaDisplay) {
        captchaDisplay.addEventListener('click', function() {
            // 发送AJAX请求获取新的验证码
            fetch('/captcha/refresh', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('captchaCode').textContent = data.captcha;
                }
            })
            .catch(error => {
                console.error('刷新验证码失败:', error);
                // 如果请求失败，使用客户端生成
                generateCaptcha();
            });
            
            // 添加刷新动画
            this.classList.add('animate-pulse');
            setTimeout(() => {
                this.classList.remove('animate-pulse');
            }, 500);
        });
    }

    // 客户端生成验证码（备用方案）
    function generateCaptcha() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let code = '';
        for (let i = 0; i < 4; i++) {
            code += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        document.getElementById('captchaCode').textContent = code;
    }

    // 表单提交处理
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 登录中...';
            
            // 如果表单验证失败，恢复按钮状态
            setTimeout(() => {
                if (!this.checkValidity()) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            }, 100);
        });
    }

    // 输入框焦点效果
    const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentNode.classList.add('ring-2', 'ring-secondary/50');
        });
        
        input.addEventListener('blur', function() {
            this.parentNode.classList.remove('ring-2', 'ring-secondary/50');
        });
    });

    // 添加全局动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        .animate-shake {
            animation: shake 0.5s ease-in-out;
        }
        .animate-pulse {
            animation: pulse 0.5s ease-in-out;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    `;
    document.head.appendChild(style);

    // 自动隐藏提示信息
    const alerts = document.querySelectorAll('.bg-red-500\\/20, .bg-green-500\\/20');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.transition = 'opacity 0.5s ease-out';
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 500);
        }, 5000);
    });
});
