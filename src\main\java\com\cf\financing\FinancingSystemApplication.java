package com.cf.financing;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * CF金融催收管理系统启动类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@MapperScan("com.cf.financing.mapper")
@EnableTransactionManagement
public class FinancingSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(FinancingSystemApplication.class, args);
        System.out.println("");
        System.out.println("  ______ ______   ______ _                      _             ");
        System.out.println(" / _____|  ____) |  ____(_)                    (_)            ");
        System.out.println("| /     | |__    | |__   _ _ __   __ _ _ __   ___ _ _ __   __ _ ");
        System.out.println("| |     |  __)   |  __| | | '_ \\ / _` | '_ \\ / __| | '_ \\ / _` |");
        System.out.println("| \\____ | |      | |    | | | | | (_| | | | | (__| | | | | (_| |");
        System.out.println(" \\______|_|      |_|    |_|_| |_|\\__,_|_| |_|\\___|_|_| |_|\\__, |");
        System.out.println("                                                          __/ |");
        System.out.println("                                                         |___/ ");
        System.out.println("");
        System.out.println("CF金融催收管理系统启动成功！");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("API文档: http://localhost:8080/doc.html");
        System.out.println("数据库监控: http://localhost:8080/druid");
    }
}