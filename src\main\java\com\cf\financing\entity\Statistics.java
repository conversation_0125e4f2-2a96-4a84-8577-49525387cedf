package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 统计信息实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("statistics")
public class Statistics implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计日期
     */
    @TableField("stat_date")
    private LocalDate statDate;

    /**
     * 统计类型（1-日统计，2-周统计，3-月统计，4-年统计）
     */
    @TableField("stat_type")
    private Integer statType;

    /**
     * 部门ID
     */
    @TableField("department_id")
    private Long departmentId;

    /**
     * 部门名称
     */
    @TableField("department_name")
    private String departmentName;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 新增案件数量
     */
    @TableField("new_case_count")
    private Integer newCaseCount;

    /**
     * 新增案件金额
     */
    @TableField("new_case_amount")
    private BigDecimal newCaseAmount;

    /**
     * 完成案件数量
     */
    @TableField("completed_case_count")
    private Integer completedCaseCount;

    /**
     * 完成案件金额
     */
    @TableField("completed_case_amount")
    private BigDecimal completedCaseAmount;

    /**
     * 回收金额
     */
    @TableField("recovered_amount")
    private BigDecimal recoveredAmount;

    /**
     * 回收率
     */
    @TableField("recovery_rate")
    private BigDecimal recoveryRate;

    /**
     * 联系次数
     */
    @TableField("contact_count")
    private Integer contactCount;

    /**
     * 有效联系次数
     */
    @TableField("effective_contact_count")
    private Integer effectiveContactCount;

    /**
     * 联系成功率
     */
    @TableField("contact_success_rate")
    private BigDecimal contactSuccessRate;

    /**
     * 任务完成数量
     */
    @TableField("task_completed_count")
    private Integer taskCompletedCount;

    /**
     * 任务完成率
     */
    @TableField("task_completion_rate")
    private BigDecimal taskCompletionRate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;
}