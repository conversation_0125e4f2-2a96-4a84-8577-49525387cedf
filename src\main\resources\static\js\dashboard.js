// Dashboard页面JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    // 初始化图表
    initTrendChart();
    initTeamChart();

    // 加载数据
    loadDashboardData();
    loadRankingData();
    loadCaseDistribution();

    // 初始化进度条动画
    initProgressAnimations();

    // 绑定事件
    bindEvents();

    // 设置定时刷新
    setInterval(() => {
        loadDashboardData();
        loadRankingData();
        loadCaseDistribution();
    }, 300000); // 5分钟刷新一次
});

// 回收趋势折线图
function initTrendChart() {
    const trendCtx = document.getElementById('trendChart');
    if (!trendCtx) return;
    
    const trendChart = new Chart(trendCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: ['12/08', '12/09', '12/10', '12/11', '12/12', '12/13', '12/14'],
            datasets: [{
                label: '回收金额 (万元)',
                data: [32, 45, 28, 51, 42, 39, 48],
                borderColor: '#f6b93b',
                backgroundColor: 'rgba(246, 185, 59, 0.1)',
                borderWidth: 3,
                pointBackgroundColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                fill: true,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 10
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// 团队绩效对比图
function initTeamChart() {
    const teamCtx = document.getElementById('teamChart');
    if (!teamCtx) return;
    
    const teamChart = new Chart(teamCtx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['催收一组', '催收二组', '催收三组', '催收四组'],
            datasets: [{
                label: '回收金额 (万元)',
                data: [186, 152, 124, 98],
                backgroundColor: [
                    'rgba(59, 130, 246, 0.7)',
                    'rgba(16, 185, 129, 0.7)',
                    'rgba(245, 158, 11, 0.7)',
                    'rgba(156, 163, 175, 0.7)'
                ],
                borderColor: [
                    'rgba(59, 130, 246, 1)',
                    'rgba(16, 185, 129, 1)',
                    'rgba(245, 158, 11, 1)',
                    'rgba(156, 163, 175, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 10
                        }
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// 进度条动画效果
function initProgressAnimations() {
    const progressBars = document.querySelectorAll('.progress, .progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
}

// 绑定事件
function bindEvents() {
    // 快速操作按钮事件
    const actionButtons = document.querySelectorAll('.action-btn-top');
    actionButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.querySelector('span').textContent;
            handleQuickAction(action);
        });
    });
    
    // 面板切换按钮事件
    const panelButtons = document.querySelectorAll('.btn');
    panelButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除同级按钮的active类
            const siblings = this.parentNode.querySelectorAll('.btn');
            siblings.forEach(sibling => sibling.classList.remove('active'));
            
            // 添加active类到当前按钮
            this.classList.add('active');
            
            // 处理面板切换逻辑
            handlePanelSwitch(this);
        });
    });
    
    // 下拉选择框事件
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            handleSelectChange(this);
        });
    });
}

// 处理快速操作
function handleQuickAction(action) {
    switch(action) {
        case '导出报表':
            console.log('导出报表功能');
            // 这里可以调用后端API导出报表
            break;
        case '设置提醒':
            console.log('设置提醒功能');
            // 这里可以打开提醒设置对话框
            break;
        case '设置':
            console.log('设置功能');
            // 这里可以跳转到设置页面
            break;
        case '刷新':
            console.log('刷新数据');
            // 这里可以刷新页面数据
            location.reload();
            break;
        default:
            console.log('未知操作:', action);
    }
}

// 处理面板切换
function handlePanelSwitch(button) {
    const buttonText = button.textContent;
    const panel = button.closest('.panel');
    const panelTitle = panel.querySelector('.panel-title').textContent;
    
    console.log(`面板 "${panelTitle}" 切换到 "${buttonText}"`);
    
    // 这里可以根据不同的面板和按钮执行相应的数据更新逻辑
    // 例如：更新图表数据、更新表格数据等
}

// 处理下拉选择框变化
function handleSelectChange(select) {
    const selectedValue = select.value;
    const panel = select.closest('.panel');
    const panelTitle = panel.querySelector('.panel-title').textContent;
    
    console.log(`面板 "${panelTitle}" 选择了 "${selectedValue}"`);
    
    // 这里可以根据选择的值更新相应的数据
    // 例如：更新时间范围、更新数据源等
}

// 工具函数：格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 工具函数：格式化货币
function formatCurrency(amount) {
    return '¥' + amount.toLocaleString();
}

// 加载仪表板数据
function loadDashboardData() {
    fetch('/api/statistics/dashboard')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                updateStats(data.data);
            }
        })
        .catch(error => {
            console.error('加载仪表板数据失败:', error);
            // 使用模拟数据
            updateStats({
                totalCases: 2587,
                totalAmount: 8740000,
                todayRecovery: 356800,
                avgRecoveryRate: 74.6
            });
        });
}

// 加载排行榜数据
function loadRankingData() {
    fetch('/api/statistics/ranking?type=personal&period=today')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                renderRanking(data.data);
            }
        })
        .catch(error => {
            console.error('加载排行榜数据失败:', error);
            // 使用模拟数据
            renderRanking([
                { rank: 1, name: '张某某', team: '催收一组', amount: 68500, rate: 92 },
                { rank: 2, name: '李某某', team: '催收二组', amount: 52300, rate: 85 },
                { rank: 3, name: '王某某', team: '催收三组', amount: 48800, rate: 78 },
                { rank: 4, name: '陈某某', team: '催收一组', amount: 42100, rate: 72 },
                { rank: 5, name: '刘某某', team: '催收二组', amount: 38900, rate: 68 }
            ]);
        });
}

// 渲染排行榜
function renderRanking(rankings) {
    const container = document.getElementById('rankingList');
    if (!container) return;

    container.innerHTML = '';

    rankings.forEach(item => {
        const div = document.createElement('div');
        div.className = item.rank === 1 ? 'top-performer' : 'performer';

        div.innerHTML = `
            <div class="rank">${item.rank}</div>
            <div class="user-info">
                <div class="avatar">${item.name.charAt(0)}</div>
                <div class="details">
                    <div class="name">${item.name}</div>
                    <div class="team">${item.team}</div>
                </div>
            </div>
            <div class="performance-data">
                <div class="amount">¥${(item.amount / 1000).toFixed(1)}K</div>
                <div class="progress-container">
                    <div class="progress" style="width: ${item.rate}%"></div>
                    <span>${item.rate}%</span>
                </div>
            </div>
        `;

        container.appendChild(div);
    });
}

// 加载案件分布数据
function loadCaseDistribution() {
    fetch('/api/statistics/case-distribution?type=department')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                renderCaseDistribution(data.data);
            }
        })
        .catch(error => {
            console.error('加载案件分布数据失败:', error);
            // 使用模拟数据
            renderCaseDistribution([
                { name: '催收一组', cases: 856, amount: 1240000, progress: 73 },
                { name: '催收二组', cases: 732, amount: 1078000, progress: 68 },
                { name: '催收三组', cases: 654, amount: 896500, progress: 62 },
                { name: '催收四组', cases: 512, amount: 745200, progress: 58 }
            ]);
        });
}

// 渲染案件分布
function renderCaseDistribution(distributions) {
    const tbody = document.getElementById('caseDistributionBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    distributions.forEach(item => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td class="highlight">${item.name}</td>
            <td>${item.cases.toLocaleString()}</td>
            <td>¥${item.amount.toLocaleString()}</td>
            <td class="progress-cell">
                ${item.progress}%
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${item.progress}%"></div>
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 工具函数：更新统计数据
function updateStats(stats) {
    if (stats.totalCases !== undefined) {
        const element = document.querySelector('.overview-card.warning .value');
        if (element) element.textContent = formatNumber(stats.totalCases);
    }
    if (stats.totalAmount !== undefined) {
        const element = document.querySelector('.overview-card.primary .value');
        if (element) element.textContent = formatCurrency(stats.totalAmount);
    }
    if (stats.todayRecovery !== undefined) {
        const element = document.querySelector('.overview-card.success .value');
        if (element) element.textContent = formatCurrency(stats.todayRecovery);
    }
    if (stats.avgRecoveryRate !== undefined) {
        const element = document.querySelector('.overview-card.info .value');
        if (element) element.textContent = stats.avgRecoveryRate.toFixed(1) + '%';
    }
}
