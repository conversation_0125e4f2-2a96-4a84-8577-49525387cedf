package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.TaskList;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 作业清单服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ITaskListService extends IService<TaskList> {

    /**
     * 分页查询作业清单
     *
     * @param page 分页参数
     * @param taskNo 任务编号
     * @param caseNo 案件编号
     * @param customerName 客户姓名
     * @param taskType 任务类型
     * @param taskStatus 任务状态
     * @param assignedUserId 分配人员ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<TaskList> getTaskListPage(Page<TaskList> page,
                                   String taskNo,
                                   String caseNo,
                                   String customerName,
                                   Integer taskType,
                                   Integer taskStatus,
                                   Long assignedUserId,
                                   LocalDateTime startTime,
                                   LocalDateTime endTime);

    /**
     * 创建新任务
     *
     * @param taskList 任务信息
     * @return 是否成功
     */
    boolean createTask(TaskList taskList);

    /**
     * 更新任务信息
     *
     * @param taskList 任务信息
     * @return 是否成功
     */
    boolean updateTask(TaskList taskList);

    /**
     * 分配任务
     *
     * @param taskId 任务ID
     * @param assignedUserId 分配人员ID
     * @param assignedUserName 分配人员姓名
     * @param assignBy 分配人
     * @return 是否成功
     */
    boolean assignTask(Long taskId, Long assignedUserId, String assignedUserName, String assignBy);

    /**
     * 开始任务
     *
     * @param taskId 任务ID
     * @param startBy 开始人
     * @return 是否成功
     */
    boolean startTask(Long taskId, String startBy);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param result 处理结果
     * @param remark 备注
     * @param completeBy 完成人
     * @return 是否成功
     */
    boolean completeTask(Long taskId, String result, String remark, String completeBy);

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @param cancelReason 取消原因
     * @param cancelBy 取消人
     * @return 是否成功
     */
    boolean cancelTask(Long taskId, String cancelReason, String cancelBy);

    /**
     * 根据用户ID查询待处理任务
     *
     * @param userId 用户ID
     * @return 待处理任务列表
     */
    List<TaskList> getPendingTasksByUserId(Long userId);

    /**
     * 根据案件ID查询相关任务
     *
     * @param caseId 案件ID
     * @return 任务列表
     */
    List<TaskList> getTasksByCaseId(Long caseId);

    /**
     * 统计任务状态分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param assignedUserId 分配人员ID（可选）
     * @return 状态统计结果
     */
    List<Map<String, Object>> getTaskStatusStatistics(LocalDateTime startTime,
                                                      LocalDateTime endTime,
                                                      Long assignedUserId);

    /**
     * 统计任务类型分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param assignedUserId 分配人员ID（可选）
     * @return 类型统计结果
     */
    List<Map<String, Object>> getTaskTypeStatistics(LocalDateTime startTime,
                                                    LocalDateTime endTime,
                                                    Long assignedUserId);

    /**
     * 查询逾期任务
     *
     * @return 逾期任务列表
     */
    List<TaskList> getOverdueTasks();

    /**
     * 批量更新任务状态
     *
     * @param taskIds 任务ID列表
     * @param taskStatus 新状态
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean batchUpdateTaskStatus(List<Long> taskIds, Integer taskStatus, String updateBy);

    /**
     * 生成任务编号
     *
     * @return 任务编号
     */
    String generateTaskNo();

    /**
     * 根据案件自动创建任务
     *
     * @param caseId 案件ID
     * @param caseNo 案件编号
     * @param customerName 客户姓名
     * @param createBy 创建人
     * @return 是否成功
     */
    boolean autoCreateTasksByCase(Long caseId, String caseNo, String customerName, String createBy);
}