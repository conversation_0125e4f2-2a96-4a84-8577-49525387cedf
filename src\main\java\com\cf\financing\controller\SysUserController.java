package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.SysUser;
import com.cf.financing.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统用户控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/sys-user")
@RequiredArgsConstructor
public class SysUserController {

    private final ISysUserService sysUserService;

    /**
     * 分页查询用户列表
     */
        @GetMapping("/page")
    public Map<String, Object> getUserPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String realName,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long deptId,
            @RequestParam(required = false) Long roleId) {
        
        Page<SysUser> page = new Page<>(current, size);
        IPage<SysUser> result = sysUserService.getUserPage(
                page, username, realName, phone, email, status, deptId, roleId
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 根据ID查询用户详情
     */
        @GetMapping("/{userId}")
    public Map<String, Object> getUserById(
            @PathVariable Long userId) {
        
        SysUser user = sysUserService.getUserById(userId);
        
        Map<String, Object> response = new HashMap<>();
        if (user != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", user);
        } else {
            response.put("code", 404);
            response.put("message", "用户不存在");
        }
        return response;
    }

    /**
     * 根据用户名查询用户
     */
        @GetMapping("/username/{username}")
    public Map<String, Object> getUserByUsername(
            @PathVariable String username) {
        
        SysUser user = sysUserService.getUserByUsername(username);
        
        Map<String, Object> response = new HashMap<>();
        if (user != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", user);
        } else {
            response.put("code", 404);
            response.put("message", "用户不存在");
        }
        return response;
    }

    /**
     * 根据部门ID查询用户列表
     */
        @GetMapping("/dept/{deptId}")
    public Map<String, Object> getUsersByDeptId(
            @PathVariable Long deptId) {
        
        List<SysUser> users = sysUserService.getUsersByDeptId(deptId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", users);
        return response;
    }

    /**
     * 根据角色ID查询用户列表
     */
        @GetMapping("/role/{roleId}")
    public Map<String, Object> getUsersByRoleId(
            @PathVariable Long roleId) {
        
        List<SysUser> users = sysUserService.getUsersByRoleId(roleId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", users);
        return response;
    }

    /**
     * 创建用户
     */
        @PostMapping
    public Map<String, Object> createUser(@RequestBody SysUser user) {
        boolean success = sysUserService.createUser(user);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "创建成功");
            response.put("data", user);
        } else {
            response.put("code", 400);
            response.put("message", "创建失败，用户名、手机号或邮箱可能已存在");
        }
        return response;
    }

    /**
     * 更新用户信息
     */
        @PutMapping("/{userId}")
    public Map<String, Object> updateUser(
            @PathVariable Long userId,
            @RequestBody SysUser user) {
        
        user.setId(userId);
        boolean success = sysUserService.updateUser(user);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "更新失败，用户名、手机号或邮箱可能已存在");
        }
        return response;
    }

    /**
     * 删除用户
     */
        @DeleteMapping("/{userId}")
    public Map<String, Object> deleteUser(
            @PathVariable Long userId) {
        
        boolean success = sysUserService.deleteUser(userId);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "删除失败");
        }
        return response;
    }

    /**
     * 批量删除用户
     */
        @DeleteMapping("/batch")
    public Map<String, Object> batchDeleteUsers(@RequestBody List<Long> userIds) {
        boolean success = sysUserService.batchDeleteUsers(userIds);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量删除失败");
        }
        return response;
    }

    /**
     * 用户登录
     */
        @PostMapping("/login")
    public Map<String, Object> login(
            @RequestBody Map<String, String> loginRequest,
            HttpServletRequest request) {
        
        String username = loginRequest.get("username");
        String password = loginRequest.get("password");
        String loginIp = getClientIpAddress(request);
        
        SysUser user = sysUserService.login(username, password, loginIp);
        
        Map<String, Object> response = new HashMap<>();
        if (user != null) {
            response.put("code", 200);
            response.put("message", "登录成功");
            response.put("data", user);
        } else {
            response.put("code", 401);
            response.put("message", "用户名或密码错误，或用户已被禁用");
        }
        return response;
    }

    /**
     * 修改密码
     */
        @PutMapping("/{userId}/password")
    public Map<String, Object> changePassword(
            @PathVariable Long userId,
            @RequestBody Map<String, String> passwordRequest) {
        
        String oldPassword = passwordRequest.get("oldPassword");
        String newPassword = passwordRequest.get("newPassword");
        
        boolean success = sysUserService.changePassword(userId, oldPassword, newPassword);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "密码修改成功");
        } else {
            response.put("code", 400);
            response.put("message", "密码修改失败，请检查原密码是否正确");
        }
        return response;
    }

    /**
     * 重置密码
     */
        @PutMapping("/{userId}/reset-password")
    public Map<String, Object> resetPassword(
            @PathVariable Long userId,
            @RequestBody Map<String, String> passwordRequest) {
        
        String newPassword = passwordRequest.get("newPassword");
        
        boolean success = sysUserService.resetPassword(userId, newPassword);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "密码重置成功");
        } else {
            response.put("code", 400);
            response.put("message", "密码重置失败");
        }
        return response;
    }

    /**
     * 更新用户状态
     */
        @PutMapping("/{userId}/status")
    public Map<String, Object> updateUserStatus(
            @PathVariable Long userId,
            @RequestBody Map<String, String> statusRequest) {
        
        String status = statusRequest.get("status");
        
        boolean success = sysUserService.updateUserStatus(userId, status);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "状态更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "状态更新失败");
        }
        return response;
    }

    /**
     * 批量更新用户状态
     */
        @PutMapping("/batch/status")
    public Map<String, Object> batchUpdateUserStatus(
            @RequestBody Map<String, Object> request) {
        
        @SuppressWarnings("unchecked")
        List<Long> userIds = (List<Long>) request.get("userIds");
        String status = (String) request.get("status");
        
        boolean success = sysUserService.batchUpdateUserStatus(userIds, status);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量状态更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量状态更新失败");
        }
        return response;
    }

    /**
     * 获取用户统计信息
     */
        @GetMapping("/statistics")
    public Map<String, Object> getUserStatistics(
            @RequestParam(required = false) Long deptId) {
        
        Map<String, Object> statistics = sysUserService.getUserStatistics(deptId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取在线用户列表
     */
        @GetMapping("/online")
    public Map<String, Object> getOnlineUsers() {
        List<SysUser> onlineUsers = sysUserService.getOnlineUsers();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", onlineUsers);
        return response;
    }

    /**
     * 检查用户名是否存在
     */
        @GetMapping("/check-username")
    public Map<String, Object> checkUsernameExists(
            @RequestParam String username,
            @RequestParam(required = false) Long excludeUserId) {
        
        boolean exists = sysUserService.checkUsernameExists(username, excludeUserId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", exists);
        return response;
    }

    /**
     * 检查手机号是否存在
     */
        @GetMapping("/check-phone")
    public Map<String, Object> checkPhoneExists(
            @RequestParam String phone,
            @RequestParam(required = false) Long excludeUserId) {
        
        boolean exists = sysUserService.checkPhoneExists(phone, excludeUserId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", exists);
        return response;
    }

    /**
     * 检查邮箱是否存在
     */
        @GetMapping("/check-email")
    public Map<String, Object> checkEmailExists(
            @RequestParam String email,
            @RequestParam(required = false) Long excludeUserId) {
        
        boolean exists = sysUserService.checkEmailExists(email, excludeUserId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", exists);
        return response;
    }

    /**
     * 生成用户编号
     */
        @GetMapping("/generate-user-no")
    public Map<String, Object> generateUserNo() {
        String userNo = sysUserService.generateUserNo();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "生成成功");
        response.put("data", userNo);
        return response;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}