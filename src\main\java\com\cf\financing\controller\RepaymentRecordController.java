package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.RepaymentRecord;
import com.cf.financing.service.IRepaymentRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 还款记录控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Api(tags = "还款记录管理")
@RestController
@RequestMapping("/api/repayment-record")
@RequiredArgsConstructor
public class RepaymentRecordController {

    private final IRepaymentRecordService repaymentRecordService;

    /**
     * 分页查询还款记录列表
     */
    @ApiOperation("分页查询还款记录列表")
    @GetMapping("/page")
    public Map<String, Object> getRepaymentRecordPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("案件ID") @RequestParam(required = false) Long caseId,
            @ApiParam("客户ID") @RequestParam(required = false) Long customerId,
            @ApiParam("还款编号") @RequestParam(required = false) String repaymentNo,
            @ApiParam("还款状态") @RequestParam(required = false) String repaymentStatus,
            @ApiParam("还款方式") @RequestParam(required = false) String repaymentType) {
        
        Page<RepaymentRecord> page = new Page<>(current, size);
        IPage<RepaymentRecord> result = repaymentRecordService.getRepaymentRecordPage(
                page, caseId, customerId, repaymentNo, repaymentStatus, repaymentType
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 根据案件ID查询还款记录
     */
    @ApiOperation("根据案件ID查询还款记录")
    @GetMapping("/case/{caseId}")
    public Map<String, Object> getRepaymentRecordsByCaseId(
            @ApiParam("案件ID") @PathVariable Long caseId) {
        
        List<RepaymentRecord> records = repaymentRecordService.getRepaymentRecordsByCaseId(caseId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 根据客户ID查询还款记录
     */
    @ApiOperation("根据客户ID查询还款记录")
    @GetMapping("/customer/{customerId}")
    public Map<String, Object> getRepaymentRecordsByCustomerId(
            @ApiParam("客户ID") @PathVariable Long customerId) {
        
        List<RepaymentRecord> records = repaymentRecordService.getRepaymentRecordsByCustomerId(customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 根据还款编号查询还款记录
     */
    @ApiOperation("根据还款编号查询还款记录")
    @GetMapping("/no/{repaymentNo}")
    public Map<String, Object> getRepaymentRecordByNo(
            @ApiParam("还款编号") @PathVariable String repaymentNo) {
        
        RepaymentRecord record = repaymentRecordService.getRepaymentRecordByNo(repaymentNo);
        
        Map<String, Object> response = new HashMap<>();
        if (record != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", record);
        } else {
            response.put("code", 404);
            response.put("message", "还款记录不存在");
        }
        return response;
    }

    /**
     * 根据交易流水号查询还款记录
     */
    @ApiOperation("根据交易流水号查询还款记录")
    @GetMapping("/transaction/{transactionNo}")
    public Map<String, Object> getRepaymentRecordByTransactionNo(
            @ApiParam("交易流水号") @PathVariable String transactionNo) {
        
        RepaymentRecord record = repaymentRecordService.getRepaymentRecordByTransactionNo(transactionNo);
        
        Map<String, Object> response = new HashMap<>();
        if (record != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", record);
        } else {
            response.put("code", 404);
            response.put("message", "还款记录不存在");
        }
        return response;
    }

    /**
     * 创建还款记录
     */
    @ApiOperation("创建还款记录")
    @PostMapping
    public Map<String, Object> createRepaymentRecord(@RequestBody RepaymentRecord record) {
        boolean success = repaymentRecordService.createRepaymentRecord(record);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "创建成功");
            response.put("data", record);
        } else {
            response.put("code", 400);
            response.put("message", "创建失败，还款编号或交易流水号可能已存在");
        }
        return response;
    }

    /**
     * 更新还款记录
     */
    @ApiOperation("更新还款记录")
    @PutMapping("/{recordId}")
    public Map<String, Object> updateRepaymentRecord(
            @ApiParam("记录ID") @PathVariable Long recordId,
            @RequestBody RepaymentRecord record) {
        
        record.setId(recordId);
        boolean success = repaymentRecordService.updateRepaymentRecord(record);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "更新失败，还款编号或交易流水号可能已存在");
        }
        return response;
    }

    /**
     * 删除还款记录
     */
    @ApiOperation("删除还款记录")
    @DeleteMapping("/{recordId}")
    public Map<String, Object> deleteRepaymentRecord(
            @ApiParam("记录ID") @PathVariable Long recordId) {
        
        boolean success = repaymentRecordService.deleteRepaymentRecord(recordId);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "删除失败");
        }
        return response;
    }

    /**
     * 批量删除还款记录
     */
    @ApiOperation("批量删除还款记录")
    @DeleteMapping("/batch")
    public Map<String, Object> batchDeleteRepaymentRecords(@RequestBody List<Long> recordIds) {
        boolean success = repaymentRecordService.batchDeleteRepaymentRecords(recordIds);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量删除失败");
        }
        return response;
    }

    /**
     * 获取案件总还款金额
     */
    @ApiOperation("获取案件总还款金额")
    @GetMapping("/total-amount/case/{caseId}")
    public Map<String, Object> getTotalRepaymentAmountByCaseId(
            @ApiParam("案件ID") @PathVariable Long caseId) {
        
        BigDecimal totalAmount = repaymentRecordService.getTotalRepaymentAmountByCaseId(caseId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", totalAmount);
        return response;
    }

    /**
     * 获取客户总还款金额
     */
    @ApiOperation("获取客户总还款金额")
    @GetMapping("/total-amount/customer/{customerId}")
    public Map<String, Object> getTotalRepaymentAmountByCustomerId(
            @ApiParam("客户ID") @PathVariable Long customerId) {
        
        BigDecimal totalAmount = repaymentRecordService.getTotalRepaymentAmountByCustomerId(customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", totalAmount);
        return response;
    }

    /**
     * 获取还款统计信息
     */
    @ApiOperation("获取还款统计信息")
    @GetMapping("/statistics")
    public Map<String, Object> getRepaymentStatistics(
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Map<String, Object> statistics = repaymentRecordService.getRepaymentStatistics(startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取还款趋势数据
     */
    @ApiOperation("获取还款趋势数据")
    @GetMapping("/trend")
    public Map<String, Object> getRepaymentTrend(
            @ApiParam("天数") @RequestParam(defaultValue = "30") Integer days) {
        
        List<Map<String, Object>> trend = repaymentRecordService.getRepaymentTrend(days);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", trend);
        return response;
    }

    /**
     * 获取还款方式统计
     */
    @ApiOperation("获取还款方式统计")
    @GetMapping("/type-statistics")
    public Map<String, Object> getRepaymentTypeStatistics(
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<Map<String, Object>> statistics = repaymentRecordService.getRepaymentTypeStatistics(startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取今日还款记录
     */
    @ApiOperation("获取今日还款记录")
    @GetMapping("/today")
    public Map<String, Object> getTodayRepaymentRecords() {
        List<RepaymentRecord> records = repaymentRecordService.getTodayRepaymentRecords();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 获取待确认还款记录
     */
    @ApiOperation("获取待确认还款记录")
    @GetMapping("/pending")
    public Map<String, Object> getPendingRepaymentRecords() {
        List<RepaymentRecord> records = repaymentRecordService.getPendingRepaymentRecords();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 批量更新还款状态
     */
    @ApiOperation("批量更新还款状态")
    @PutMapping("/batch/status")
    public Map<String, Object> batchUpdateRepaymentStatus(
            @RequestBody Map<String, Object> request) {
        
        @SuppressWarnings("unchecked")
        List<Long> recordIds = (List<Long>) request.get("recordIds");
        String status = (String) request.get("status");
        
        boolean success = repaymentRecordService.batchUpdateRepaymentStatus(recordIds, status);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量更新状态成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量更新状态失败");
        }
        return response;
    }

    /**
     * 检查还款编号是否存在
     */
    @ApiOperation("检查还款编号是否存在")
    @GetMapping("/check-repayment-no")
    public Map<String, Object> checkRepaymentNoExists(
            @ApiParam("还款编号") @RequestParam String repaymentNo,
            @ApiParam("排除的记录ID") @RequestParam(required = false) Long excludeRecordId) {
        
        boolean exists = repaymentRecordService.checkRepaymentNoExists(repaymentNo, excludeRecordId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", exists);
        return response;
    }

    /**
     * 检查交易流水号是否存在
     */
    @ApiOperation("检查交易流水号是否存在")
    @GetMapping("/check-transaction-no")
    public Map<String, Object> checkTransactionNoExists(
            @ApiParam("交易流水号") @RequestParam String transactionNo,
            @ApiParam("排除的记录ID") @RequestParam(required = false) Long excludeRecordId) {
        
        boolean exists = repaymentRecordService.checkTransactionNoExists(transactionNo, excludeRecordId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", exists);
        return response;
    }

    /**
     * 获取月度还款统计
     */
    @ApiOperation("获取月度还款统计")
    @GetMapping("/monthly-statistics")
    public Map<String, Object> getMonthlyRepaymentStatistics(
            @ApiParam("月份数") @RequestParam(defaultValue = "12") Integer months) {
        
        List<Map<String, Object>> statistics = repaymentRecordService.getMonthlyRepaymentStatistics(months);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取操作员还款排行
     */
    @ApiOperation("获取操作员还款排行")
    @GetMapping("/operator-ranking")
    public Map<String, Object> getOperatorRepaymentRanking(
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        
        List<Map<String, Object>> ranking = repaymentRecordService.getOperatorRepaymentRanking(limit);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", ranking);
        return response;
    }

    /**
     * 生成还款编号
     */
    @ApiOperation("生成还款编号")
    @GetMapping("/generate-repayment-no")
    public Map<String, Object> generateRepaymentNo() {
        String repaymentNo = repaymentRecordService.generateRepaymentNo();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "生成成功");
        response.put("data", repaymentNo);
        return response;
    }
}