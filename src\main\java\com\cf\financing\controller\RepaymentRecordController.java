package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.RepaymentRecord;
import com.cf.financing.service.IRepaymentRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 还款记录控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/repayment-record")
@RequiredArgsConstructor
public class RepaymentRecordController {

    private final IRepaymentRecordService repaymentRecordService;

    /**
     * 分页查询还款记录列表
     */
        @GetMapping("/page")
    public Map<String, Object> getRepaymentRecordPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long caseId,
            @RequestParam(required = false) Long customerId,
            @RequestParam(required = false) String repaymentNo,
            @RequestParam(required = false) String repaymentStatus,
            @RequestParam(required = false) String repaymentType) {
        
        Page<RepaymentRecord> page = new Page<>(current, size);
        IPage<RepaymentRecord> result = repaymentRecordService.getRepaymentPage(
                page, caseId, customerId, repaymentNo, repaymentType, repaymentStatus, null, null, null
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 根据案件ID查询还款记录
     */
        @GetMapping("/case/{caseId}")
    public Map<String, Object> getRepaymentRecordsByCaseId(
            @PathVariable Long caseId) {
        
        List<RepaymentRecord> records = repaymentRecordService.getRepaymentsByCaseId(caseId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 根据客户ID查询还款记录
     */
        @GetMapping("/customer/{customerId}")
    public Map<String, Object> getRepaymentRecordsByCustomerId(
            @PathVariable Long customerId) {
        
        List<RepaymentRecord> records = repaymentRecordService.getRepaymentsByCustomerId(customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 根据还款编号查询还款记录
     */
        @GetMapping("/no/{repaymentNo}")
    public Map<String, Object> getRepaymentRecordByNo(
            @PathVariable String repaymentNo) {
        
        RepaymentRecord record = repaymentRecordService.getRepaymentByNo(repaymentNo);
        
        Map<String, Object> response = new HashMap<>();
        if (record != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", record);
        } else {
            response.put("code", 404);
            response.put("message", "还款记录不存在");
        }
        return response;
    }

    /**
     * 根据交易流水号查询还款记录
     */
        @GetMapping("/transaction/{transactionNo}")
    public Map<String, Object> getRepaymentRecordByTransactionNo(
            @PathVariable String transactionNo) {
        
        RepaymentRecord record = repaymentRecordService.getRepaymentByTransactionNo(transactionNo);
        
        Map<String, Object> response = new HashMap<>();
        if (record != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", record);
        } else {
            response.put("code", 404);
            response.put("message", "还款记录不存在");
        }
        return response;
    }

    /**
     * 创建还款记录
     */
        @PostMapping
    public Map<String, Object> createRepaymentRecord(@RequestBody RepaymentRecord record) {
        boolean success = repaymentRecordService.createRepayment(record);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "创建成功");
            response.put("data", record);
        } else {
            response.put("code", 400);
            response.put("message", "创建失败，还款编号或交易流水号可能已存在");
        }
        return response;
    }

    /**
     * 更新还款记录
     */
        @PutMapping("/{recordId}")
    public Map<String, Object> updateRepaymentRecord(
            @PathVariable Long recordId,
            @RequestBody RepaymentRecord record) {
        
        record.setId(recordId);
        boolean success = repaymentRecordService.updateRepayment(record);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "更新失败，还款编号或交易流水号可能已存在");
        }
        return response;
    }

    /**
     * 删除还款记录
     */
        @DeleteMapping("/{recordId}")
    public Map<String, Object> deleteRepaymentRecord(
            @PathVariable Long recordId) {
        
        boolean success = repaymentRecordService.deleteRepayment(recordId);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "删除失败");
        }
        return response;
    }

    /**
     * 批量删除还款记录
     */
        @DeleteMapping("/batch")
    public Map<String, Object> batchDeleteRepaymentRecords(@RequestBody List<Long> recordIds) {
        boolean success = repaymentRecordService.batchDeleteRepayments(recordIds);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量删除失败");
        }
        return response;
    }

    /**
     * 获取案件总还款金额
     */
        @GetMapping("/total-amount/case/{caseId}")
    public Map<String, Object> getTotalRepaymentAmountByCaseId(
            @PathVariable Long caseId) {
        
        BigDecimal totalAmount = repaymentRecordService.getTotalRepaymentByCaseId(caseId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", totalAmount);
        return response;
    }

    /**
     * 获取客户总还款金额
     */
        @GetMapping("/total-amount/customer/{customerId}")
    public Map<String, Object> getTotalRepaymentAmountByCustomerId(
            @PathVariable Long customerId) {
        
        BigDecimal totalAmount = repaymentRecordService.getTotalRepaymentByCustomerId(customerId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", totalAmount);
        return response;
    }

    /**
     * 获取还款统计信息
     */
        @GetMapping("/statistics")
    public Map<String, Object> getRepaymentStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Map<String, Object> statistics = repaymentRecordService.getRepaymentStatistics(startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取还款趋势数据
     */
        @GetMapping("/trend")
    public Map<String, Object> getRepaymentTrend(
            @RequestParam(defaultValue = "30") Integer days) {
        
        List<Map<String, Object>> trend = repaymentRecordService.getRepaymentTrend(days);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", trend);
        return response;
    }

    /**
     * 获取还款方式统计
     */
        @GetMapping("/type-statistics")
    public Map<String, Object> getRepaymentTypeStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        List<Map<String, Object>> statistics = repaymentRecordService.getRepaymentTypeStatistics(startDate, endDate);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取今日还款记录
     */
        @GetMapping("/today")
    public Map<String, Object> getTodayRepaymentRecords() {
        List<RepaymentRecord> records = repaymentRecordService.getTodayRepayments();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 获取待确认还款记录
     */
        @GetMapping("/pending")
    public Map<String, Object> getPendingRepaymentRecords() {
        List<RepaymentRecord> records = repaymentRecordService.getPendingRepayments();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", records);
        return response;
    }

    /**
     * 批量更新还款状态
     */
        @PutMapping("/batch/status")
    public Map<String, Object> batchUpdateRepaymentStatus(
            @RequestBody Map<String, Object> request) {
        
        @SuppressWarnings("unchecked")
        List<Long> recordIds = (List<Long>) request.get("recordIds");
        String status = (String) request.get("status");
        
        // 需要获取操作员ID，这里暂时使用null，实际应该从当前登录用户获取
        boolean success = repaymentRecordService.batchUpdateStatus(recordIds, status, null);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量更新状态成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量更新状态失败");
        }
        return response;
    }

    /**
     * 检查还款编号是否存在
     */
        @GetMapping("/check-repayment-no")
    public Map<String, Object> checkRepaymentNoExists(
            @RequestParam String repaymentNo,
            @RequestParam(required = false) Long excludeRecordId) {
        
        boolean exists = repaymentRecordService.checkRepaymentNoExists(repaymentNo, excludeRecordId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", exists);
        return response;
    }

    /**
     * 检查交易流水号是否存在
     */
        @GetMapping("/check-transaction-no")
    public Map<String, Object> checkTransactionNoExists(
            @RequestParam String transactionNo,
            @RequestParam(required = false) Long excludeRecordId) {
        
        boolean exists = repaymentRecordService.checkTransactionNoExists(transactionNo, excludeRecordId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", exists);
        return response;
    }

    /**
     * 获取月度还款统计
     */
        @GetMapping("/monthly-statistics")
    public Map<String, Object> getMonthlyRepaymentStatistics(
            @RequestParam(defaultValue = "12") Integer months) {
        
        List<Map<String, Object>> statistics = repaymentRecordService.getMonthlyRepaymentStatistics(months);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取操作员还款排行
     */
        @GetMapping("/operator-ranking")
    public Map<String, Object> getOperatorRepaymentRanking(
            @RequestParam(defaultValue = "10") Integer limit) {
        
        // 需要传入日期范围，这里使用默认值
        List<Map<String, Object>> ranking = repaymentRecordService.getOperatorRepaymentRanking(null, null, limit);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", ranking);
        return response;
    }

    /**
     * 生成还款编号
     */
        @GetMapping("/generate-repayment-no")
    public Map<String, Object> generateRepaymentNo() {
        String repaymentNo = repaymentRecordService.generateRepaymentNo();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "生成成功");
        response.put("data", repaymentNo);
        return response;
    }
}