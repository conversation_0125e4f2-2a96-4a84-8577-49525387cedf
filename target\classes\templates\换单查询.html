<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>换单查询系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        :root {
            --primary-color: #165DFF;
            --primary-light: #E8F3FF;
            --success-color: #00B42A;
            --success-light: #E8FFEF;
            --danger-color: #F53F3F;
            --danger-light: #FFF2F2;
            --text-color: #1D2129;
            --text-secondary: #4E5969;
            --bg-color: #F7F8FA;
            --card-bg: #FFFFFF;
            --border-color: #E5E6EB;
            --hover-bg: #F2F3F5;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            padding: 10px;
            display: flex;
            justify-content: center;
            font-size: 14px;
            overflow: hidden; /* 移除外部滚动条 */
        }
        
        .app-container {
            width: 100%;
            max-width: 1400px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            background: var(--card-bg);
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 900px;
        }
        
        .app-container:hover {
            box-shadow: var(--shadow-lg);
        }
        
        /* 导航栏 */
        .app-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14px 20px;
            border-bottom: 1px solid var(--border-color);
            background: var(--card-bg);
            position: relative;
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .back-btn {
            padding: 7px 14px;
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: var(--transition);
            opacity: 0;
            pointer-events: none;
        }
        
        .back-btn:hover {
            background: #E5E6EB;
        }
        
        .back-btn.active {
            opacity: 1;
            pointer-events: auto;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        /* 筛选框区域 */
        .query-form {
            padding: 18px 20px;
            background: var(--card-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 10;
        }
        
        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .form-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
        }
        
        @media (max-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 480px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        
        .form-group label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
            white-space: normal; /* 允许标签文本换行 */
        }
        
        .form-control {
            padding: 8px 12px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 13px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.2);
        }
        
        .form-control::placeholder {
            color: #C9CDD4;
        }
        
        .form-control i {
            color: #86909C;
            font-size: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: var(--transition);
            justify-content: center;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: #0D47A1;
            box-shadow: 0 2px 8px rgba(22, 93, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .btn-primary:active {
            transform: translateY(0);
        }
        
        .btn-secondary {
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            background: #E5E6EB;
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 12px;
            justify-content: flex-end;
        }
        
        /* 统计信息区域 */
        .summary-info {
            background: var(--card-bg);
            padding: 10px 20px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            border-bottom: 1px solid var(--border-color);
            position: relative;
            z-index: 5;
        }
        
        @media (max-width: 768px) {
            .summary-info {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 480px) {
            .summary-info {
                grid-template-columns: 1fr;
            }
        }
        
        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            background: var(--primary-light);
            border-radius: 6px;
            transition: var(--transition);
        }
        
        .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .summary-value {
            font-size: 16px;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1.2;
            margin-bottom: 3px;
        }
        
        .summary-value.success {
            color: var(--success-color);
        }
        
        .summary-value.danger {
            color: var(--danger-color);
        }
        
        .summary-label {
            font-size: 11px;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        /* 记录展示区域 */
        .table-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative; /* 建立定位上下文 */
        }
        
        .table-content {
            flex: 1;
            overflow-y: auto;
            padding: 0; /* 移除内边距，避免表头与内容之间的缝隙 */
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            min-width: 800px;
        }
        
        .data-table th {
            text-align: left;
            padding: 10px 12px;
            background-color: #F2F3F5;
            color: var(--text-secondary);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 150; /* 显著提高表头层级，确保覆盖内容 */
            border-bottom: 2px solid var(--border-color);
            font-size: 12px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1); /* 添加阴影增强层次感 */
        }
        
        .data-table td {
            padding: 10px 12px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-color);
            transition: var(--transition);
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .data-table tr:hover {
            background: #F9FAFB;
        }
        
        .data-table tr.selected {
            background: var(--primary-light);
        }
        
        .data-table .checkbox-cell {
            width: 36px;
            text-align: center;
        }
        
        .data-table .highlight {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .status-tag {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }
        
        .status-tag.success {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .status-tag.danger {
            background: var(--danger-light);
            color: var(--danger-color);
        }
        
        .action-btn {
            padding: 4px 8px;
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            display: inline-flex;
            align-items: center;
            gap: 2px;
            transition: var(--transition);
        }
        
        .action-btn:hover {
            background: #E5E6EB;
            transform: translateY(-1px);
        }
        
        .action-btn:active {
            transform: translateY(0);
        }
        
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-top: 1px solid var(--border-color);
            background: var(--card-bg);
            font-size: 12px;
        }
        
        .pagination-controls {
            display: flex;
            gap: 5px;
        }
        
        .pagination-btn {
            padding: 6px 10px;
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            transition: var(--transition);
            font-size: 11px;
            min-width: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .pagination-btn:hover {
            background: #E5E6EB;
        }
        
        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            font-weight: bold;
            border-color: var(--primary-color);
        }
        
        .pagination-btn:disabled {
            background: #F2F3F5;
            color: #C9CDD4;
            cursor: not-allowed;
            border-color: #E5E6EB;
        }
        
        .pagination-info {
            color: var(--text-secondary);
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: var(--text-secondary);
        }
        
        .loading-spinner {
            font-size: 20px;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 详情模态框 */
        .backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        
        .backdrop.active {
            opacity: 1;
            pointer-events: auto;
        }
        
        .modal {
            background: var(--card-bg);
            border-radius: 12px;
            width: 95%;
            max-width: 1200px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            transform: scale(0.9);
            transition: var(--transition);
            overflow: hidden;
        }
        
        .backdrop.active .modal {
            transform: scale(1);
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .close-btn {
            font-size: 18px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .close-btn:hover {
            color: var(--text-color);
        }
        
        .modal-content {
            flex: 1;
            overflow-y: auto;
            padding: 18px 20px;
            display: flex;
            gap: 15px;
        }
        
        .modal-footer {
            padding: 12px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        .detail-left, .detail-right {
            flex: 1;
        }
        
        .detail-left {
            min-width: 280px;
        }
        
        .customer-info {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            box-shadow: var(--shadow);
        }
        
        .info-row {
            display: flex;
            gap: 12px;
            margin-bottom: 10px;
        }
        
        .info-item {
            flex: 1;
        }
        
        .info-label {
            font-size: 11px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: 13px;
            font-weight: 500;
            color: var(--text-color);
        }
        
        .history-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 10px;
        }
        
        .transfer-history {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 15px;
            box-shadow: var(--shadow);
        }
        
        .history-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .history-table th, .history-table td {
            padding: 8px 10px;
            border-bottom: 1px solid var(--border-color);
            text-align: left;
            font-size: 12px;
        }
        
        .history-table th {
            background-color: #F2F3F5;
            color: var(--text-secondary);
            font-weight: 600;
        }
        
        .history-table tr:last-child td {
            border-bottom: none;
        }
        
        .history-table tr:hover {
            background: #F9FAFB;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 筛选框区域 -->
            <div class="query-form">
                <div class="form-header">
                    <h2 class="form-title">换单记录查询</h2>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="operator"><i class="fas fa-user-tie"></i> 作业员/组织</label>
                        <select class="form-control" id="operator">
                            <option value="">全部</option>
                            <option value="1">催收一组</option>
                            <option value="2">催收二组</option>
                            <option value="3">催收三组</option>
                            <option value="4">催收四组</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="customerName"><i class="fas fa-user"></i> 客户姓名</label>
                        <input type="text" class="form-control" id="customerName" placeholder="输入姓名">
                    </div>
                    
                    <div class="form-group">
                        <label for="customerId"><i class="fas fa-id-card"></i> 客户索引号</label>
                        <input type="text" class="form-control" id="customerId" placeholder="输入索引号">
                    </div>
                    
                    <div class="form-group">
                        <label for="batchNumber"><i class="fas fa-barcode"></i> 批次号</label>
                        <input type="text" class="form-control" id="batchNumber" placeholder="输入批次号">
                    </div>
                    
                    <div class="form-group">
                        <label for="transferDate"><i class="fas fa-calendar"></i> 换单日期</label>
                        <input type="text" class="form-control" id="transferDate" placeholder="选择日期范围">
                    </div>
                </div>
                
                <!-- 按钮移到筛选字段下方 -->
                <div class="form-actions">
                    <button class="btn btn-secondary">
                        <i class="fas fa-redo"></i> 重置
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-search"></i> 查询
                    </button>
                </div>
            </div>
            
            <!-- 统计信息区域 -->
            <div class="summary-info">
                <div class="summary-item">
                    <div class="summary-value">86</div>
                    <div class="summary-label">筛选户数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value success">32</div>
                    <div class="summary-label">换进</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value danger">54</div>
                    <div class="summary-label">换出</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">¥1,240,000</div>
                    <div class="summary-label">涉及金额</div>
                </div>
            </div>
            
            <!-- 记录展示区域 -->
            <div class="table-container">
                <div class="table-content">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th>客户姓名</th>
                                <th>客户索引号</th>
                                <th>换单日期</th>
                                <th>换单类型</th>
                                <th>作业员/工号</th>
                                <th>组织</th> 
                                <th>批次号</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td class="highlight">张某某</td>
                                <td>CUST20231215001</td>
                                <td>2023-12-15</td>
                                <td><span class="status-tag success"><i class="fas fa-arrow-down"></i> 换进</span></td>
                                <td>张三 CF 1001</td>
                                <td>柏溪/二组</td>
                                <td>20250317</td>
                                <td>
                                    <button class="action-btn view-detail" data-id="CUST20231215001">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td class="highlight">李某某</td>
                                <td>CUST20231215001</td>
                                <td>2023-12-15</td>
                                <td><span class="status-tag success"><i class="fas fa-arrow-down"></i> 换进</span></td>
                                <td>张四 CF 1002</td>
                                <td>柏溪/一组</td>
                                <td>20250317</td>
                                <td>
                                    <button class="action-btn view-detail" data-id="CUST20231215001">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td class="highlight">刘某某</td>
                                <td>CUST20231215001</td>
                                <td>2023-12-15</td>
                                <td><span class="status-tag success"><i class="fas fa-arrow-down"></i> 换进</span></td>
                                <td>王三 CF 1003</td>
                                <td>柏溪/二组</td>
                                <td>20250317</td>
                                <td>
                                    <button class="action-btn view-detail" data-id="CUST20231215001">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td class="highlight">赵某某</td>
                                <td>CUST20231215001</td>
                                <td>2023-12-15</td>
                                <td><span class="status-tag danger"><i class="fas fa-arrow-up"></i> 换出</span></td>
                                <td>李四 CF 1004</td>
                                <td>柏溪/三组</td>
                                <td>20250317</td>
                                <td>
                                    <button class="action-btn view-detail" data-id="CUST20231215001">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                            <tr>
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td class="highlight">钱某某</td>
                                <td>CUST20231215001</td>
                                <td>2023-12-15</td>
                                <td><span class="status-tag danger"><i class="fas fa-arrow-up"></i> 换出</span></td>
                                <td>王四 CF 1005</td>
                                <td>柏溪/四组</td>
                                <td>20250317</td>
                                <td>
                                    <button class="action-btn view-detail" data-id="CUST20231215001">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                            <tr>
                                <td class="checkbox-cell">
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td class="highlight">孙某某</td>
                                <td>CUST20231215001</td>
                                <td>2023-12-15</td>
                                <td><span class="status-tag danger"><i class="fas fa-arrow-up"></i> 换出</span></td>
                                <td>赵四 CF 1006</td>
                                <td>柏溪/一组</td>
                                <td>20250317</td>
                                <td>
                                    <button class="action-btn view-detail" data-id="CUST20231215001">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
                           
               
                <div class="pagination">
                    <div class="pagination-info">共 128 条记录</div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-angle-left"></i>
                        </button>
                        
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">4</button>
                        <button class="pagination-btn">5</button>
                        
                        <button class="pagination-btn">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="pagination-btn">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 详情模态框 -->
    <div class="backdrop" id="detailModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">客户详情</h3>
                <button class="close-btn" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div class="detail-left">
                    <!-- 客户基本信息 -->
                    <div class="customer-info">
                        <h3 style="font-size: 14px; font-weight: 600; margin-bottom: 10px;">客户基本信息</h3>
                        
                        <div class="info-row">
                            <div class="info-item">
                                <div class="info-label">客户姓名</div>
                                <div class="info-value" id="modalName">张某某</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">客户索引号</div>
                                <div class="info-value" id="modalId">CUST20231215001</div>
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-item">
                                <div class="info-label">身份证号</div>
                                <div class="info-value" id="modalIdNumber">330123********1234</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">联系电话</div>
                                <div class="info-value" id="modalPhone">138****5678</div>
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-item">
                                <div class="info-label">欠款金额</div>
                                <div class="info-value" id="modalDebtAmount">¥24,500.00</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">逾期天数</div>
                                <div class="info-value" id="modalOverdueDays">120天</div>
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-item">
                                <div class="info-label">当前状态</div>
                                <div class="info-value" id="modalStatus">
                                    <span class="status-tag danger">
                                        <i class="fas fa-exclamation-circle"></i> 逾期
                                    </span>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">最近还款日期</div>
                                <div class="info-value" id="modalLastPaymentDate">2023-10-15</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 催收记录统计 -->
                    <div class="customer-info">
                        <h3 style="font-size: 14px; font-weight: 600; margin-bottom: 10px;">催收记录统计</h3>
                        
                        <div class="info-row">
                            <div class="info-item">
                                <div class="info-label">总催收次数</div>
                                <div class="info-value" id="modalTotalCollections">8次</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">成功联系次数</div>
                                <div class="info-value" id="modalSuccessfulContacts">5次</div>
                            </div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-item">
                                <div class="info-label">承诺还款次数</div>
                                <div class="info-value" id="modalPromisedPayments">3次</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">实际还款次数</div>
                                <div class="info-value" id="modalActualPayments">1次</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="detail-right">
                    <!-- 换单历史 -->
                    <div class="transfer-history">
                        <h3 class="history-title">换单历史记录</h3>
                        
                        <table class="history-table" id="transferHistoryTable">
                            <thead>
                                <tr>
                                    <th>换单日期</th>
                                    <th>原作业员/组织</th>
                                    <th>新作业员/组织</th>
                                    <th>换单类型</th>
                                    <th>换单原因</th>
                                    <th>操作人</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2023-12-15</td>
                                    <td>待分配</td>
                                    <td>催收一组</td>
                                    <td><span class="status-tag success">换进</span></td>
                                    <td>新客户分配</td>
                                    <td>系统管理员</td>
                                </tr>
                                <tr>
                                    <td>2023-11-20</td>
                                    <td>催收三组</td>
                                    <td>待分配</td>
                                    <td><span class="status-tag danger">换出</span></td>
                                    <td>区域调整</td>
                                    <td>王主管</td>
                                </tr>
                                <tr>
                                    <td>2023-10-05</td>
                                    <td>待分配</td>
                                    <td>催收三组</td>
                                    <td><span class="status-tag success">换进</span></td>
                                    <td>新客户分配</td>
                                    <td>系统管理员</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 最近催收记录 -->
                    <div class="transfer-history" style="margin-top: 12px;">
                        <h3 class="history-title">最近催收记录</h3>
                        
                        <table class="history-table" id="collectionHistoryTable">
                            <thead>
                                <tr>
                                    <th>催收日期</th>
                                    <th>催收方式</th>
                                    <th>催收结果</th>
                                    <th>催收内容摘要</th>
                                    <th>下一步计划</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2023-12-14</td>
                                    <td>电话催收</td>
                                    <td>承诺还款</td>
                                    <td>客户表示下周内还款5000元</td>
                                    <td>下周三跟进</td>
                                </tr>
                                <tr>
                                    <td>2023-12-05</td>
                                    <td>电话催收</td>
                                    <td>未联系到</td>
                                    <td>多次拨打客户电话无人接听</td>
                                    <td>次日再次尝试联系</td>
                                </tr>
                                <tr>
                                    <td>2023-11-28</td>
                                    <td>上门催收</td>
                                    <td>部分还款</td>
                                    <td>客户偿还了1000元，并承诺月底前再还3000元</td>
                                    <td>月底前跟进</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="closeModalBtn">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const customerData = {
            "CUST20231215001": {
                name: "张某某",
                id: "CUST20231215001",
                idNumber: "330123********1234",
                phone: "138****5678",
                debtAmount: "¥24,500.00",
                overdueDays: "120天",
                status: "逾期",
                lastPaymentDate: "2023-10-15",
                totalCollections: "8次",
                successfulContacts: "5次",
                promisedPayments: "3次",
                actualPayments: "1次",
                transferHistory: [
                    { date: "2023-12-15", from: "待分配", to: "催收一组", type: "换进", reason: "新客户分配", operator: "系统管理员" },
                    { date: "2023-11-20", from: "催收三组", to: "待分配", type: "换出", reason: "区域调整", operator: "王主管" },
                    { date: "2023-10-05", from: "待分配", to: "催收三组", type: "换进", reason: "新客户分配", operator: "系统管理员" }
                ],
                collectionHistory: [
                    { date: "2023-12-14", method: "电话催收", result: "承诺还款", summary: "客户表示下周内还款5000元", nextStep: "下周三跟进" },
                    { date: "2023-12-05", method: "电话催收", result: "未联系到", summary: "多次拨打客户电话无人接听", nextStep: "次日再次尝试联系" },
                    { date: "2023-11-28", method: "上门催收", result: "部分还款", summary: "客户偿还了1000元，并承诺月底前再还3000元", nextStep: "月底前跟进" }
                ]
            },
            "CUST20231215002": {
                name: "李某某",
                id: "CUST20231215002",
                idNumber: "310104********5678",
                phone: "139****1234",
                debtAmount: "¥18,200.00",
                overdueDays: "90天",
                status: "逾期",
                lastPaymentDate: "2023-11-01",
                totalCollections: "6次",
                successfulContacts: "4次",
                promisedPayments: "2次",
                actualPayments: "0次",
                transferHistory: [
                    { date: "2023-12-14", from: "待分配", to: "催收二组", type: "换进", reason: "新客户分配", operator: "系统管理员" },
                    { date: "2023-10-15", from: "催收四组", to: "待分配", type: "换出", reason: "催收效果不佳", operator: "赵主管" },
                    { date: "2023-09-20", from: "待分配", to: "催收四组", type: "换进", reason: "新客户分配", operator: "系统管理员" }
                ],
                collectionHistory: [
                    { date: "2023-12-10", method: "电话催收", result: "承诺还款", summary: "客户表示本月底前还款8000元", nextStep: "月底前跟进" },
                    { date: "2023-11-25", method: "电话催收", result: "联系到本人", summary: "客户称经济困难，需要延期还款", nextStep: "两周后再次联系" },
                    { date: "2023-11-10", method: "电话催收", result: "联系到家人", summary: "家人表示会转告客户尽快还款", nextStep: "三日后再次联系" }
                ]
            }
        };

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化日期范围选择器
            flatpickr("#transferDate", {
                mode: "range",
                dateFormat: "Y-m-d",
                defaultDate: "today",
                locale: "zh",
                placeholder: "选择日期范围"
            });
            
            // 表单重置功能
            document.querySelector('.btn-secondary:first-child').addEventListener('click', function() {
                // 重置输入框
                document.querySelectorAll('input[type="text"]').forEach(input => {
                    input.value = '';
                });
                
                // 重置下拉框
                document.querySelectorAll('select').forEach(select => {
                    select.selectedIndex = 0;
                });
                
                // 重置日期选择器
                flatpickr("#transferDate").clear();
            });
            
            // 查询按钮功能
            document.querySelector('.btn-primary').addEventListener('click', function() {
                // 显示加载状态
                const tableContent = document.querySelector('.table-content');
                const originalContent = tableContent.innerHTML;
                
                tableContent.innerHTML = `
                    <div class="loading">
                        <span class="loading-spinner"><i class="fas fa-spinner"></i></span>
                        <span>正在查询数据，请稍候...</span>
                    </div>
                `;
                
                // 模拟网络请求延迟
                setTimeout(() => {
                    tableContent.innerHTML = originalContent;
                    
                    // 更新汇总信息
                    document.querySelectorAll('.summary-value')[0].textContent = '86';
                    document.querySelectorAll('.summary-value')[1].textContent = '32';
                    document.querySelectorAll('.summary-value')[2].textContent = '54';
                    document.querySelectorAll('.summary-value')[3].textContent = '¥1,240,000';
                    
                    // 添加行点击事件
                    addTableRowEvents();
                }, 1200);
            });
            
            // 全选/取消全选功能
            document.getElementById('selectAll').addEventListener('change', function() {
                const isChecked = this.checked;
                document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                    checkbox.checked = isChecked;
                    
                    const row = checkbox.closest('tr');
                    if (isChecked) {
                        row.classList.add('selected');
                    } else {
                        row.classList.remove('selected');
                    }
                });
            });
            
            // 添加表格行点击事件
            function addTableRowEvents() {
                document.querySelectorAll('.data-table tbody tr').forEach(row => {
                    row.addEventListener('click', function(e) {
                        // 跳过复选框和按钮的点击
                        if (e.target.matches('input[type="checkbox"]') || e.target.closest('button')) {
                            return;
                        }
                        
                        // 取消所有行的选中状态
                        document.querySelectorAll('.data-table tbody tr').forEach(r => {
                            r.classList.remove('selected');
                        });
                        
                        // 设置当前行选中状态
                        this.classList.add('selected');
                        
                        // 选中对应的复选框
                        const checkbox = this.querySelector('.row-checkbox');
                        checkbox.checked = true;
                    });
                });
                
                // 添加复选框点击事件
                document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const row = this.closest('tr');
                        if (this.checked) {
                            row.classList.add('selected');
                        } else {
                            row.classList.remove('selected');
                        }
                    });
                });
                
                // 添加查看详情按钮事件
                document.querySelectorAll('.view-detail').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const customerId = this.getAttribute('data-id');
                        showCustomerDetail(customerId);
                    });
                });
            }
            
            // 初始绑定事件
            addTableRowEvents();
            
            // 关闭模态框事件
            document.getElementById('closeModal').addEventListener('click', closeDetailModal);
            document.getElementById('closeModalBtn').addEventListener('click', closeDetailModal);
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });
            
            // 返回按钮事件（备用，实际功能由模态框关闭实现）
            document.getElementById('backBtn').addEventListener('click', function() {
                closeDetailModal();
            });
            
            // 显示客户详情模态框
            function showCustomerDetail(customerId) {
                // 获取客户数据
                const customer = customerData[customerId] || customerData["CUST20231215001"];
                
                // 更新模态框内容
                document.getElementById('modalTitle').textContent = `客户详情 - ${customer.name}`;
                document.getElementById('modalName').textContent = customer.name;
                document.getElementById('modalId').textContent = customer.id;
                document.getElementById('modalIdNumber').textContent = customer.idNumber;
                document.getElementById('modalPhone').textContent = customer.phone;
                document.getElementById('modalDebtAmount').textContent = customer.debtAmount;
                document.getElementById('modalOverdueDays').textContent = customer.overdueDays;
                
               // 设置状态标签
                const statusElement = document.getElementById('modalStatus');
                statusElement.innerHTML = customer.status === "逾期" ? 
                    '<span class="status-tag danger"><i class="fas fa-exclamation-circle"></i> 逾期</span>' : 
                    '<span class="status-tag success"><i class="fas fa-check-circle"></i> 正常</span>';
                
                document.getElementById('modalLastPaymentDate').textContent = customer.lastPaymentDate;
                document.getElementById('modalTotalCollections').textContent = customer.totalCollections;
                document.getElementById('modalSuccessfulContacts').textContent = customer.successfulContacts;
                document.getElementById('modalPromisedPayments').textContent = customer.promisedPayments;
                document.getElementById('modalActualPayments').textContent = customer.actualPayments;
                
                // 更新换单历史表格
                const transferTableBody = document.getElementById('transferHistoryTable').querySelector('tbody');
                transferTableBody.innerHTML = '';
                
                customer.transferHistory.forEach(transfer => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${transfer.date}</td>
                        <td>${transfer.from}</td>
                        <td>${transfer.to}</td>
                        <td><span class="status-tag ${transfer.type === '换进' ? 'success' : 'danger'}">${transfer.type}</span></td>
                        <td>${transfer.reason}</td>
                        <td>${transfer.operator}</td>
                    `;
                    transferTableBody.appendChild(row);
                });
                
                // 更新催收历史表格
                const collectionTableBody = document.getElementById('collectionHistoryTable').querySelector('tbody');
                collectionTableBody.innerHTML = '';
                
                customer.collectionHistory.forEach(collection => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${collection.date}</td>
                        <td>${collection.method}</td>
                        <td>${collection.result}</td>
                        <td>${collection.summary}</td>
                        <td>${collection.nextStep}</td>
                    `;
                    collectionTableBody.appendChild(row);
                });
                
                // 显示模态框
                document.getElementById('detailModal').classList.add('active');
            }
            
            // 关闭详情模态框
            function closeDetailModal() {
                document.getElementById('detailModal').classList.remove('active');
            }
        });
    </script>
</body>
</html>    