<!DOCTYPE html>
<html lang="zh-CN" style="height: 100%;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> 案池页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #2c3e50);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
            height: 100%;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 40px);
            background: #fff;
        }
        
        /* 上框体：筛选框 - 占视口高度35% */
        .filter-box {
            height: 35vh;
            background: #fff;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            border-bottom: 2px solid #f0f0f0;
            overflow-y: auto;
        }
        
        .filter-title {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #2c3e50;
            font-size: 1.1rem;
            font-weight: 600;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
        }
        
        .filter-title i {
            color: #3498db;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(5, minmax(0, 1fr));
            gap: 15px;
            flex: 1;
        }
        
        /* 筛选组样式 */
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 2px;
        }
        
        /* 字段标签样式 */
        .filter-label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.75rem;
            min-width: 70px;
        }
        
        /* 输入控件样式 */
        .filter-control {
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.7rem;
            flex: 1;
        }
        
        .filter-control:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .filter-btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .filter-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 0.8rem;
        }
        
        .filter-btn.search {
            background-color: #3498db;
            color: white;
        }
        
        .filter-btn.reset {
            background-color: #e1e4e8;
            color: #2c3e50;
        }
        
        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        /* 中框体：功能框 - 占视口高度5% */
        .function-box {
            height: 5vh;
            background: linear-gradient(145deg, #2c3e50, #1a2a6c);
            display: flex;
            align-items: center;
            padding: 0 15px;
            position: relative;
        }
        
        .stats-info {
            position: absolute;
            left: 2px;
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
            font-size: 0.8rem;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .function-buttons {
            position: absolute;
            right: 2px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .function-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8rem;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .function-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        
        .function-btn.primary {
            background-color: #3498db;
        }
        
        /* 下框体：数据展示框 - 占视口高度60% */
        .data-box {
            height: 60vh;
            background: #fff;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .data-header {
            padding: 10px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e4e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .data-title {
            font-size: 1.0rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .data-title i {
            color: #3498db;
        }
        
        .data-actions {
            display: flex;
            gap: 8px;
        }
        
        .data-action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            color: #2c3e50;
            background: #e1e4e8;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .data-action-btn:hover {
            background: #d1d5db;
        }
        
        /* 改进的表格容器 */
        .data-container {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            background-color: #f9f9f9;
        }
        
        .table-wrapper {
            flex: 1;
            overflow: auto;
            position: relative;
        }
        
        table {
            border-collapse: collapse;
            min-width: 100%;
            width: max-content;
        }
        
        thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background: #f1f3f5;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 8px 10px;
            text-align: center;
            font-size: 0.7rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #2c3e50;
            font-weight: 600;
            min-width: 100px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        thead th:hover {
            background-color: #e1e4e8;
        }
        
        th:nth-child(1),
        td:nth-child(1) {
            position: sticky;
            left: 0;
            z-index: 5;
            width: 30px;
            min-width: 30px;
            background: #f8f8f8;
        }
        
        th:nth-child(2),
        td:nth-child(2) {
            position: sticky;
            left: 30px;
            z-index: 5;
            width: 100px;
            min-width: 100px;
            background: #f8f8f8;
        }
        
        th:nth-child(3),
        td:nth-child(3) {
            position: sticky;
            left: 130px;
            z-index: 5;
            width: 180px;
            min-width: 180px;
            background: #f8f8f8;
        }
        
        th:nth-child(1),
        th:nth-child(2),
        th:nth-child(3) {
            z-index: 15;
        }
        
        td {
            padding: 8px 10px;
            border-bottom: 1px solid #e1e4e8;
            background: white;
            white-space: nowrap;
            position: relative;
            font-size: 0.7rem;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #333;
        }
        
        th:nth-child(1),
        td:nth-child(1) {
            border-right: none;
            box-shadow: 30px 0 0 #f8f8f8,
                        inset -1px 0 0 #e1e4e8;
        }
        
        th:nth-child(2),
        td:nth-child(2) {
            border-left: none;
            border-right: none;
            box-shadow: 100px 0 0 #f8f8f8,
                        inset -1px 0 0 #e1e4e8;
        }
        
        th:nth-child(3),
        td:nth-child(3) {
            border-left: none;
            box-shadow: 180px 0 0 #f8f8f8,
                        inset -1px 0 0 #e1e4e8,
                        2px 0 5px rgba(0,0,0,0.1);
        }
        
        .data-table tr:hover td {
            background: #f0f7ff;
        }
        
        .data-table tr:nth-child(even) td {
            background: #f8fafc;
        }
        
        /* 排序图标样式 */
        .sort-icon {
            margin-left: 5px;
            color: #3498db;
            font-size: 0.7rem;
        }
        
        .status-tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-align: center;
            min-width: 70px;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-processing {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-completed {
            background: #bbf7d0;
            color: #047857;
        }
        
        .status-overdue {
            background: #fee2e2;
            color: #b91c1c;
        }
        
        .status-locked {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .status-unlocked {
            background: #dcfce7;
            color: #166534;
        }
        
        .amount {
            font-weight: 500;
            color: #2c3e50;
            text-align: center;
        }
        
        /* 空值样式 */
        .empty-value {
            color: #999;
            font-style: italic;
        }
        
        /* 改进的分页控件样式 */
        .pagination {
            padding: 10px 15px;
            background: #f8f9fa;
            border-top: 1px solid #e1e4e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
        }
        
        .pagination-left, .pagination-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .pagination-select, .pagination-input {
            padding: 5px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.8rem;
            margin: 0 4px;
        }
        
        .pagination-input {
            width: 40px;
            text-align: center;
        }
        
        .pagination-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            background: #e1e4e8;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.8rem;
        }
        
        .pagination-btn:hover:not(:disabled) {
            background: #d1d5db;
        }
        
        .pagination-btn:disabled {
            background: #e1e4e8;
            color: #adb5bd;
            cursor: not-allowed;
        }
        
        .pagination-btn.active {
            background: #3498db;
            color: white;
        }
        
        .page-info {
            color: #6c757d;
        }
        
        /* 加载指示器 */
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-box {
                height: auto;
                min-height: 35vh;
                max-height: 50vh;
                overflow-y: auto;
            }
            
            .function-box {
                height: auto;
                padding: 10px;
                flex-wrap: wrap;
                justify-content: center;
                position: relative;
            }
            
            .stats-info, .function-buttons {
                position: static;
                width: 100%;
                justify-content: center;
            }
            
            .data-box {
                height: auto;
                min-height: 60vh;
            }
            
            .data-table th, 
            .data-table td {
                padding: 8px 10px;
                font-size: 0.75rem;
            }
            
            .pagination {
                flex-direction: column;
                gap: 8px;
            }
            
            .pagination-left, .pagination-right {
                width: 100%;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            /* 移动端调整：标签和控件恢复为上下排列 */
            .filter-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .filter-label {
                min-width: auto;
            }
        }
        
        @media (max-width: 480px) {
            .filter-box, .data-box {
                padding: 10px;
            }
            
            .filter-btn-group {
                flex-direction: column;
                gap: 8px;
            }
            
            .filter-btn {
                width: 100%;
            }
            
            .data-title {
                font-size: 0.9rem;
            }
            
            .data-table {
                font-size: 0.7rem;
            }
            
            .pagination-btn {
                padding: 4px 7px;
            }
            
            .stats-info, .function-buttons {
                flex-direction: column;
                gap: 8px;
            }
        }
        
        .client-id-link {
            color: #3498db;
            text-decoration: none;
            cursor: pointer;
            font-weight: 500;
        }
        
        .client-id-link:hover {
            text-decoration: underline;
        }
        
        .sort-asc::after {
            content: "▲";
            font-size: 0.7em;
            margin-left: 5px;
            color: #3498db;
        }
        
        .sort-desc::after {
            content: "▼";
            font-size: 0.7em;
            margin-left: 5px;
            color: #3498db;
        }
        
        /* 滚动条样式 */
        .table-wrapper::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        
        .table-wrapper::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 上框体：筛选框 - 占视口高度35% -->
        <div class="filter-box">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                <span>案件筛选条件</span>
            </div>
            <div class="filter-grid">
                <!-- 第1排 -->
                <div class="filter-group">
                    <label class="filter-label">客户姓名</label>
                    <input type="text" class="filter-control" id="clientName" placeholder="请输入关键词">
                </div>
                <div class="filter-group">
                    <label class="filter-label">客户索引号</label>
                    <input type="text" class="filter-control" id="clientId" placeholder="唯一标识需手动输入，粘贴去除前后空格">
                </div>
                <div class="filter-group">
                    <label class="filter-label">分案城市</label>
                    <input type="text" class="filter-control" id="city" placeholder="输入城市名称">
                </div>
                <div class="filter-group">
                    <label class="filter-label">当前逾期时段</label>
                    <input type="text" class="filter-control" id="overduePeriod" placeholder="输入逾期时段">
                </div>
                <div class="filter-group">
                    <label class="filter-label">入案池次数</label>
                    <input type="text" class="filter-control" id="poolCount" placeholder="输入次数">
                </div>
                
                <!-- 第2排 -->
                <div class="filter-group">
                    <label class="filter-label">批次号</label>
                    <input type="text" class="filter-control" id="batchNumber" placeholder="输入批次号">
                </div>
                <div class="filter-group">
                    <label class="filter-label">持卡人代码</label>
                    <input type="text" class="filter-control" id="cardholderCode" placeholder="输入持卡人代码">
                </div>
                <div class="filter-group">
                    <label class="filter-label">专项类型</label>
                    <input type="text" class="filter-control" id="specialType" placeholder="输入专项类型">
                </div>
                <div class="filter-group">
                    <label class="filter-label">委托时金额段</label>
                    <input type="text" class="filter-control" id="amountRange" placeholder="例: 0万-5万">
                </div>
                <div class="filter-group">
                    <label class="filter-label">未跟进天数</label>
                    <input type="text" class="filter-control" id="unfollowedDays" placeholder="输入天数范围">
                </div>
                
                <!-- 第3排 -->
                <div class="filter-group">
                    <label class="filter-label">委托机构</label>
                    <input type="text" class="filter-control" id="institution" placeholder="输入委托机构名称">
                </div>
                <div class="filter-group">
                    <label class="filter-label">委托开始日</label>
                    <input type="text" class="filter-control flatpickr-input" id="startDateRange" placeholder="选择日期范围">
                </div>
                <div class="filter-group">
                    <label class="filter-label">目标时段</label>
                    <input type="text" class="filter-control" id="targetPeriod" placeholder="输入目标时段">
                </div>
                <div class="filter-group">
                    <label class="filter-label">委托时月龄分档</label>
                    <input type="text" class="filter-control" id="monthRange" placeholder="输入月份区间">
                </div>
                <div class="filter-group">
                    <label class="filter-label">保留次数</label>
                    <input type="text" class="filter-control" id="retentionCount" placeholder="输入保留次数">
                </div>
                
                <!-- 第4排 -->
                <div class="filter-group">
                    <label class="filter-label">案件类型</label>
                    <input type="text" class="filter-control" id="caseType" placeholder="输入案件类型">
                </div>
                <div class="filter-group">
                    <label class="filter-label">委托结束日</label>
                    <input type="text" class="filter-control flatpickr-input" id="endDateRange" placeholder="选择日期范围">
                </div>
                <div class="filter-group">
                    <label class="filter-label">诉讼标签</label>
                    <input type="text" class="filter-control" id="litigationTag" placeholder="输入诉讼状态">
                </div>
                <div class="filter-group">
                    <label class="filter-label">委托时佣金分档</label>
                    <input type="text" class="filter-control" id="commissionRange" placeholder="输入佣金等级">
                </div>
                <div class="filter-group">
                    <label class="filter-label">评分档</label>
                    <input type="text" class="filter-control" id="rating" placeholder="输入评分等级">
                </div>
                
                <!-- 第5排 -->
                <div class="filter-group">
                    <label class="filter-label">失联修复结果</label>
                    <select class="filter-control" id="repairResult">
                        <option value="">全部</option>
                        <option value="success">成功</option>
                        <option value="failed">失败</option>
                        <option value="not-applied">未申请</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">配置状态</label>
                    <select class="filter-control" id="configStatus">
                        <option value="">全部状态</option>
                        <option value="locked">已锁定</option>
                        <option value="unlocked">未锁定</option>
                    </select>
                </div>
                
                <!-- 按钮组移动到第五排第五列的位置 -->
                <div class="filter-group" style="grid-column: 5; margin-top: 5px;">
                    <div class="filter-btn-group">
                        <button class="filter-btn search" id="searchBtn">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                        <button class="filter-btn reset" id="resetBtn">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 中框体：功能框 - 占视口高度5% -->
        <div class="function-box">
            <div class="stats-info">
                <div class="stat-item">
                    <span>总户数：</span>
                    <span id="total-accounts">0</span>
                </div>
                <div class="stat-item">
                    <span>总委托金额：</span>
                    <span id="total-amount">¥0.00</span>
                </div>
                <div class="stat-item">
                    <span>勾选户数：</span>
                    <span id="selected-accounts">0</span>
                </div>
                <div class="stat-item">
                    <span>勾选金额：</span>
                    <span id="selected-amount">¥0.00</span>
                </div>
            </div>
            <div class="function-buttons">
                <button class="function-btn" id="lockBtn">
                    <i class="fas fa-lock"></i>
                    锁定
                </button>
                 <button class="function-btn" id="unlockBtn">
                    <i class="fas fa-lock-open"></i>
                    解除
                </button>
                 <button class="function-btn" id="assignBtn">
                    <i class="fas fa-user-graduate"></i>
                    任务分派
                </button>
                <button class="function-btn" id="importBtn">
                    <i class="fas fa-upload"></i>
                    导入
                </button>
                <button class="function-btn" id="exportBtn">
                    <i class="fas fa-download"></i>
                    导出
                </button>
            </div>
        </div>
        
        <!-- 下框体：数据展示框 - 占视口高度60% -->
        <div class="data-box">
            <div class="data-header">
                <div class="data-title">
                    <i class="fas fa-table"></i>
                    <span>案件数据列表</span>
                </div>
                <!-- <div class="data-actions">
                    <button class="data-action-btn">
                        <i class="fas fa-sort"></i>
                        排序
                    </button>
                    <button class="data-action-btn">
                        <i class="fas fa-columns"></i>
                        视图
                    </button>
                    <button class="data-action-btn">
                        <i class="fas fa-sliders-h"></i>
                        更多
                    </button>
                </div> -->
            </div>
            <div class="data-container">
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><div class="check-cell"><input type="checkbox" id="select-all"></div></th>
                                <th data-column="clientName" class="sortable">客户姓名</th>
                                <th data-column="clientId" class="sortable">客户索引号</th>
                                <th data-column="cardholderCode" class="sortable">持卡人代码</th>
                                <th data-column="entrustedAmount" class="sortable">委托金额</th>
                                <th data-column="entrustedPrincipal" class="sortable">委托本金</th>
                                <th data-column="batchNumber" class="sortable">批次号</th>
                                <th data-column="poolCount" class="sortable">入案池次数</th>
                                <th data-column="unfollowedDays" class="sortable">未跟进天数</th>
                                <th data-column="configStatus" class="sortable">配置状态</th>
                                <th data-column="balanceOPS" class="sortable">余额 OPS</th>
                                <th data-column="principalOPS" class="sortable">本金 OPS</th>
                                <th data-column="caseType" class="sortable">案件类型</th>
                                <th data-column="specialType" class="sortable">专项类型</th>
                                <th data-column="repairResult" class="sortable">失联修复结果</th>
                                <th data-column="entrustStartDate" class="sortable">委托开始日</th>
                                <th data-column="entrustEndDate" class="sortable">委托结束日</th>
                                <th data-column="retentionCount" class="sortable">保留次数</th>
                                <th data-column="cardOpeningDate" class="sortable">开卡日期</th>
                                <th data-column="creditLimit" class="sortable">信用额度</th>
                                <th data-column="lastPaymentDate" class="sortable">最后缴款日期</th>
                                <th data-column="overduePeriodAtEntrust" class="sortable">委托时逾期时段</th>
                                <th data-column="targetPeriod" class="sortable">目标时段</th>
                                <th data-column="city" class="sortable">分案城市</th>
                                <th data-column="lastFollowupDate" class="sortable">最后跟进日期</th>
                                <th data-column="caseFlag" class="sortable">新旧案标志</th>
                                <th data-column="currentOverduePeriod" class="sortable">当前逾期时段</th>
                                <th data-column="residenceCity" class="sortable">户籍城市</th>
                                <th data-column="clientAge" class="sortable">客户年龄</th>
                                <th data-column="occupation" class="sortable">职业</th>
                                <th data-column="accountLast7" class="sortable">账户号后 7 位</th>
                                <th data-column="monthRangeAtEntrust" class="sortable">委托时月龄分档</th>
                                <th data-column="amountRangeAtEntrust" class="sortable">委托时金额段</th>
                                <th data-column="commissionRangeAtEntrust" class="sortable">委托时佣金分档</th>
                                <th data-column="rating" class="sortable">评分档</th>
                                <th data-column="isLitigation" class="sortable">是否诉讼 (含风险代理)</th>
                                <th data-column="currentMonthPayment" class="sortable">客户当月主动还款金额</th>
                                <th data-column="previousDayPayment" class="sortable">客户前日主动还款金额</th>
                                <th data-column="installmentStatus" class="sortable">个性化分期状态</th>
                                <th data-column="installmentPerformance" class="sortable">个性化分期履约状态</th>
                                <th data-column="complaintTag" class="sortable">投诉标签</th>
                                <th data-column="installmentPerformanceTag" class="sortable">个分履约标签</th>
                                <th data-column="litigationTag" class="sortable">诉讼标签</th>
                                <th data-column="voiceTag" class="sortable">智能语音标签</th>
                                <th data-column="specialTag" class="sortable">专项标签</th>
                                <th data-column="isLitigationCommission" class="sortable">是否诉讼 (结佣)</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <!-- 数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <div class="pagination-left">
                        <div>
                            <label for="pageSizeSelect">每页显示:</label>
                            <select id="pageSizeSelect" class="pagination-select">
                                <option value="10">10条</option>
                                <option value="20" selected>20条</option>
                                <option value="50">50条</option>
                                <option value="100">100条</option>
                            </select>
                        </div>
                        <div class="page-info">显示 <span id="start-record">0</span>-<span id="end-record">0</span> 条，共 <span id="total-records">0</span> 条</div>
                    </div>
                    <div class="pagination-right">
                        <button class="pagination-btn" id="firstPageBtn" disabled>
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="pagination-btn" id="prevPageBtn" disabled>
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <button class="pagination-btn active" id="page1">1</button>
                        <button class="pagination-btn" id="page2">2</button>
                        <button class="pagination-btn" id="page3">3</button>
                        <button class="pagination-btn" id="nextPageBtn">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="pagination-btn" id="lastPageBtn">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                        <div class="pagination-goto">
                            到第 <input type="number" id="pageNumInput" min="1" value="1" class="pagination-input"> 页
                            <button class="pagination-btn" id="gotoPageBtn">确定</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        // 当前页码
        let currentPage = 1;
        // 每页显示数量
        let pageSize = 20;
        // 总记录数
        let totalRecords = 0;
        // 案件数据
        let caseData = [];
        // 完整数据备份
        let fullData = [];
        // 排序配置
        let sortConfig = {
            column: null,
            direction: 'asc'
        };

        // 生成模拟数据
        function generateMockData(count) {
            const mockData = [];
            const cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉', '西安', '天津'];
            const occupations = ['工程师', '教师', '医生', '销售', '经理', '设计师', '公务员', '自由职业', '学生', '退休'];
            const caseTypes = ['信用卡逾期', '消费贷款', '房贷逾期', '其他类型'];
            const specialTypes = ['常规', '专项'];
            const repairResults = ['成功', '失败', '未申请'];
            const configStatuses = ['已锁定', '未锁定'];
            const litigationTags = ['无诉讼', '诉讼中'];
            const installmentStatuses = ['正常', '逾期', '未申请'];
            const tags = ['是', '否', '无'];
            
            for (let i = 1; i <= count; i++) {
                const city = cities[Math.floor(Math.random() * cities.length)];
                const age = Math.floor(Math.random() * 40) + 18;
                
                mockData.push({
                    clientName: `客户${i}`,
                    clientId: `ID${1000 + i}`,
                    cardholderCode: `CM${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
                    entrustedAmount: Math.floor(Math.random() * 20000) + 5000,
                    entrustedPrincipal: Math.floor(Math.random() * 18000) + 4000,
                    batchNumber: `B2023${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
                    poolCount: Math.floor(Math.random() * 5) + 1,
                    unfollowedDays: Math.floor(Math.random() * 10),
                    configStatus: configStatuses[Math.floor(Math.random() * configStatuses.length)],
                    balanceOPS: (Math.random() * 10000).toFixed(2),
                    principalOPS: (Math.random() * 9000).toFixed(2),
                    caseType: caseTypes[Math.floor(Math.random() * caseTypes.length)],
                    specialType: specialTypes[Math.floor(Math.random() * specialTypes.length)],
                    repairResult: repairResults[Math.floor(Math.random() * repairResults.length)],
                    entrustStartDate: `2023-${Math.floor(Math.random() * 12 + 1).toString().padStart(2, '0')}-${Math.floor(Math.random() * 28 + 1).toString().padStart(2, '0')}`,
                    entrustEndDate: `2024-${Math.floor(Math.random() * 12 + 1).toString().padStart(2, '0')}-${Math.floor(Math.random() * 28 + 1).toString().padStart(2, '0')}`,
                    retentionCount: Math.floor(Math.random() * 3),
                    cardOpeningDate: `2020-${Math.floor(Math.random() * 12 + 1).toString().padStart(2, '0')}-${Math.floor(Math.random() * 28 + 1).toString().padStart(2, '0')}`,
                    creditLimit: Math.floor(Math.random() * 50000) + 10000,
                    lastPaymentDate: `2023-${Math.floor(Math.random() * 12 + 1).toString().padStart(2, '0')}-${Math.floor(Math.random() * 28 + 1).toString().padStart(2, '0')}`,
                    overduePeriodAtEntrust: `${Math.floor(Math.random() * 12) + 1}期`,
                    targetPeriod: `${Math.floor(Math.random() * 6) + 1}期`,
                    city: city,
                    lastFollowupDate: `2023-${Math.floor(Math.random() * 12 + 1).toString().padStart(2, '0')}-${Math.floor(Math.random() * 28 + 1).toString().padStart(2, '0')}`,
                    caseFlag: i % 3 === 0 ? '新案' : '旧案',
                    currentOverduePeriod: `${Math.floor(Math.random() * 12) + 1}期`,
                    residenceCity: city,
                    clientAge: age,
                    occupation: occupations[Math.floor(Math.random() * occupations.length)],
                    accountLast7: Math.floor(Math.random() * ********).toString().padStart(7, '0'),
                    monthRangeAtEntrust: `${Math.floor(Math.random() * 12) + 1}-${Math.floor(Math.random() * 12) + 13}月`,
                    amountRangeAtEntrust: `${Math.floor(Math.random() * 5)}万-${Math.floor(Math.random() * 5) + 5}万`,
                    commissionRangeAtEntrust: `${Math.floor(Math.random() * 5) + 1}档`,
                    rating: `${Math.floor(Math.random() * 5) + 1}星`,
                    isLitigation: tags[Math.floor(Math.random() * tags.length)],
                    currentMonthPayment: Math.floor(Math.random() * 5000),
                    previousDayPayment: Math.floor(Math.random() * 2000),
                    installmentStatus: installmentStatuses[Math.floor(Math.random() * installmentStatuses.length)],
                    installmentPerformance: installmentStatuses[Math.floor(Math.random() * installmentStatuses.length)],
                    complaintTag: tags[Math.floor(Math.random() * tags.length)],
                    installmentPerformanceTag: tags[Math.floor(Math.random() * tags.length)],
                    litigationTag: litigationTags[Math.floor(Math.random() * litigationTags.length)],
                    voiceTag: tags[Math.floor(Math.random() * tags.length)],
                    specialTag: tags[Math.floor(Math.random() * tags.length)],
                    isLitigationCommission: tags[Math.floor(Math.random() * tags.length)]
                });
            }
            
            return mockData;
        }

        // 渲染表格数据
        function renderTable() {
            const tableBody = document.getElementById('table-body');
            const start = (currentPage - 1) * pageSize;
            const end = Math.min(start + pageSize, totalRecords);
            const pageData = caseData.slice(start, end);
            
            let html = '';
            
            if (pageData.length === 0) {
                html = `<tr><td colspan="44" style="text-align: center; padding: 20px;">没有数据可显示</td></tr>`;
            } else {
                pageData.forEach(item => {
                    html += `<tr>
                        <td><div class="check-cell"><input type="checkbox" class="row-checkbox" data-id="${item.clientId}"></div></td>
                        <td>${item.clientName || '<span class="empty-value">空值</span>'}</td>
                        <td><a href="javascript:void(0);" class="client-id-link" data-id="${item.clientId}">${item.clientId || '<span class="empty-value">空值</span>'}</a></td>
                        <td>${item.cardholderCode || '<span class="empty-value">空值</span>'}</td>
                        <td class="amount">${item.entrustedAmount ? '¥' + item.entrustedAmount.toFixed(2) : '<span class="empty-value">空值</span>'}</td>
                        <td class="amount">${item.entrustedPrincipal ? '¥' + item.entrustedPrincipal.toFixed(2) : '<span class="empty-value">空值</span>'}</td>
                        <td>${item.batchNumber || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.poolCount || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.unfollowedDays || '<span class="empty-value">空值</span>'}</td>
                        <td><span class="status-tag ${item.configStatus === '已锁定' ? 'status-locked' : 'status-unlocked'}">${item.configStatus || '<span class="empty-value">空值</span>'}</span></td>
                        <td>${item.balanceOPS || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.principalOPS || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.caseType || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.specialType || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.repairResult || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.entrustStartDate || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.entrustEndDate || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.retentionCount || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.cardOpeningDate || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.creditLimit ? '¥' + item.creditLimit.toLocaleString() : '<span class="empty-value">空值</span>'}</td>
                        <td>${item.lastPaymentDate || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.overduePeriodAtEntrust || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.targetPeriod || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.city || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.lastFollowupDate || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.caseFlag || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.currentOverduePeriod || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.residenceCity || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.clientAge || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.occupation || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.accountLast7 || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.monthRangeAtEntrust || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.amountRangeAtEntrust || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.commissionRangeAtEntrust || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.rating || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.isLitigation || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.currentMonthPayment ? '¥' + item.currentMonthPayment.toLocaleString() : '<span class="empty-value">空值</span>'}</td>
                        <td>${item.previousDayPayment ? '¥' + item.previousDayPayment.toLocaleString() : '<span class="empty-value">空值</span>'}</td>
                        <td>${item.installmentStatus || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.installmentPerformance || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.complaintTag || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.installmentPerformanceTag || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.litigationTag || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.voiceTag || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.specialTag || '<span class="empty-value">空值</span>'}</td>
                        <td>${item.isLitigationCommission || '<span class="empty-value">空值</span>'}</td>
                    </tr>`;
                });
            }
            
            tableBody.innerHTML = html;
            
            // 更新统计信息
            updateStats();
            // 更新分页信息
            updatePaginationInfo();
            // 更新排序图标
            updateSortIcons();
        }
        
        // 更新排序图标
        function updateSortIcons() {
            const headers = document.querySelectorAll('.data-table th.sortable');
            headers.forEach(th => {
                const column = th.getAttribute('data-column');
                th.classList.remove('sort-asc', 'sort-desc');
                
                if (sortConfig.column === column) {
                    if (sortConfig.direction === 'asc') {
                        th.classList.add('sort-asc');
                    } else {
                        th.classList.add('sort-desc');
                    }
                }
            });
        }
        
        // 排序数据
        function sortData(column) {
            if (sortConfig.column === column) {
                // 切换排序方向
                sortConfig.direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
            } else {
                // 设置新的排序列和默认方向
                sortConfig.column = column;
                sortConfig.direction = 'asc';
            }
            
            caseData.sort((a, b) => {
                let valueA = a[column];
                let valueB = b[column];
                
                // 处理空值
                if (valueA === undefined || valueA === null) valueA = '';
                if (valueB === undefined || valueB === null) valueB = '';
                
                // 尝试转换为数字
                if (typeof valueA === 'number' && typeof valueB === 'number') {
                    return sortConfig.direction === 'asc' ? valueA - valueB : valueB - valueA;
                }
                
                // 字符串比较
                if (typeof valueA === 'string' && typeof valueB === 'string') {
                    valueA = valueA.toLowerCase();
                    valueB = valueB.toLowerCase();
                    return sortConfig.direction === 'asc' 
                        ? valueA.localeCompare(valueB) 
                        : valueB.localeCompare(valueA);
                }
                
                return 0;
            });
            
            currentPage = 1;
            renderTable();
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('total-accounts').textContent = totalRecords;
            
            // 计算总委托金额
            const totalAmount = caseData.reduce((sum, item) => {
                return sum + (item.entrustedAmount || 0);
            }, 0);
            
            document.getElementById('total-amount').textContent = `¥${totalAmount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
            
            // 计算勾选数量和金额
            const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
            document.getElementById('selected-accounts').textContent = selectedCheckboxes.length;
            
            // 计算勾选金额
            const selectedAmount = Array.from(selectedCheckboxes)
                .reduce((sum, checkbox) => {
                    const clientId = checkbox.getAttribute('data-id');
                    const item = caseData.find(d => d.clientId === clientId);
                    if (item) {
                        return sum + (item.entrustedAmount || 0);
                    }
                    return sum;
                }, 0);
            
            document.getElementById('selected-amount').textContent = `¥${selectedAmount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
        }
        
        // 更新分页信息
        function updatePaginationInfo() {
            const start = (currentPage - 1) * pageSize + 1;
            const end = Math.min(currentPage * pageSize, totalRecords);
            
            document.getElementById('start-record').textContent = start;
            document.getElementById('end-record').textContent = end;
            document.getElementById('total-records').textContent = totalRecords;
            
            // 更新页码输入框
            document.getElementById('pageNumInput').value = currentPage;
            const totalPages = Math.ceil(totalRecords / pageSize);
            document.getElementById('pageNumInput').max = totalPages;
            
            // 更新页码按钮状态
            for (let i = 1; i <= 3; i++) {
                const btn = document.getElementById(`page${i}`);
                if (i <= totalPages) {
                    btn.style.display = 'block';
                    btn.textContent = i;
                    if (i === currentPage) {
                        btn.classList.add('active');
                    } else {
                        btn.classList.remove('active');
                    }
                } else {
                    btn.style.display = 'none';
                }
            }
            
            // 更新首尾页按钮状态
            document.getElementById('firstPageBtn').disabled = currentPage === 1;
            document.getElementById('prevPageBtn').disabled = currentPage === 1;
            document.getElementById('nextPageBtn').disabled = currentPage >= totalPages;
            document.getElementById('lastPageBtn').disabled = currentPage >= totalPages;
        }
        
        // 筛选数据
        function filterData() {
            const clientName = document.getElementById('clientName').value.toLowerCase();
            const clientId = document.getElementById('clientId').value.trim();
            const city = document.getElementById('city').value.toLowerCase();
            const overduePeriod = document.getElementById('overduePeriod').value;
            const poolCount = document.getElementById('poolCount').value;
            const batchNumber = document.getElementById('batchNumber').value;
            const cardholderCode = document.getElementById('cardholderCode').value;
            const specialType = document.getElementById('specialType').value;
            const amountRange = document.getElementById('amountRange').value;
            const unfollowedDays = document.getElementById('unfollowedDays').value;
            const institution = document.getElementById('institution').value;
            const targetPeriod = document.getElementById('targetPeriod').value;
            const monthRange = document.getElementById('monthRange').value;
            const retentionCount = document.getElementById('retentionCount').value;
            const caseType = document.getElementById('caseType').value;
            const litigationTag = document.getElementById('litigationTag').value;
            const commissionRange = document.getElementById('commissionRange').value;
            const rating = document.getElementById('rating').value;
            const repairResult = document.getElementById('repairResult').value;
            const configStatus = document.getElementById('configStatus').value;
            
            // 应用筛选条件
            return fullData.filter(item => {
                // 客户姓名筛选
                if (clientName && !item.clientName?.toLowerCase().includes(clientName)) {
                    return false;
                }
                
                // 客户索引号筛选（精确匹配）
                if (clientId && item.clientId !== clientId) {
                    return false;
                }
                
                // 分案城市筛选
                if (city && !item.city?.toLowerCase().includes(city)) {
                    return false;
                }
                
                // 配置状态筛选 - 修复点
                if (configStatus) {
                    const expectedStatus = configStatus === 'locked' ? '已锁定' : '未锁定';
                    if (item.configStatus !== expectedStatus) {
                        return false;
                    }
                }
                
                // 其他筛选条件可以根据需要添加
                
                return true;
            });
        }
        
        // 重置筛选条件
        function resetFilters() {
            document.getElementById('clientName').value = '';
            document.getElementById('clientId').value = '';
            document.getElementById('city').value = '';
            document.getElementById('overduePeriod').value = '';
            document.getElementById('poolCount').value = '';
            document.getElementById('batchNumber').value = '';
            document.getElementById('cardholderCode').value = '';
            document.getElementById('specialType').value = '';
            document.getElementById('amountRange').value = '';
            document.getElementById('unfollowedDays').value = '';
            document.getElementById('institution').value = '';
            document.getElementById('targetPeriod').value = '';
            document.getElementById('monthRange').value = '';
            document.getElementById('retentionCount').value = '';
            document.getElementById('caseType').value = '';
            document.getElementById('litigationTag').value = '';
            document.getElementById('commissionRange').value = '';
            document.getElementById('rating').value = '';
            document.getElementById('repairResult').selectedIndex = 0;
            document.getElementById('configStatus').selectedIndex = 0;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 生成模拟数据
            fullData = generateMockData(100);
            caseData = [...fullData];
            totalRecords = caseData.length;
            
            // 初始化表格
            renderTable();
            
            // 初始化日期选择器
            flatpickr("#startDateRange", {
                mode: "range",
                dateFormat: "Y-m-d",
                locale: "zh"
            });
            
            flatpickr("#endDateRange", {
                mode: "range",
                dateFormat: "Y-m-d",
                locale: "zh"
            });
            
            // 搜索按钮点击事件
            document.getElementById('searchBtn').addEventListener('click', function() {
                const filteredData = filterData();
                caseData = filteredData;
                totalRecords = caseData.length;
                currentPage = 1;
                renderTable();
            });
            
            // 重置按钮点击事件
            document.getElementById('resetBtn').addEventListener('click', function() {
                resetFilters();
                caseData = [...fullData];
                totalRecords = caseData.length;
                currentPage = 1;
                renderTable();
            });
            
            // 全选/取消全选功能
            const selectAllCheckbox = document.getElementById('select-all');
            selectAllCheckbox.addEventListener('change', function() {
                const rowCheckboxes = document.querySelectorAll('.row-checkbox');
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateStats();
            });
            
            // 行复选框变化时更新统计
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('row-checkbox')) {
                    updateStats();
                }
            });
            
            // 分页控件事件处理
            // 每页显示条数变更
            document.getElementById('pageSizeSelect').addEventListener('change', function() {
                pageSize = parseInt(this.value);
                currentPage = 1;
                renderTable();
            });
            
            // 第一页按钮
            document.getElementById('firstPageBtn').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage = 1;
                    renderTable();
                }
            });
            
            // 上一页按钮
            document.getElementById('prevPageBtn').addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    renderTable();
                }
            });
            
            // 下一页按钮
            document.getElementById('nextPageBtn').addEventListener('click', function() {
                const totalPages = Math.ceil(totalRecords / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderTable();
                }
            });
            
            // 最后一页按钮
            document.getElementById('lastPageBtn').addEventListener('click', function() {
                const totalPages = Math.ceil(totalRecords / pageSize);
                if (currentPage < totalPages) {
                    currentPage = totalPages;
                    renderTable();
                }
            });
            
            // 页码按钮点击
            for (let i = 1; i <= 3; i++) {
                document.getElementById(`page${i}`).addEventListener('click', function() {
                    const pageNum = parseInt(this.textContent);
                    if (pageNum !== currentPage) {
                        currentPage = pageNum;
                        renderTable();
                    }
                });
            }
            
            // 前往指定页按钮
            document.getElementById('gotoPageBtn').addEventListener('click', function() {
                const pageNum = parseInt(document.getElementById('pageNumInput').value);
                const totalPages = Math.ceil(totalRecords / pageSize);
                if (pageNum >= 1 && pageNum <= totalPages && pageNum !== currentPage) {
                    currentPage = pageNum;
                    renderTable();
                }
            });
            
            // 功能按钮点击事件
            document.getElementById('lockBtn').addEventListener('click', function() {
                const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
                if (selectedCheckboxes.length === 0) {
                    alert('请先选择要锁定的账户');
                    return;
                }
                
                selectedCheckboxes.forEach(checkbox => {
                    const clientId = checkbox.getAttribute('data-id');
                    const item = caseData.find(d => d.clientId === clientId);
                    const fullItem = fullData.find(d => d.clientId === clientId);
                    if (item && fullItem) {
                        item.configStatus = '已锁定';
                        fullItem.configStatus = '已锁定';
                    }
                });
                
                renderTable();
                alert(`已锁定 ${selectedCheckboxes.length} 个账户`);
            });
            
            document.getElementById('unlockBtn').addEventListener('click', function() {
                const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
                if (selectedCheckboxes.length === 0) {
                    alert('请先选择要解除锁定的账户');
                    return;
                }
                
                selectedCheckboxes.forEach(checkbox => {
                    const clientId = checkbox.getAttribute('data-id');
                    const item = caseData.find(d => d.clientId === clientId);
                    const fullItem = fullData.find(d => d.clientId === clientId);
                    if (item && fullItem) {
                        item.configStatus = '未锁定';
                        fullItem.configStatus = '未锁定';
                    }
                });
                
                renderTable();
                alert(`已解除锁定 ${selectedCheckboxes.length} 个账户`);
            });
            
            document.getElementById('assignBtn').addEventListener('click', function() {
                const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
                if (selectedCheckboxes.length === 0) {
                    alert('请先选择要分派的账户');
                    return;
                }
                
                const clients = Array.from(selectedCheckboxes).map(checkbox => {
                    const clientId = checkbox.getAttribute('data-id');
                    const item = caseData.find(d => d.clientId === clientId);
                    return item ? item.clientName : '';
                }).filter(name => name);
                
                alert(`任务分派功能：已选择 ${clients.length} 个账户\n${clients.join(', ')}`);
            });
            
            document.getElementById('importBtn').addEventListener('click', function() {
                alert('导入功能：请上传包含以下字段的Excel文件：\n上一案件负责人\\工号(记录案件来源的最后操作人），客户索引号，当前案件负责人（案池，工号AC001）');
            });
            
            document.getElementById('exportBtn').addEventListener('click', function() {
                alert('导出功能：当前筛选结果已导出为Excel文件');
            });
            
            // 客户索引号点击事件
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('client-id-link')) {
                    const clientId = e.target.getAttribute('data-id');
                    const item = caseData.find(d => d.clientId === clientId);
                    if (item) {
                        alert(`查看客户详情：${item.clientName} (${clientId})\n工作地址：${item.city}\n委托金额：¥${item.entrustedAmount.toFixed(2)}\n（此页面只读，不可修改）`);
                    }
                }
            });
            
            // 表格排序功能
            document.querySelectorAll('.data-table th.sortable').forEach(th => {
                th.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    sortData(column);
                });
            });
        });
    </script>
</body>
</html>