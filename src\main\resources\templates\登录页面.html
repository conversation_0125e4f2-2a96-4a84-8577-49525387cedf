<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <title>CF-作业系统 | 登录</title>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#002b5c',
                        secondary: '#ffd700',
                        accent: '#0066cc',
                        neutral: '#f4f9ff',
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .bg-glass {
                backdrop-filter: blur(12px);
                background: rgba(255, 255, 255, 0.1);
            }
            .text-shadow {
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            }
            .animate-float {
                animation: float 6s ease-in-out infinite;
            }
            @keyframes float {
                0% { transform: translateY(0px); }
                50% { transform: translateY(-15px); }
                100% { transform: translateY(0px); }
            }
            .animate-gradient {
                background-size: 400% 400%;
                animation: gradientFlow 15s ease infinite;
            }
            @keyframes gradientFlow {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }
        }
    </style>
</head>
<body class="font-inter bg-neutral min-h-screen flex flex-col">
    <!-- 顶部区域 - LOGO和企业名称 -->
    <header class="bg-primary text-secondary py-4 px-8 flex items-center shadow-lg relative z-10">
        <div class="container mx-auto flex items-center">
            <div class="flex items-center space-x-3">
                <img id="logo" src="frontend/public/images/logo.png" alt="企业Logo" class="h-12 w-auto animate-float">
                <h1 id="companyName" class="text-2xl font-bold text-shadow">CF 智慧作业系统</h1>
            </div>
        </div>
    </header>

    <!-- 中间区域 - 企业文化和登录表单 -->
    <main class="flex-grow flex flex-col md:flex-row relative overflow-hidden animate-gradient bg-gradient-to-br from-primary to-accent">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-secondary"></div>
            <div class="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-accent"></div>
        </div>

        <!-- 中左区域 - 企业文化 -->
        <section class="w-full md:w-1/2 p-8 md:p-16 flex items-start justify-center relative z-10"> <!-- 修改为 items-start -->
            <div class="max-w-xl mt-8 md:mt-0"> <!-- 添加顶部边距 -->
                <h2 id="cultureTitle" class="text-4xl font-bold text-white mb-6 border-l-4 border-secondary pl-4">企业文化</h2>
                <div id="cultureContent" class="text-white/90 text-lg leading-relaxed space-y-4">
                    <p>我们始终秉持着创新、协作、责任与卓越的核心价值观。</p>
                    <p>发展是我们的使命，服务是我们的承诺、诚信是我们的基石，创新是我们的动力。</p>
                    <p>我们相信，只有通过不断的创新和卓越的服务，才能赢得客户的信任和尊重。</p>
                </div>
            </div>
        </section>

        <!-- 中右区域 - 登录表单 -->
        <section class="w-full md:w-1/2 p-8 md:p-16 flex items-center justify-center">
            <div class="w-full max-w-md bg-glass rounded-2xl p-8 shadow-2xl border border-white/20">
                <h3 class="text-2xl font-bold text-white mb-8 text-center">用户登录</h3>
                
                <form id="loginForm" class="space-y-6">
                    <!-- 工号输入 -->
                    <div class="space-y-2">
                        <label for="username" class="block text-white/80 text-sm font-medium">用户名</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-white/50"></i>
                            </div>
                            <input 
                                type="text" 
                                id="username" 
                                class="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all"
                                placeholder="请输入您的工号"
                                required
                            >
                        </div>
                    </div>

                    <!-- 密码输入 -->
                    <div class="space-y-2">
                        <label for="password" class="block text-white/80 text-sm font-medium">密码</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-white/50"></i>
                            </div>
                            <input 
                                type="password" 
                                id="password" 
                                class="w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all"
                                placeholder="请输入密码"
                                required
                            >
                            <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center text-white/50 hover:text-white">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 验证码 -->
                    <div class="space-y-2">
                        <label for="captcha" class="block text-white/80 text-sm font-medium">验证码</label>
                        <div class="flex gap-3">
                            <div class="relative flex-1">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-shield-alt text-white/50"></i>
                                </div>
                                <input 
                                    type="text" 
                                    id="captcha" 
                                    class="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all"
                                    placeholder="请输入验证码"
                                    required
                                >
                            </div>
                            <div id="captchaDisplay" class="flex items-center justify-center px-4 bg-white/20 border border-white/30 rounded-lg cursor-pointer hover:bg-white/30 transition-all">
                                <span id="captchaCode" class="font-mono text-white tracking-wider">X9K4</span>
                            </div>
                        </div>
                    </div>

                    <!-- 登录按钮 -->
                    <button 
                        type="submit" 
                        class="w-full py-3 bg-secondary text-primary font-semibold rounded-lg hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all transform hover:translate-y-[-2px] active:translate-y-0"
                    >
                        立即登录
                    </button>

                    <!-- 辅助功能 -->
                    <div class="flex justify-between items-center text-sm">
                        <label class="flex items-center text-white/70">
                            <input type="checkbox" class="mr-2 rounded bg-white/10 border-white/20 text-secondary focus:ring-secondary/50">
                            记住密码
                        </label>
                        <a href="#" class="text-white/70 hover:text-white transition-colors">忘记密码?</a>
                    </div>
                </form>
            </div>
        </section>
    </main>

    <!-- 底部区域 - 企业文化标语 -->
    <footer class="bg-primary text-secondary py-6 px-8 text-center">
        <p id="companySlogan" class="text-lg font-medium">做大、做强、做尊敬、做永恒</p>
    </footer>

    <script>
        // 密码显示/隐藏切换
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // 刷新验证码
        document.getElementById('captchaDisplay').addEventListener('click', function() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let code = '';
            for (let i = 0; i < 4; i++) {
                code += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('captchaCode').textContent = code;
            
            // 添加刷新动画
            this.classList.add('animate-pulse');
            setTimeout(() => {
                this.classList.remove('animate-pulse');
            }, 500);
        });

        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 表单验证
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const captcha = document.getElementById('captcha').value.trim();
            const captchaCode = document.getElementById('captchaCode').textContent;
            
            let isValid = true;
            
            if (!username) {
                showError('username', '请输入用户名');
                isValid = false;
            }
            
            if (!password) {
                showError('password', '请输入密码');
                isValid = false;
            }
            
            if (!captcha || captcha !== captchaCode) {
                showError('captcha', '验证码不正确');
                isValid = false;
            }
            
            if (isValid) {
                // 模拟登录请求
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 登录中...';
                
                setTimeout(() => {
                    // 模拟登录成功
                    alert('登录成功！即将跳转到系统首页');
                    
                    // 重置表单
                    this.reset();
                    document.getElementById('captchaDisplay').click();
                    
                    // 恢复按钮状态
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                    
                    // 这里可以添加跳转到首页的代码
                    // window.location.href = '/dashboard';
                }, 1500);
            }
        });

        // 显示错误信息
        function showError(fieldId, message) {
            // 先移除所有错误提示
            document.querySelectorAll('.error-message').forEach(el => el.remove());
            
            const field = document.getElementById(fieldId);
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message text-red-400 text-sm mt-1 flex items-center';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i> ${message}`;
            
            field.parentNode.appendChild(errorDiv);
            
            // 添加错误动画
            field.classList.add('border-red-400');
            field.classList.add('animate-shake');
            
            setTimeout(() => {
                field.classList.remove('animate-shake');
            }, 500);
            
            // 聚焦到错误字段
            field.focus();
        }

        // 为DOM添加全局动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }
            .animate-shake {
                animation: shake 0.5s ease-in-out;
            }
            .animate-pulse {
                animation: pulse 0.5s ease-in-out;
            }
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }
        `;
        document.head.appendChild(style);

        // 模拟从后台获取配置
        function loadConfig() {
            // 这里应该从API获取配置，现在用模拟数据
            const config = {
                logo: 'frontend/public/images/logo.png',
                companyName: 'CF 智慧作业系统',
                cultureTitle: '企业文化',
                cultureContent: `我们始终秉持着创新、协作、责任与卓越的核心价值观。
发展是我们的使命，服务是我们的承诺、诚信是我们的基石，创新是我们的动力。
我们相信，只有通过不断的创新和卓越的服务，才能赢得客户的信任和尊重。`,
                companySlogan: '做大、做强、做尊敬、做永恒',
                backgroundColor: 'linear-gradient(to-br, #002b5c, #0066cc)'
            };
            
            // 应用配置
            document.getElementById('logo').src = config.logo;
            document.getElementById('companyName').textContent = config.companyName;
            document.getElementById('cultureTitle').textContent = config.cultureTitle;
            document.getElementById('cultureContent').innerHTML = config.cultureContent.replace(/\n/g, '<p>');
            document.getElementById('companySlogan').textContent = config.companySlogan;
            document.querySelector('main').style.background = config.backgroundColor;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
        });
    </script>
</body>
</html>
    