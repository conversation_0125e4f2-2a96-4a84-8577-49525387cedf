<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分派记录弹框</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4CAF50',
                        secondary: '#81C784',
                        background: '#F5F5F5',
                        tableHeader: '#E8F5E9',
                        tableRow1: '#FFFFFF',
                        tableRow2: '#F1F8E9',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .border-primary {
                border-color: #4CAF50;
            }
            .shadow-modal {
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            }
            .transition-modal {
                transition: opacity 0.3s ease, transform 0.3s ease;
            }
        }
    </style>
</head>
<body class="bg-gray-200 min-h-screen flex items-center justify-center p-4">
    <!-- 弹框容器 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-300">
        <!-- 弹框主体 -->
        <div class="bg-white rounded-lg shadow-modal border-4 border-primary w-full max-w-4xl h-[500px] flex flex-col overflow-hidden transition-modal transform scale-100">
            <!-- 弹框头部 -->
            <div class="bg-primary text-white px-6 py-4 flex justify-between items-center">
                <h3 class="text-xl font-bold flex items-center">
                    <i class="fa fa-table mr-2"></i>分配记录
                </h3>
                <button id="closeModal" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fa fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- 弹框内容区 -->
            <div class="flex-1 overflow-auto p-4">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <table id="assignmentTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-tableHeader">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[5%]">序号</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">源组织</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">源用户</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">目标组织</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">目标用户</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">分配类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">操作人</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">操作时间</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 表格内容将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 弹框底部 -->
            <div class="px-6 py-4 bg-gray-50 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    共 <span id="totalCount" class="font-medium">25</span> 条记录
                </div>
                <div class="flex space-x-1">
                    <button id="prevPage" class="px-3 py-1 rounded border border-gray-300 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fa fa-angle-left"></i>
                    </button>
                    <div id="pageNumbers" class="flex">
                        <!-- 页码将通过JavaScript动态生成 -->
                    </div>
                    <button id="nextPage" class="px-3 py-1 rounded border border-gray-300 text-gray-600 hover:bg-gray-50">
                        <i class="fa fa-angle-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const assignmentData = [
            { id: 25, sourceOrg: '行政部', sourceUser: '卫十五', targetOrg: '运营部', targetUser: '蒋十六', type: '新增', operator: '沈十七', time: '2025-05-01 09:30' },
            { id: 24, sourceOrg: '技术部', sourceUser: '冯十二', targetOrg: '客服部', targetUser: '陈十三', type: '转移', operator: '褚十四', time: '2025-05-02 14:15' },
            { id: 23, sourceOrg: '财务部', sourceUser: '吴九', targetOrg: '人事部', targetUser: '郑十', type: '调整', operator: '钱十一', time: '2025-05-03 10:45' },
            { id: 22, sourceOrg: '市场部', sourceUser: '赵六', targetOrg: '销售部', targetUser: '孙七', type: '新增', operator: '周八', time: '2025-05-04 16:20' },
            { id: 21, sourceOrg: '研发部', sourceUser: '张三', targetOrg: '产品部', targetUser: '李四', type: '转移', operator: '王五', time: '2025-05-05 11:10' },
            { id: 20, sourceOrg: '行政部', sourceUser: '卫十五', targetOrg: '运营部', targetUser: '蒋十六', type: '新增', operator: '沈十七', time: '2025-05-06 09:30' },
            { id: 19, sourceOrg: '技术部', sourceUser: '冯十二', targetOrg: '客服部', targetUser: '陈十三', type: '转移', operator: '褚十四', time: '2025-05-07 14:15' },
            { id: 18, sourceOrg: '财务部', sourceUser: '吴九', targetOrg: '人事部', targetUser: '郑十', type: '调整', operator: '钱十一', time: '2025-05-08 10:45' },
            { id: 17, sourceOrg: '市场部', sourceUser: '赵六', targetOrg: '销售部', targetUser: '孙七', type: '新增', operator: '周八', time: '2025-05-09 16:20' },
            { id: 16, sourceOrg: '研发部', sourceUser: '张三', targetOrg: '产品部', targetUser: '李四', type: '转移', operator: '王五', time: '2025-05-10 11:10' },
            { id: 15, sourceOrg: '行政部', sourceUser: '卫十五', targetOrg: '运营部', targetUser: '蒋十六', type: '新增', operator: '沈十七', time: '2025-05-11 09:30' },
            { id: 14, sourceOrg: '技术部', sourceUser: '冯十二', targetOrg: '客服部', targetUser: '陈十三', type: '转移', operator: '褚十四', time: '2025-05-12 14:15' },
            { id: 13, sourceOrg: '财务部', sourceUser: '吴九', targetOrg: '人事部', targetUser: '郑十', type: '调整', operator: '钱十一', time: '2025-05-13 10:45' },
            { id: 12, sourceOrg: '市场部', sourceUser: '赵六', targetOrg: '销售部', targetUser: '孙七', type: '新增', operator: '周八', time: '2025-05-14 16:20' },
            { id: 11, sourceOrg: '研发部', sourceUser: '张三', targetOrg: '产品部', targetUser: '李四', type: '转移', operator: '王五', time: '2025-05-15 11:10' },
            { id: 10, sourceOrg: '行政部', sourceUser: '卫十五', targetOrg: '运营部', targetUser: '蒋十六', type: '新增', operator: '沈十七', time: '2025-05-16 09:30' },
            { id: 9, sourceOrg: '技术部', sourceUser: '冯十二', targetOrg: '客服部', targetUser: '陈十三', type: '转移', operator: '褚十四', time: '2025-05-17 14:15' },
            { id: 8, sourceOrg: '财务部', sourceUser: '吴九', targetOrg: '人事部', targetUser: '郑十', type: '调整', operator: '钱十一', time: '2025-05-18 10:45' },
            { id: 7, sourceOrg: '市场部', sourceUser: '赵六', targetOrg: '销售部', targetUser: '孙七', type: '新增', operator: '周八', time: '2025-05-19 16:20' },
            { id: 6, sourceOrg: '研发部', sourceUser: '张三', targetOrg: '产品部', targetUser: '李四', type: '转移', operator: '王五', time: '2025-05-20 11:10' },
            { id: 5, sourceOrg: '行政部', sourceUser: '卫十五', targetOrg: '运营部', targetUser: '蒋十六', type: '新增', operator: '沈十七', time: '2025-05-21 09:30' },
            { id: 4, sourceOrg: '技术部', sourceUser: '冯十二', targetOrg: '客服部', targetUser: '陈十三', type: '转移', operator: '褚十四', time: '2025-05-22 14:15' },
            { id: 3, sourceOrg: '财务部', sourceUser: '吴九', targetOrg: '人事部', targetUser: '郑十', type: '调整', operator: '钱十一', time: '2025-05-23 10:45' },
            { id: 2, sourceOrg: '市场部', sourceUser: '赵六', targetOrg: '销售部', targetUser: '孙七', type: '新增', operator: '周八', time: '2025-05-24 16:20' },
            { id: 1, sourceOrg: '研发部', sourceUser: '张三', targetOrg: '产品部', targetUser: '李四', type: '转移', operator: '王五', time: '2025-05-25 11:10' }
        ];

        // 分页配置
        const recordsPerPage = 5;
        let currentPage = 1;
        const totalPages = Math.ceil(assignmentData.length / recordsPerPage);

        // 关闭弹框功能
        document.getElementById('closeModal').addEventListener('click', closeModal);

        // 生成分页控件
        function generatePagination() {
            const pageNumbers = document.getElementById('pageNumbers');
            pageNumbers.innerHTML = '';
            
            // 显示当前页面前后各2页
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            // 调整起始页，确保显示5个页码
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }
            
            // 第一页按钮
            if (startPage > 1) {
                addPageButton(1);
                if (startPage > 2) {
                    addEllipsis();
                }
            }
            
            // 页码按钮
            for (let i = startPage; i <= endPage; i++) {
                addPageButton(i);
            }
            
            // 最后一页按钮
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    addEllipsis();
                }
                addPageButton(totalPages);
            }
            
            // 更新上一页/下一页按钮状态
            document.getElementById('prevPage').disabled = currentPage === 1;
            document.getElementById('nextPage').disabled = currentPage === totalPages;
        }
        
        // 添加页码按钮
        function addPageButton(pageNum) {
            const button = document.createElement('button');
            button.className = `px-3 py-1 rounded border ${pageNum === currentPage ? 'border-primary bg-primary text-white' : 'border-gray-300 text-gray-600 hover:bg-gray-50'}`;
            button.textContent = pageNum;
            button.addEventListener('click', () => {
                currentPage = pageNum;
                renderTable();
                generatePagination();
            });
            document.getElementById('pageNumbers').appendChild(button);
        }
        
        // 添加省略号
        function addEllipsis() {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'px-2 py-1';
            ellipsis.textContent = '...';
            document.getElementById('pageNumbers').appendChild(ellipsis);
        }
        
        // 渲染表格内容
        function renderTable() {
            const tableBody = document.getElementById('tableBody');
            tableBody.innerHTML = '';
            
            // 计算当前页的数据范围
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = Math.min(startIndex + recordsPerPage, assignmentData.length);
            const currentData = assignmentData.slice(startIndex, endIndex);
            
            // 生成表格行
            currentData.forEach((item, index) => {
                const row = document.createElement('tr');
                row.className = `bg-${index % 2 === 0 ? 'tableRow1' : 'tableRow2'} hover:bg-gray-50 transition-colors`;
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.sourceOrg}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.sourceUser}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.targetOrg}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.targetUser}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.type}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.operator}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.time}</td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // 关闭弹框
        function closeModal() {
            const modal = document.querySelector('.fixed');
            modal.classList.add('opacity-0');
            modal.querySelector('.max-w-4xl').classList.add('scale-95');
            
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }
        
        // 上一页/下一页事件监听
        document.getElementById('prevPage').addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                renderTable();
                generatePagination();
            }
        });
        
        document.getElementById('nextPage').addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                renderTable();
                generatePagination();
            }
        });
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            renderTable();
            generatePagination();
        });
    </script>
</body>
</html>
    