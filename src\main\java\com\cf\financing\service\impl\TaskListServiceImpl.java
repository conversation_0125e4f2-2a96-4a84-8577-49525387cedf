package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.TaskList;
import com.cf.financing.mapper.TaskListMapper;
import com.cf.financing.service.ITaskListService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 作业清单服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Service
public class TaskListServiceImpl extends ServiceImpl<TaskListMapper, TaskList> implements ITaskListService {

    @Override
    public IPage<TaskList> getTaskListPage(Page<TaskList> page,
                                          String taskNo,
                                          String caseNo,
                                          String customerName,
                                          Integer taskType,
                                          Integer taskStatus,
                                          Long assignedUserId,
                                          LocalDateTime startTime,
                                          LocalDateTime endTime) {
        return baseMapper.selectTaskListPage(page, taskNo, caseNo, customerName,
                taskType, taskStatus, assignedUserId, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createTask(TaskList taskList) {
        if (taskList.getTaskNo() == null || taskList.getTaskNo().isEmpty()) {
            taskList.setTaskNo(generateTaskNo());
        }
        taskList.setTaskStatus(0); // 默认待处理状态
        taskList.setCreateTime(LocalDateTime.now());
        taskList.setUpdateTime(LocalDateTime.now());
        return save(taskList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTask(TaskList taskList) {
        taskList.setUpdateTime(LocalDateTime.now());
        return updateById(taskList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignTask(Long taskId, Long assignedUserId, String assignedUserName, String assignBy) {
        TaskList taskList = getById(taskId);
        if (taskList == null) {
            return false;
        }
        taskList.setAssignedUserId(assignedUserId);
        taskList.setAssignedUserName(assignedUserName);
        taskList.setUpdateBy(assignBy);
        taskList.setUpdateTime(LocalDateTime.now());
        return updateById(taskList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startTask(Long taskId, String startBy) {
        TaskList taskList = getById(taskId);
        if (taskList == null || taskList.getTaskStatus() != 0) {
            return false;
        }
        taskList.setTaskStatus(1); // 处理中
        taskList.setActualStartTime(LocalDateTime.now());
        taskList.setUpdateBy(startBy);
        taskList.setUpdateTime(LocalDateTime.now());
        return updateById(taskList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeTask(Long taskId, String result, String remark, String completeBy) {
        TaskList taskList = getById(taskId);
        if (taskList == null || taskList.getTaskStatus() != 1) {
            return false;
        }
        taskList.setTaskStatus(2); // 已完成
        taskList.setActualEndTime(LocalDateTime.now());
        taskList.setResult(result);
        taskList.setRemark(remark);
        taskList.setUpdateBy(completeBy);
        taskList.setUpdateTime(LocalDateTime.now());
        return updateById(taskList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelTask(Long taskId, String cancelReason, String cancelBy) {
        TaskList taskList = getById(taskId);
        if (taskList == null) {
            return false;
        }
        taskList.setTaskStatus(3); // 已取消
        taskList.setRemark(cancelReason);
        taskList.setUpdateBy(cancelBy);
        taskList.setUpdateTime(LocalDateTime.now());
        return updateById(taskList);
    }

    @Override
    public List<TaskList> getPendingTasksByUserId(Long userId) {
        return baseMapper.selectPendingTasksByUserId(userId);
    }

    @Override
    public List<TaskList> getTasksByCaseId(Long caseId) {
        return baseMapper.selectTasksByCaseId(caseId);
    }

    @Override
    public List<Map<String, Object>> getTaskStatusStatistics(LocalDateTime startTime,
                                                             LocalDateTime endTime,
                                                             Long assignedUserId) {
        return baseMapper.selectTaskStatusStatistics(startTime, endTime, assignedUserId);
    }

    @Override
    public List<Map<String, Object>> getTaskTypeStatistics(LocalDateTime startTime,
                                                           LocalDateTime endTime,
                                                           Long assignedUserId) {
        return baseMapper.selectTaskTypeStatistics(startTime, endTime, assignedUserId);
    }

    @Override
    public List<TaskList> getOverdueTasks() {
        return baseMapper.selectOverdueTasks(LocalDateTime.now());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateTaskStatus(List<Long> taskIds, Integer taskStatus, String updateBy) {
        int count = baseMapper.batchUpdateTaskStatus(taskIds, taskStatus, updateBy);
        return count > 0;
    }

    @Override
    public String generateTaskNo() {
        String prefix = "TASK";
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.valueOf((int) (Math.random() * 1000));
        return prefix + timestamp + String.format("%03d", Integer.parseInt(random));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoCreateTasksByCase(Long caseId, String caseNo, String customerName, String createBy) {
        try {
            // 创建电话催收任务
            TaskList phoneTask = new TaskList();
            phoneTask.setCaseId(caseId);
            phoneTask.setCaseNo(caseNo);
            phoneTask.setCustomerName(customerName);
            phoneTask.setTaskType(1); // 电话催收
            phoneTask.setPriority(2); // 中等优先级
            phoneTask.setTaskDescription("对客户 " + customerName + " 进行电话催收");
            phoneTask.setPlannedStartTime(LocalDateTime.now());
            phoneTask.setPlannedEndTime(LocalDateTime.now().plusDays(1));
            phoneTask.setCreateBy(createBy);
            createTask(phoneTask);

            // 创建法务处理任务（如果需要）
            TaskList legalTask = new TaskList();
            legalTask.setCaseId(caseId);
            legalTask.setCaseNo(caseNo);
            legalTask.setCustomerName(customerName);
            legalTask.setTaskType(3); // 法务处理
            legalTask.setPriority(3); // 高优先级
            legalTask.setTaskDescription("对案件 " + caseNo + " 进行法务处理评估");
            legalTask.setPlannedStartTime(LocalDateTime.now().plusDays(3));
            legalTask.setPlannedEndTime(LocalDateTime.now().plusDays(7));
            legalTask.setCreateBy(createBy);
            createTask(legalTask);

            return true;
        } catch (Exception e) {
            return false;
        }
    }
}