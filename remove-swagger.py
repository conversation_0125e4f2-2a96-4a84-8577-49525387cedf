#!/usr/bin/env python3
import os
import re

def remove_swagger_annotations(file_path):
    """Remove Swagger annotations from a Java file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove @Api annotations
    content = re.sub(r'@Api\([^)]*\)\s*\n', '', content)
    
    # Remove @ApiOperation annotations
    content = re.sub(r'@ApiOperation\([^)]*\)\s*\n', '', content)
    
    # Remove @ApiParam annotations (but keep the parameter)
    content = re.sub(r'@ApiParam\([^)]*\)\s+', '', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ Removed Swagger annotations from {file_path}")

def main():
    controller_files = [
        "src/main/java/com/cf/financing/controller/ContactRecordController.java",
        "src/main/java/com/cf/financing/controller/CaseInfoController.java", 
        "src/main/java/com/cf/financing/controller/RepaymentRecordController.java",
        "src/main/java/com/cf/financing/controller/CustomerInfoController.java",
        "src/main/java/com/cf/financing/controller/SysUserController.java",
        "src/main/java/com/cf/financing/controller/StatisticsController.java"
    ]
    
    for file_path in controller_files:
        if os.path.exists(file_path):
            remove_swagger_annotations(file_path)
        else:
            print(f"⚠ File not found: {file_path}")
    
    print("All Swagger annotations removed!")

if __name__ == "__main__":
    main()
