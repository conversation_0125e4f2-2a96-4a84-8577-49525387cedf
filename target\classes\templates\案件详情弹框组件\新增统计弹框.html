<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增统计 - 优化布局</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }

        .modal {
            width: 800px;
            height: 500px;
            background-color: white;
            border: 5px solid #4CAF50;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #4CAF50;
            color: white;
        }

        .header h2 {
            font-size: 20px;
        }

        .close {
            font-size: 20px;
            cursor: pointer;
        }

        .content {
            padding: 20px;
            flex: 1;
            overflow: hidden;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 15px;
        }

        .form-row {
            display: flex;
            margin-bottom: 10px;
        }

        .form-row label {
            width: 120px;
            font-size: 14px;
            display: flex;
            align-items: center;
            margin-right: 10px;
        }

        .form-row input,
        .form-row select {
            width: 220px;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px 0;
        }

        .notes-container {
            display: flex;
            gap: 15px;
            align-items: flex-end; /* 确保按钮与备注框底部对齐 */
        }

        /* 备注文本框样式 */
        .notes-container textarea {
            width: 450px;
            height: 150px;
            padding: 12px; /* 统一上下左右内边距 */
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
            resize: none !important;
            overflow: auto;
            min-width: 450px !important;
            max-width: 450px !important;
            min-height: 150px !important;
            max-height: 150px !important;
        }

        .btn-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            justify-content: flex-end; /* 按钮组底部对齐 */
            height: 100%; /* 与备注框等高 */
        }

        .btn {
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
            width: 100px;
        }

        .btn-save {
            background-color: #4CAF50;
            color: white;
            border: none;
        }

        .btn-save:hover {
            background-color: #45a049;
        }

        .btn-cancel {
            background-color: #f1f1f1;
            color: #333;
            border: 1px solid #ddd;
        }

        .btn-cancel:hover {
            background-color: #e9e9e9;
        }
    </style>
</head>
<body>
    <div class="modal">
        <div class="header">
            <h2>新增统计</h2>
            <span class="close">&times;</span>
        </div>
        <div class="content">
            <div class="form-grid">
                <div class="form-row">
                    <label>协商总金额</label>
                    <input type="number" placeholder="请输入协商总金额">
                </div>
                <div class="form-row">
                    <label>分期方式</label>
                    <select>
                        <option value="">请选择分期方式</option>
                        <option value="online">线上分期</option>
                        <option value="offline">线下分期</option>
                        <option value="one-time">一次性付清</option>
                        <option value="other">其他方式</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>分期数</label>
                    <input type="number" placeholder="请输入分期数">
                </div>
                <div class="form-row">
                    <label>已还款金额</label>
                    <input type="number" placeholder="请输入已还款金额">
                </div>
                <div class="form-row">
                    <label>次月还款金额</label>
                    <input type="number" placeholder="请输入次月还款金额">
                </div>
                <div class="form-row">
                    <label>次月还款时间</label>
                    <input type="date" value="2025-06-30">
                </div>
                <div class="form-row">
                    <label>登记时间</label>
                    <input type="text" value="2025-05-30 05:04:49" disabled>
                </div>
                <div class="form-row">
                    <label>登记人</label>
                    <input type="text" placeholder="请输入登记人姓名">
                </div>
            </div>
            <div class="form-group">
                <label>备注: 记录分期方案</label>
                <div class="notes-container">
                    <textarea placeholder="请输入备注信息">此备注框具有以下优化：
1. 按钮与备注框底部对齐
2. 文本内容四周有相等的页边距
3. 固定尺寸不可调整</textarea>
                    <div class="btn-group">
                        <button class="btn btn-cancel">取消</button>
                        <button class="btn btn-save">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const closeBtn = document.querySelector('.close');
            const cancelBtn = document.querySelector('.btn-cancel');
            const saveBtn = document.querySelector('.btn-save');
            const modal = document.querySelector('.modal');
            const form = document.querySelector('form');

            function closeModal() {
                if (confirm('确定要取消吗？未保存的修改将丢失。')) {
                    modal.style.display = 'none';
                }
            }

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            saveBtn.addEventListener('click', function() {
                const formData = {};
                document.querySelectorAll('input, select, textarea').forEach(field => {
                    if (field.name) {
                        formData[field.name] = field.value;
                    }
                });

                console.log('保存数据:', formData);
                alert('数据已成功保存！');
                modal.style.display = 'none';
            });

            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                });
            }
        });
    </script>
</body>
</html>