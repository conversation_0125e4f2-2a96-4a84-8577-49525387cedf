<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化响应式导航栏</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* 导航栏样式 */
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(90deg, #1a2a6c, #b21f1f, #1a2a6c);
            padding: 0 30px;
            height: 80px;
            position: relative;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo i {
            font-size: 28px;
            color: white;
            margin-right: 10px;
        }
        
        .logo h1 {
            color: white;
            font-size: 24px;
            font-weight: 700;
            letter-spacing: 1px;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            transition: all 0.4s ease;
        }
        
        .nav-links li {
            margin: 0 15px;
            position: relative;
        }
        
        .nav-links li a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-size: 18px;
            font-weight: 500;
            padding: 8px 5px;
            transition: all 0.3s;
            display: block;
        }
        
        .nav-links li a:hover {
            color: white;
            transform: translateY(-2px);
        }
        
        .nav-links li a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 3px;
            background: #ffcc00;
            transition: width 0.3s;
            border-radius: 2px;
        }
        
        .nav-links li a:hover::after {
            width: 100%;
        }
        
        .active {
            color: white !important;
        }
        
        .active::after {
            width: 100% !important;
        }
        
        .search-box {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            padding: 5px 15px;
            margin-left: 20px;
            transition: all 0.3s;
        }
        
        .search-box:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .search-box input {
            background: transparent;
            border: none;
            outline: none;
            color: white;
            font-size: 16px;
            width: 0;
            transition: width 0.5s;
        }
        
        .search-box:hover input {
            width: 150px;
            padding: 0 10px;
        }
        
        .search-box input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .search-box i {
            color: white;
            cursor: pointer;
        }
        
        .hamburger {
            display: none;
            cursor: pointer;
        }
        
        .hamburger div {
            width: 30px;
            height: 3px;
            background: white;
            margin: 6px 0;
            transition: all 0.3s;
        }
        
        /* 移动端样式 */
        @media (max-width: 900px) {
            nav {
                padding: 0 20px;
            }
            
            .nav-links {
                position: absolute;
                top: 80px;
                left: 0;
                width: 100%;
                background: linear-gradient(90deg, #1a2a6c, #b21f1f);
                flex-direction: column;
                align-items: center;
                padding: 20px 0;
                clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
            }
            
            .nav-links.open {
                clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
            }
            
            .nav-links li {
                margin: 15px 0;
                opacity: 0;
            }
            
            .nav-links.open li {
                animation: fadeIn 0.5s forwards;
            }
            
            .nav-links li:nth-child(1) {
                animation-delay: 0.1s;
            }
            
            .nav-links li:nth-child(2) {
                animation-delay: 0.2s;
            }
            
            .nav-links li:nth-child(3) {
                animation-delay: 0.3s;
            }
            
            .nav-links li:nth-child(4) {
                animation-delay: 0.4s;
            }
            
            .nav-links li:nth-child(5) {
                animation-delay: 0.5s;
            }
            
            .search-box {
                position: absolute;
                top: 80px;
                left: 0;
                width: 100%;
                margin: 0;
                border-radius: 0;
                padding: 15px;
                background: rgba(0, 0, 0, 0.2);
                clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
                transition: all 0.4s;
            }
            
            .search-box.open {
                clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
            }
            
            .search-box input {
                width: 100%;
                padding: 0 10px;
            }
            
            .search-box:hover input {
                width: 100%;
            }
            
            .hamburger {
                display: block;
            }
        }
        
        /* 内容区域 */
        .content {
            padding: 40px;
            text-align: center;
        }
        
        .content h2 {
            font-size: 36px;
            color: #1a2a6c;
            margin-bottom: 20px;
        }
        
        .content p {
            font-size: 18px;
            color: #555;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        
        .code-container {
            background: #2d3748;
            border-radius: 10px;
            padding: 20px;
            text-align: left;
            margin-top: 30px;
            overflow-x: auto;
        }
        
        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .code-header h3 {
            color: white;
            font-size: 18px;
        }
        
        .copy-btn {
            background: #4a5568;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .copy-btn:hover {
            background: #2d3748;
        }
        
        pre {
            color: #cbd5e0;
            font-size: 16px;
            line-height: 1.5;
        }
        
        /* 动画 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 汉堡菜单动画 */
        .hamburger.open div:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }
        
        .hamburger.open div:nth-child(2) {
            opacity: 0;
        }
        
        .hamburger.open div:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }
    </style>
</head>
<body>
    <div class="container">
        <nav>
            <div class="logo">
                <i class="fas fa-rocket"></i>
                <h1>TechNav</h1>
            </div>
            
            <ul class="nav-links">
                <li><a href="#" class="active">首页</a></li>
                <li><a href="#">产品</a></li>
                <li><a href="#">服务</a></li>
                <li><a href="#">案例</a></li>
                <li><a href="#">关于我们</a></li>
            </ul>
            
            <div class="search-box">
                <input type="text" placeholder="搜索...">
                <i class="fas fa-search"></i>
            </div>
            
            <div class="hamburger">
                <div></div>
                <div></div>
                <div></div>
            </div>
        </nav>
        
        <div class="content">
            <h2>现代化响应式导航栏</h2>
            <p>这个导航栏组件具有响应式设计，在移动设备上会自动转换为汉堡菜单。它包含了Logo、导航链接、搜索框等常见元素，并添加了平滑的过渡动画和悬停效果。</p>
            
            <div class="code-container">
                <div class="code-header">
                    <h3>HTML 代码示例</h3>
                    <button class="copy-btn" onclick="copyCode()">复制代码</button>
                </div>
                <pre><code>&lt;nav&gt;
  &lt;div class="logo"&gt;
    &lt;i class="fas fa-rocket"&gt;&lt;/i&gt;
    &lt;h1&gt;TechNav&lt;/h1&gt;
  &lt;/div&gt;
  
  &lt;ul class="nav-links"&gt;
    &lt;li&gt;&lt;a href="#" class="active"&gt;首页&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;产品&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;服务&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;案例&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;关于我们&lt;/a&gt;&lt;/li&gt;
  &lt;/ul&gt;
  
  &lt;div class="search-box"&gt;
    &lt;input type="text" placeholder="搜索..."&gt;
    &lt;i class="fas fa-search"&gt;&lt;/i&gt;
  &lt;/div&gt;
  
  &lt;div class="hamburger"&gt;
    &lt;div&gt;&lt;/div&gt;
    &lt;div&gt;&lt;/div&gt;
    &lt;div&gt;&lt;/div&gt;
  &lt;/div&gt;
&lt;/nav&gt;</code></pre>
            </div>
        </div>
    </div>

    <script>
        // 汉堡菜单交互
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.nav-links');
        const searchBox = document.querySelector('.search-box');
        
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('open');
            navLinks.classList.toggle('open');
            searchBox.classList.toggle('open');
        });
        
        // 复制代码功能
        function copyCode() {
            const code = document.querySelector('pre code').innerText;
            navigator.clipboard.writeText(code)
                .then(() => {
                    const btn = document.querySelector('.copy-btn');
                    btn.textContent = '已复制!';
                    setTimeout(() => {
                        btn.textContent = '复制代码';
                    }, 2000);
                });
        }
        
        // 当前年份显示
        const yearSpan = document.getElementById('current-year');
        yearSpan.textContent = new Date().getFullYear();
    </script>
</body>
</html>