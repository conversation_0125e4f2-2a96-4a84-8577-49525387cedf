package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.ExchangeOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 换单记录 Mapper 接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface ExchangeOrderMapper extends BaseMapper<ExchangeOrder> {

    /**
     * 分页查询换单记录
     *
     * @param page 分页参数
     * @param exchangeNo 换单编号
     * @param originalCaseNo 原案件编号
     * @param newCaseNo 新案件编号
     * @param customerName 客户姓名
     * @param exchangeType 换单类型
     * @param exchangeStatus 换单状态
     * @param applicantId 申请人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<ExchangeOrder> selectExchangeOrderPage(Page<ExchangeOrder> page,
                                                 @Param("exchangeNo") String exchangeNo,
                                                 @Param("originalCaseNo") String originalCaseNo,
                                                 @Param("newCaseNo") String newCaseNo,
                                                 @Param("customerName") String customerName,
                                                 @Param("exchangeType") Integer exchangeType,
                                                 @Param("exchangeStatus") Integer exchangeStatus,
                                                 @Param("applicantId") Long applicantId,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 根据客户ID查询换单记录
     *
     * @param customerId 客户ID
     * @return 换单记录列表
     */
    List<ExchangeOrder> selectExchangeOrdersByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据案件ID查询相关换单记录
     *
     * @param caseId 案件ID
     * @return 换单记录列表
     */
    List<ExchangeOrder> selectExchangeOrdersByCaseId(@Param("caseId") Long caseId);

    /**
     * 查询待审核的换单记录
     *
     * @param reviewerId 审核人ID（可选）
     * @return 待审核换单记录列表
     */
    List<ExchangeOrder> selectPendingExchangeOrders(@Param("reviewerId") Long reviewerId);

    /**
     * 统计换单类型分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param applicantId 申请人ID（可选）
     * @return 类型统计结果
     */
    List<Map<String, Object>> selectExchangeTypeStatistics(@Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime,
                                                           @Param("applicantId") Long applicantId);

    /**
     * 统计换单状态分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param applicantId 申请人ID（可选）
     * @return 状态统计结果
     */
    List<Map<String, Object>> selectExchangeStatusStatistics(@Param("startTime") LocalDateTime startTime,
                                                             @Param("endTime") LocalDateTime endTime,
                                                             @Param("applicantId") Long applicantId);

    /**
     * 查询换单趋势数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组方式（day, week, month）
     * @return 趋势数据
     */
    List<Map<String, Object>> selectExchangeTrendData(@Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime,
                                                      @Param("groupBy") String groupBy);

    /**
     * 查询用户换单统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 用户统计结果
     */
    List<Map<String, Object>> selectUserExchangeStatistics(@Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime,
                                                           @Param("limit") Integer limit);

    /**
     * 查询换单金额统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param exchangeType 换单类型（可选）
     * @return 金额统计结果
     */
    Map<String, Object> selectExchangeAmountStatistics(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime,
                                                       @Param("exchangeType") Integer exchangeType);

    /**
     * 批量更新换单状态
     *
     * @param exchangeIds 换单ID列表
     * @param exchangeStatus 新状态
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @param reviewComment 审核意见
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateExchangeStatus(@Param("exchangeIds") List<Long> exchangeIds,
                                 @Param("exchangeStatus") Integer exchangeStatus,
                                 @Param("reviewerId") Long reviewerId,
                                 @Param("reviewerName") String reviewerName,
                                 @Param("reviewComment") String reviewComment,
                                 @Param("updateBy") String updateBy);

    /**
     * 检查案件是否存在进行中的换单
     *
     * @param caseId 案件ID
     * @return 进行中的换单数量
     */
    int countPendingExchangesByCaseId(@Param("caseId") Long caseId);
}