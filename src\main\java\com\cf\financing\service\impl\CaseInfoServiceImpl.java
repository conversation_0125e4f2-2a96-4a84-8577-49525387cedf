package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.CaseInfo;
import com.cf.financing.mapper.CaseInfoMapper;
import com.cf.financing.service.ICaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 案件信息服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class CaseInfoServiceImpl extends ServiceImpl<CaseInfoMapper, CaseInfo> implements ICaseInfoService {

    @Override
    public IPage<CaseInfo> getCaseInfoPage(
            Page<CaseInfo> page,
            String caseNo,
            String customerName,
            String customerPhone,
            String caseStatus,
            String caseLevel,
            Long assignUserId,
            LocalDate overdueStartDate,
            LocalDate overdueEndDate,
            BigDecimal minOverdueAmount,
            BigDecimal maxOverdueAmount) {
        
        return baseMapper.selectCaseInfoPage(
                page, caseNo, customerName, customerPhone, caseStatus, caseLevel,
                assignUserId, overdueStartDate, overdueEndDate, minOverdueAmount, maxOverdueAmount
        );
    }

    @Override
    public CaseInfo getCaseInfoById(Long id) {
        return baseMapper.selectCaseInfoById(id);
    }

    @Override
    public IPage<CaseInfo> getMyCaseList(
            Page<CaseInfo> page,
            Long assignUserId,
            String caseStatus,
            String caseLevel) {
        
        return baseMapper.selectMyCaseList(page, assignUserId, caseStatus, caseLevel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCase(CaseInfo caseInfo) {
        try {
            // 生成案件编号
            if (!StringUtils.hasText(caseInfo.getCaseNo())) {
                caseInfo.setCaseNo(generateCaseNo());
            }
            
            // 设置默认状态
            if (!StringUtils.hasText(caseInfo.getCaseStatus())) {
                caseInfo.setCaseStatus("PENDING");
            }
            
            return save(caseInfo);
        } catch (Exception e) {
            log.error("创建案件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCase(CaseInfo caseInfo) {
        try {
            return updateById(caseInfo);
        } catch (Exception e) {
            log.error("更新案件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignCase(Long caseId, Long assignUserId) {
        try {
            CaseInfo caseInfo = new CaseInfo();
            caseInfo.setId(caseId);
            caseInfo.setAssignUserId(assignUserId);
            caseInfo.setAssignTime(LocalDateTime.now());
            
            return updateById(caseInfo);
        } catch (Exception e) {
            log.error("分配案件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAssignCases(List<Long> caseIds, Long assignUserId) {
        try {
            int result = baseMapper.batchAssignCases(caseIds, assignUserId);
            return result > 0;
        } catch (Exception e) {
            log.error("批量分配案件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCaseStatus(Long caseId, String caseStatus) {
        try {
            CaseInfo caseInfo = new CaseInfo();
            caseInfo.setId(caseId);
            caseInfo.setCaseStatus(caseStatus);
            
            return updateById(caseInfo);
        } catch (Exception e) {
            log.error("更新案件状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCaseStatus(List<Long> caseIds, String caseStatus) {
        try {
            int result = baseMapper.batchUpdateCaseStatus(caseIds, caseStatus);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新案件状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContactInfo(Long caseId, String contactResult, LocalDate nextContactTime) {
        try {
            CaseInfo caseInfo = new CaseInfo();
            caseInfo.setId(caseId);
            caseInfo.setContactResult(contactResult);
            caseInfo.setLastContactTime(LocalDateTime.now());
            if (nextContactTime != null) {
                caseInfo.setNextContactTime(nextContactTime.atStartOfDay());
            }
            
            return updateById(caseInfo);
        } catch (Exception e) {
            log.error("更新联系信息失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getCaseStatistics(Long assignUserId, LocalDate startDate, LocalDate endDate) {
        return baseMapper.selectCaseStatistics(assignUserId, startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getOverdueCaseStatsByLevel(Long assignUserId) {
        return baseMapper.selectOverdueCaseStatsByLevel(assignUserId);
    }

    @Override
    public List<Map<String, Object>> getCaseTrendData(Long assignUserId, Integer days) {
        return baseMapper.selectCaseTrendData(assignUserId, days);
    }

    @Override
    public int getPendingCaseCount(Long assignUserId) {
        return baseMapper.selectPendingCaseCount(assignUserId);
    }

    @Override
    public List<CaseInfo> getTodayDueCases(Long assignUserId) {
        return baseMapper.selectTodayDueCases(assignUserId, LocalDate.now());
    }

    @Override
    public String generateCaseNo() {
        // 生成格式: CASE + 年月日 + 4位序号
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 查询当日最大序号
        LambdaQueryWrapper<CaseInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(CaseInfo::getCaseNo, "CASE" + dateStr)
               .orderByDesc(CaseInfo::getCaseNo)
               .last("LIMIT 1");
        
        CaseInfo lastCase = getOne(wrapper);
        int sequence = 1;
        
        if (lastCase != null && lastCase.getCaseNo() != null) {
            String lastCaseNo = lastCase.getCaseNo();
            if (lastCaseNo.length() >= 16) {
                try {
                    sequence = Integer.parseInt(lastCaseNo.substring(12)) + 1;
                } catch (NumberFormatException e) {
                    log.warn("解析案件编号序号失败: {}", lastCaseNo);
                }
            }
        }
        
        return String.format("CASE%s%04d", dateStr, sequence);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCase(Long caseId) {
        try {
            return removeById(caseId);
        } catch (Exception e) {
            log.error("删除案件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteCases(List<Long> caseIds) {
        try {
            return removeByIds(caseIds);
        } catch (Exception e) {
            log.error("批量删除案件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getCaseStatusDistribution() {
        return baseMapper.selectCaseStatusDistribution();
    }

    @Override
    public List<Map<String, Object>> getDailyCaseStats(Integer days, Long assignUserId) {
        return baseMapper.selectDailyCaseStats(days, assignUserId);
    }

    @Override
    public List<Map<String, Object>> getMonthlyCaseStats(Integer months, Long assignUserId) {
        return baseMapper.selectMonthlyCaseStats(months, assignUserId);
    }
}