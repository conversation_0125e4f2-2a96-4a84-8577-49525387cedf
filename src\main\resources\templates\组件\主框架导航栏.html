<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CF-作业系统 - 导航栏</title>
    <!-- 引入Font Awesome图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 颜色变量定义 - 整个系统的颜色方案 */
        :root {
            --primary-color: #1a2a6c;       /* 主色调：深蓝 */
            --secondary-color: #FFCC00;     /* 辅助色：金色 */
            --error-color: #F53F3F;         /* 错误/通知色：红色 */
            --sidebar-bg: #1E293B;          /* 侧边栏背景色 */
            --sidebar-item: #CBD5E1;        /* 侧边栏文本色 */
            --sidebar-item-active: #FFCC00; /* 侧边栏激活文本色 */
            --nav-bg: #1a2a6c;              /* 导航栏背景色 */
        }

        /* 页面主体样式 */
        body {
            background-color: #f0f2f5;
            padding: 20px;
        }

        /* 演示容器 - 用于展示导航栏 */
        .container-demo {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 20px;
        }

        /* ===== 导航栏样式 ===== */
        .nav-bar-1 {
            width: 100%;
            height: 64px;
            background-color: var(--nav-bg);
            display: flex;
            z-index: 100;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        /* ===== Logo区域样式 ===== */
        .logo-area {
            width: 280px;
            height: 100%;
            background-color: var(--sidebar-bg);
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-right: 1px solid rgba(255,255,255,0.1);
        }

        .logo-image {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo-image i {
            color: white;
            font-size: 24px;
        }

        .company-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--sidebar-item-active);
            letter-spacing: 0.5px;
        }

        /* ===== 系统消息区域样式 ===== */
        .system-messages {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 0 20px;
            overflow: hidden;
            position: relative;
        }

        .messages-container {
            flex: 1;
            height: 32px;
            overflow: hidden;
            position: relative;
        }

        .message-list {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .message-item {
            height: 32px;
            line-height: 32px;
            color: var(--sidebar-item-active);
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 10px;
            border-radius: 4px;
        }

        .message-info {
            background-color: rgba(26, 42, 108, 0.2);
        }

        .message-warning {
            background-color: rgba(255, 204, 0, 0.2);
        }

        /* ===== 用户信息区域样式 ===== */
        .user-info {
            width: 300px;
            height: 100%;
            background-color: var(--sidebar-bg);
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 20px;
            color: var(--sidebar-item-active);
            border-left: 1px solid rgba(255,255,255,0.1);
            position: relative;
        }

        .greeting-container {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }

        /* 问候语样式，控制与铃铛和头像的间距 */
        .greeting-text {
            margin: 0 10px; /* 左右各10px间距 */
            font-size: 16px;
        }

        /* ===== 通知铃铛样式 ===== */
        .notification-bell {
            margin-right: 15px; /* 右侧间距 */
            font-size: 22px;
            cursor: pointer;
            position: relative;
            transition: transform 0.3s; /* 悬停时的放大效果 */
            color: white;
        }

        /* 铃铛悬停效果 - 轻微放大 */
        .notification-bell:hover {
            transform: scale(1.2);
        }

        /* 通知计数样式 */
        .notification-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
        }

        /* ===== 通知面板样式 ===== */
        .notification-panel {
            position: fixed;
            top: 64px;
            right: -320px;
            width: 320px;
            height: calc(100vh - 64px);
            background-color: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s;
            z-index: 1000;
            overflow-y: auto;
        }

        .notification-panel.active {
            right: 0;
        }

        .notification-header {
            padding: 16px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
        }

        .close-notification {
            cursor: pointer;
            color: #64748B;
        }

        .close-notification:hover {
            color: var(--error-color);
        }

        .notification-list {
            padding: 16px;
        }

        .notification-item {
            padding: 12px;
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative;
        }

        .notification-item:hover {
            background-color: #f8fafc;
        }

        .notification-item.unread {
            background-color: rgba(26, 42, 108, 0.05);
        }

        .notification-time {
            font-size: 12px;
            color: #86909C;
            margin-bottom: 4px;
        }

        .notification-content {
            font-size: 14px;
            color: #333333;
        }

        /* ===== 用户头像样式 ===== */
        .user-avatar {
            font-size: 32px !important;
            cursor: pointer;
            transition: transform 0.3s;
            color: white;
            margin-left: 5px; /* 左侧间距 */
        }

        .user-avatar:hover {
            transform: scale(1.2);
        }

        /* ===== 用户详情样式 ===== */
        .user-details {
            display: flex;
            flex-direction: column;
            margin-left: 12px;
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
        }

        .user-id {
            font-size: 12px;
            color: rgba(255,255,255,0.7);
        }
    </style>
</head>
<body>
    <div class="container-demo">
        <!-- ===== 顶部导航栏 ===== -->
        <div class="nav-bar-1">
            <!-- ===== Logo区域 ===== -->
            <div class="logo-area">
                <div class="logo-image">
                    <i class="fa fa-bank"></i>
                </div>
                <div class="company-name">CF-作业系统</div>
            </div>
            
            <!-- ===== 系统消息区域 ===== -->
            <div class="system-messages">
                <div class="messages-container">
                    <div class="message-list">
                        <div class="message-item message-info">系统公告：系统运行正常</div>
                        <div class="message-item message-warning">警告：今日有3个待处理事项</div>
                        <div class="message-item message-info">提醒：系统将于今晚23:00进行维护</div>
                    </div>
                </div>
            </div>
            
            <!-- ===== 用户信息区域 ===== -->
            <div class="user-info">
                <div class="greeting-container">
                    <!-- 通知铃铛图标 -->
                    <div class="notification-bell" onclick="toggleNotificationPanel()">
                        <i class="fa fa-bell"></i>
                        <span class="notification-count" id="notificationCount">3</span>
                    </div>
                    
                    <!-- 动态问候语 -->
                    <span class="greeting-text" id="greetingText"></span>
                    
                    <!-- 用户头像 -->
                    <i class="fa fa-user-circle-o user-avatar" onclick="showProfile()"></i>
                </div>
                
                <!-- 用户详情 -->
                <div class="user-details">
                    <div class="user-name">张三</div>
                    <div class="user-id">工号：10086</div>
                </div>
            </div>
        </div>

        <!-- ===== 通知面板 ===== -->
        <div class="notification-panel" id="notificationPanel">
            <div class="notification-header">
                <div class="notification-title">系统通知</div>
                <div class="close-notification" onclick="toggleNotificationPanel()">
                    <i class="fa fa-times"></i>
                </div>
            </div>
            <div class="notification-list" id="notificationList">
                <div class="notification-item unread">
                    <div class="notification-time">刚刚</div>
                    <div class="notification-content">您有3个待处理的还款提醒</div>
                </div>
                <div class="notification-item unread">
                    <div class="notification-time">10分钟前</div>
                    <div class="notification-content">系统检测到异常登录尝试</div>
                </div>
                <div class="notification-item unread">
                    <div class="notification-time">今天 09:30</div>
                    <div class="notification-content">您的个人信息需要更新</div>
                </div>
                <div class="notification-item">
                    <div class="notification-time">昨天 16:45</div>
                    <div class="notification-content">本月业绩目标已完成80%</div>
                </div>
                <div class="notification-item">
                    <div class="notification-time">2025-05-27</div>
                    <div class="notification-content">系统更新已完成</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ===== 通知数据 =====
        // 存储所有通知信息，包括时间、内容和已读状态
        let notifications = [
            {id: 1, time: '刚刚', content: '您有3个待处理的还款提醒', read: false},
            {id: 2, time: '10分钟前', content: '系统检测到异常登录尝试', read: false},
            {id: 3, time: '今天 09:30', content: '您的个人信息需要更新', read: false},
            {id: 4, time: '昨天 16:45', content: '本月业绩目标已完成80%', read: true},
            {id: 5, time: '2025-05-27', content: '系统更新已完成', read: true}
        ];

        // ===== 通知面板切换功能 =====
        // 控制通知面板的显示和隐藏，以及处理未读通知状态
        function toggleNotificationPanel() {
            const panel = document.getElementById('notificationPanel');
            panel.classList.toggle('active');
            
            // 当面板打开时，将所有通知标记为已读
            if (panel.classList.contains('active')) {
                const unreadCount = notifications.filter(n => !n.read).length;
                if (unreadCount > 0) {
                    notifications.forEach(n => n.read = true);
                    document.getElementById('notificationCount').textContent = '0';
                    document.querySelectorAll('.notification-item.unread').forEach(item => {
                        item.classList.remove('unread');
                    });
                }
            }
        }

        // ===== 消息滚动初始化 =====
        // 控制系统消息区域的自动滚动效果
        function initMessageScroll() {
            const messageList = document.querySelector('.message-list');
            const items = messageList.children;
            
            // 初始位置设置
            messageList.style.transform = 'translateY(0)';
            let currentIndex = 0;
            
            // 每3秒切换一条消息
            setInterval(() => {
                currentIndex = (currentIndex + 1) % items.length;
                messageList.style.transform = `translateY(-${currentIndex * 32}px)`;
            }, 3000);
        }

        // ===== 更新问候语 =====
        // 根据当前时间自动更新问候语（如"上午好"、"下午好"等）
        function updateGreeting() {
            const hours = new Date().getHours();
            let greeting = '夜深了';
            if (hours < 6) greeting = '凌晨好';
            else if (hours < 9) greeting = '早上好';
            else if (hours < 12) greeting = '上午好';
            else if (hours < 14) greeting = '中午好';
            else if (hours < 18) greeting = '下午好';
            else if (hours < 22) greeting = '晚上好';
            document.getElementById('greetingText').textContent = greeting;
        }

        // ===== 显示个人信息 =====
        // 点击用户头像时触发的函数，在实际项目中会打开个人信息页面
        function showProfile() {
            alert("个人信息功能已触发（在实际系统中会打开个人信息页面）");
        }

        // ===== 页面初始化 =====
        // 页面加载完成后执行的初始化函数
        window.addEventListener('DOMContentLoaded', () => {
            updateGreeting();     // 更新问候语
            initMessageScroll();  // 初始化消息滚动
        });
    </script>
</body>
</html>
    