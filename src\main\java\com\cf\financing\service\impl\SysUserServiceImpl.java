package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.SysUser;
import com.cf.financing.mapper.SysUserMapper;
import com.cf.financing.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 系统用户服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public IPage<SysUser> getUserPage(
            Page<SysUser> page,
            String username,
            String realName,
            String phone,
            String email,
            String status,
            Long deptId,
            Long roleId) {
        
        return baseMapper.selectUserPage(
                page, username, realName, phone, email, status, deptId, roleId
        );
    }

    @Override
    public SysUser getUserByUsername(String username) {
        return baseMapper.selectUserByUsername(username);
    }

    @Override
    public SysUser getUserById(Long userId) {
        return baseMapper.selectUserById(userId);
    }

    @Override
    public List<SysUser> getUsersByDeptId(Long deptId) {
        return baseMapper.selectUsersByDeptId(deptId);
    }

    @Override
    public List<SysUser> getUsersByRoleId(Long roleId) {
        return baseMapper.selectUsersByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(SysUser user) {
        try {
            // 检查用户名是否存在
            if (checkUsernameExists(user.getUsername(), null)) {
                log.warn("用户名已存在: {}", user.getUsername());
                return false;
            }
            
            // 检查手机号是否存在
            if (StringUtils.hasText(user.getPhone()) && checkPhoneExists(user.getPhone(), null)) {
                log.warn("手机号已存在: {}", user.getPhone());
                return false;
            }
            
            // 检查邮箱是否存在
            if (StringUtils.hasText(user.getEmail()) && checkEmailExists(user.getEmail(), null)) {
                log.warn("邮箱已存在: {}", user.getEmail());
                return false;
            }
            
            // 加密密码
            if (StringUtils.hasText(user.getPassword())) {
                user.setPassword(encodePassword(user.getPassword()));
            } else {
                // 设置默认密码
                user.setPassword(encodePassword("123456"));
            }
            
            // 设置默认状态
            if (user.getStatus() == null) {
                user.setStatus(1); // 1表示启用状态
            }
            
            return save(user);
        } catch (Exception e) {
            log.error("创建用户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SysUser user) {
        try {
            // 检查用户名是否存在
            if (StringUtils.hasText(user.getUsername()) && 
                checkUsernameExists(user.getUsername(), user.getId())) {
                log.warn("用户名已存在: {}", user.getUsername());
                return false;
            }
            
            // 检查手机号是否存在
            if (StringUtils.hasText(user.getPhone()) && 
                checkPhoneExists(user.getPhone(), user.getId())) {
                log.warn("手机号已存在: {}", user.getPhone());
                return false;
            }
            
            // 检查邮箱是否存在
            if (StringUtils.hasText(user.getEmail()) && 
                checkEmailExists(user.getEmail(), user.getId())) {
                log.warn("邮箱已存在: {}", user.getEmail());
                return false;
            }
            
            // 如果更新密码，需要加密
            if (StringUtils.hasText(user.getPassword())) {
                user.setPassword(encodePassword(user.getPassword()));
            } else {
                // 不更新密码字段
                user.setPassword(null);
            }
            
            return updateById(user);
        } catch (Exception e) {
            log.error("更新用户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long userId) {
        try {
            return removeById(userId);
        } catch (Exception e) {
            log.error("删除用户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteUsers(List<Long> userIds) {
        try {
            return removeByIds(userIds);
        } catch (Exception e) {
            log.error("批量删除用户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public SysUser login(String username, String password, String loginIp) {
        try {
            // 根据用户名查询用户
            SysUser user = getUserByUsername(username);
            if (user == null) {
                log.warn("用户不存在: {}", username);
                return null;
            }
            
            // 检查用户状态 (1:启用, 0:禁用)
            if (user.getStatus() == null || user.getStatus() != 1) {
                log.warn("用户已被禁用: {}", username);
                return null;
            }
            
            // 验证密码
            if (!verifyPassword(password, user.getPassword())) {
                log.warn("密码错误: {}", username);
                return null;
            }
            
            // 更新登录信息
            updateLoginInfo(user.getId(), loginIp);
            
            // 清除密码信息
            user.setPassword(null);
            
            return user;
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLoginInfo(Long userId, String loginIp) {
        try {
            int result = baseMapper.updateLoginInfo(userId, loginIp);
            return result > 0;
        } catch (Exception e) {
            log.error("更新登录信息失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        try {
            // 查询用户信息
            SysUser user = getById(userId);
            if (user == null) {
                log.warn("用户不存在: {}", userId);
                return false;
            }
            
            // 验证旧密码
            if (!verifyPassword(oldPassword, user.getPassword())) {
                log.warn("旧密码错误: {}", userId);
                return false;
            }
            
            // 更新新密码
            return resetPassword(userId, newPassword);
        } catch (Exception e) {
            log.error("修改密码失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Long userId, String newPassword) {
        try {
            String encodedPassword = encodePassword(newPassword);
            int result = baseMapper.resetPassword(userId, encodedPassword);
            return result > 0;
        } catch (Exception e) {
            log.error("重置密码失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserStatus(Long userId, String status) {
        try {
            SysUser user = new SysUser();
            user.setId(userId);
            // 将字符串状态转换为Integer ("ACTIVE"->1, "INACTIVE"->0)
            Integer statusValue = "ACTIVE".equals(status) ? 1 : 0;
            user.setStatus(statusValue);
            return updateById(user);
        } catch (Exception e) {
            log.error("更新用户状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateUserStatus(List<Long> userIds, String status) {
        try {
            int result = baseMapper.batchUpdateStatus(userIds, status);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新用户状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getUserStatistics(Long deptId) {
        return baseMapper.selectUserStatistics(deptId);
    }

    @Override
    public boolean checkUsernameExists(String username, Long excludeUserId) {
        int count = baseMapper.checkUsernameExists(username, excludeUserId);
        return count > 0;
    }

    @Override
    public boolean checkPhoneExists(String phone, Long excludeUserId) {
        int count = baseMapper.checkPhoneExists(phone, excludeUserId);
        return count > 0;
    }

    @Override
    public boolean checkEmailExists(String email, Long excludeUserId) {
        int count = baseMapper.checkEmailExists(email, excludeUserId);
        return count > 0;
    }

    @Override
    public List<SysUser> getOnlineUsers() {
        return baseMapper.selectOnlineUsers();
    }

    @Override
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }

    @Override
    public String generateUserNo() {
        // 生成格式: USER + 年月日 + 4位序号
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 查询当日最大序号
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(SysUser::getUsername, "USER" + dateStr)
               .orderByDesc(SysUser::getUsername)
               .last("LIMIT 1");
        
        SysUser lastUser = getOne(wrapper);
        int sequence = 1;
        
        if (lastUser != null && lastUser.getUsername() != null) {
            String lastUsername = lastUser.getUsername();
            if (lastUsername.length() >= 16) {
                try {
                    sequence = Integer.parseInt(lastUsername.substring(12)) + 1;
                } catch (NumberFormatException e) {
                    log.warn("解析用户编号序号失败: {}", lastUsername);
                }
            }
        }
        
        return String.format("USER%s%04d", dateStr, sequence);
    }
}