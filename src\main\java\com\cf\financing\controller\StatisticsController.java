package com.cf.financing.controller;

import com.cf.financing.service.ICaseInfoService;
import com.cf.financing.service.IContactRecordService;
import com.cf.financing.service.ICustomerInfoService;
import com.cf.financing.service.IRepaymentRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统计数据控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/statistics")
@RequiredArgsConstructor
public class StatisticsController {

    private final ICaseInfoService caseInfoService;
    private final ICustomerInfoService customerInfoService;
    private final IContactRecordService contactRecordService;
    private final IRepaymentRecordService repaymentRecordService;

    /**
     * 获取首页统计数据
     */
        @GetMapping("/dashboard")
    public Map<String, Object> getDashboardStatistics() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 案件统计
            Map<String, Object> caseStats = caseInfoService.getCaseStatistics(null, null, null);
            
            // 客户统计
            Map<String, Object> customerStats = customerInfoService.getCustomerStatistics();
            
            // 今日联系记录统计
            Map<String, Object> todayContactStats = contactRecordService.getTodayContactStatistics();
            
            // 今日还款统计
            Map<String, Object> todayRepaymentStats = repaymentRecordService.getTodayRepaymentStatistics();
            
            // 风险等级分布
            List<Map<String, Object>> riskDistribution = customerInfoService.getRiskLevelDistribution();
            
            // 案件状态分布
            List<Map<String, Object>> caseStatusDistribution = caseInfoService.getCaseStatusDistribution();
            
            // 近7天案件趋势
            List<Map<String, Object>> caseTrend = caseInfoService.getDailyCaseStats(7, null);
            
            // 近7天还款趋势
            List<Map<String, Object>> repaymentTrend = repaymentRecordService.getDailyRepaymentStats(7);
            
            Map<String, Object> data = new HashMap<>();
            data.put("caseStats", caseStats);
            data.put("customerStats", customerStats);
            data.put("todayContactStats", todayContactStats);
            data.put("todayRepaymentStats", todayRepaymentStats);
            data.put("riskDistribution", riskDistribution);
            data.put("caseStatusDistribution", caseStatusDistribution);
            data.put("caseTrend", caseTrend);
            data.put("repaymentTrend", repaymentTrend);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取案件统计数据
     */
        @GetMapping("/cases")
    public Map<String, Object> getCaseStatistics(
            @RequestParam(required = false) Long assignUserId,
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = caseInfoService.getCaseStatistics(assignUserId, startDate, endDate);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取客户统计数据
     */
        @GetMapping("/customers")
    public Map<String, Object> getCustomerStatistics() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = customerInfoService.getCustomerStatistics();
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取还款统计数据
     */
        @GetMapping("/repayments")
    public Map<String, Object> getRepaymentStatistics(
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = repaymentRecordService.getRepaymentStatistics(startDate, endDate);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取联系记录统计数据
     */
        @GetMapping("/contacts")
    public Map<String, Object> getContactStatistics(
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate,
            @RequestParam(required = false) Long operatorId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = contactRecordService.getContactStatistics(startDate, endDate, operatorId);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取风险等级分布
     */
        @GetMapping("/risk-distribution")
    public Map<String, Object> getRiskDistribution() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> distribution = customerInfoService.getRiskLevelDistribution();
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", distribution);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取信用等级分布
     */
        @GetMapping("/credit-distribution")
    public Map<String, Object> getCreditDistribution() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> distribution = customerInfoService.getCreditLevelDistribution();
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", distribution);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取案件状态分布
     */
        @GetMapping("/case-status-distribution")
    public Map<String, Object> getCaseStatusDistribution() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> distribution = caseInfoService.getCaseStatusDistribution();
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", distribution);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取月度趋势数据
     */
        @GetMapping("/monthly-trend")
    public Map<String, Object> getMonthlyTrend(
            @RequestParam(defaultValue = "12") Integer months) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 客户新增趋势
            List<Map<String, Object>> customerTrend = customerInfoService.getMonthlyNewCustomerTrend(months);
            
            // 案件新增趋势
            List<Map<String, Object>> caseTrend = caseInfoService.getMonthlyCaseStats(months, null);
            
            // 还款趋势
            List<Map<String, Object>> repaymentTrend = repaymentRecordService.getMonthlyRepaymentStats(months);
            
            Map<String, Object> data = new HashMap<>();
            data.put("customerTrend", customerTrend);
            data.put("caseTrend", caseTrend);
            data.put("repaymentTrend", repaymentTrend);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取绩效统计数据
     */
        @GetMapping("/performance")
    public Map<String, Object> getPerformanceStatistics(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 案件处理绩效
            Map<String, Object> casePerformance = caseInfoService.getCaseStatistics(userId, startDate, endDate);
            
            // 联系记录绩效
            Map<String, Object> contactPerformance = contactRecordService.getContactStatistics(startDate, endDate, userId);
            
            // 还款回收绩效
            Map<String, Object> repaymentPerformance = repaymentRecordService.getRepaymentStatistics(startDate, endDate);
            
            Map<String, Object> data = new HashMap<>();
            data.put("casePerformance", casePerformance);
            data.put("contactPerformance", contactPerformance);
            data.put("repaymentPerformance", repaymentPerformance);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }
}