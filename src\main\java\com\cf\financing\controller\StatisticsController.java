package com.cf.financing.controller;

import com.cf.financing.service.ICaseInfoService;
import com.cf.financing.service.IContactRecordService;
import com.cf.financing.service.ICustomerInfoService;
import com.cf.financing.service.IRepaymentRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统计数据控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/statistics")
@RequiredArgsConstructor
public class StatisticsController {

    private final ICaseInfoService caseInfoService;
    private final ICustomerInfoService customerInfoService;
    private final IContactRecordService contactRecordService;
    private final IRepaymentRecordService repaymentRecordService;

    /**
     * 获取首页统计数据
     */
    @GetMapping("/dashboard")
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 模拟仪表板数据
            Map<String, Object> data = new HashMap<>();
            data.put("totalCases", 2587);
            data.put("totalAmount", 8740000);
            data.put("todayRecovery", 356800);
            data.put("avgRecoveryRate", 74.6);

            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取案件统计数据
     */
        @GetMapping("/cases")
    public Map<String, Object> getCaseStatistics(
            @RequestParam(required = false) Long assignUserId,
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = caseInfoService.getCaseStatistics(assignUserId, startDate, endDate);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取客户统计数据
     */
        @GetMapping("/customers")
    public Map<String, Object> getCustomerStatistics() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = customerInfoService.getCustomerStatistics();
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取还款统计数据
     */
        @GetMapping("/repayments")
    public Map<String, Object> getRepaymentStatistics(
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = repaymentRecordService.getRepaymentStatistics(startDate, endDate);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取联系记录统计数据
     */
        @GetMapping("/contacts")
    public Map<String, Object> getContactStatistics(
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate,
            @RequestParam(required = false) Long operatorId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = contactRecordService.getContactStatistics(startDate, endDate, operatorId);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取风险等级分布
     */
        @GetMapping("/risk-distribution")
    public Map<String, Object> getRiskDistribution() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> distribution = customerInfoService.getRiskLevelDistribution();
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", distribution);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取信用等级分布
     */
        @GetMapping("/credit-distribution")
    public Map<String, Object> getCreditDistribution() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> distribution = customerInfoService.getCreditLevelDistribution();
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", distribution);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取案件状态分布
     */
        @GetMapping("/case-status-distribution")
    public Map<String, Object> getCaseStatusDistribution() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> distribution = caseInfoService.getCaseStatusDistribution();
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", distribution);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取月度趋势数据
     */
        @GetMapping("/monthly-trend")
    public Map<String, Object> getMonthlyTrend(
            @RequestParam(defaultValue = "12") Integer months) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 客户新增趋势
            List<Map<String, Object>> customerTrend = customerInfoService.getMonthlyNewCustomerTrend(months);
            
            // 案件新增趋势
            List<Map<String, Object>> caseTrend = caseInfoService.getMonthlyCaseStats(months, null);
            
            // 还款趋势
            List<Map<String, Object>> repaymentTrend = repaymentRecordService.getMonthlyRepaymentStats(months);
            
            Map<String, Object> data = new HashMap<>();
            data.put("customerTrend", customerTrend);
            data.put("caseTrend", caseTrend);
            data.put("repaymentTrend", repaymentTrend);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取绩效统计数据
     */
        @GetMapping("/performance")
    public Map<String, Object> getPerformanceStatistics(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 案件处理绩效
            Map<String, Object> casePerformance = caseInfoService.getCaseStatistics(userId, startDate, endDate);
            
            // 联系记录绩效
            Map<String, Object> contactPerformance = contactRecordService.getContactStatistics(startDate, endDate, userId);
            
            // 还款回收绩效
            Map<String, Object> repaymentPerformance = repaymentRecordService.getRepaymentStatistics(startDate, endDate);
            
            Map<String, Object> data = new HashMap<>();
            data.put("casePerformance", casePerformance);
            data.put("contactPerformance", contactPerformance);
            data.put("repaymentPerformance", repaymentPerformance);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取排行榜数据
     */
    @GetMapping("/ranking")
    public Map<String, Object> getRanking(
            @RequestParam(defaultValue = "personal") String type,
            @RequestParam(defaultValue = "today") String period) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 模拟排行榜数据
            List<Map<String, Object>> rankings = new ArrayList<>();

            Map<String, Object> rank1 = new HashMap<>();
            rank1.put("rank", 1);
            rank1.put("name", "张某某");
            rank1.put("team", "催收一组");
            rank1.put("amount", 68500);
            rank1.put("rate", 92);
            rankings.add(rank1);

            Map<String, Object> rank2 = new HashMap<>();
            rank2.put("rank", 2);
            rank2.put("name", "李某某");
            rank2.put("team", "催收二组");
            rank2.put("amount", 52300);
            rank2.put("rate", 85);
            rankings.add(rank2);

            Map<String, Object> rank3 = new HashMap<>();
            rank3.put("rank", 3);
            rank3.put("name", "王某某");
            rank3.put("team", "催收三组");
            rank3.put("amount", 48800);
            rank3.put("rate", 78);
            rankings.add(rank3);

            Map<String, Object> rank4 = new HashMap<>();
            rank4.put("rank", 4);
            rank4.put("name", "陈某某");
            rank4.put("team", "催收一组");
            rank4.put("amount", 42100);
            rank4.put("rate", 72);
            rankings.add(rank4);

            Map<String, Object> rank5 = new HashMap<>();
            rank5.put("rank", 5);
            rank5.put("name", "刘某某");
            rank5.put("team", "催收二组");
            rank5.put("amount", 38900);
            rank5.put("rate", 68);
            rankings.add(rank5);

            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", rankings);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取案件分布数据
     */
    @GetMapping("/case-distribution")
    public Map<String, Object> getCaseDistribution(
            @RequestParam(defaultValue = "department") String type) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 模拟案件分布数据
            List<Map<String, Object>> distributions = new ArrayList<>();

            Map<String, Object> dept1 = new HashMap<>();
            dept1.put("name", "催收一组");
            dept1.put("cases", 856);
            dept1.put("amount", 1240000);
            dept1.put("progress", 73);
            distributions.add(dept1);

            Map<String, Object> dept2 = new HashMap<>();
            dept2.put("name", "催收二组");
            dept2.put("cases", 732);
            dept2.put("amount", 1078000);
            dept2.put("progress", 68);
            distributions.add(dept2);

            Map<String, Object> dept3 = new HashMap<>();
            dept3.put("name", "催收三组");
            dept3.put("cases", 654);
            dept3.put("amount", 896500);
            dept3.put("progress", 62);
            distributions.add(dept3);

            Map<String, Object> dept4 = new HashMap<>();
            dept4.put("name", "催收四组");
            dept4.put("cases", 512);
            dept4.put("amount", 745200);
            dept4.put("progress", 58);
            distributions.add(dept4);

            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", distributions);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "查询失败: " + e.getMessage());
        }

        return response;
    }

}