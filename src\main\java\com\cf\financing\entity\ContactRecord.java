package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 联系记录实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("contact_record")
public class ContactRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 案件ID
     */
    @TableField("case_id")
    private Long caseId;

    /**
     * 联系方式(PHONE:电话,SMS:短信,EMAIL:邮件,VISIT:上门)
     */
    @TableField("contact_type")
    private String contactType;

    /**
     * 联系时间
     */
    @TableField("contact_time")
    private LocalDateTime contactTime;

    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 联系结果
     */
    @TableField("contact_result")
    private String contactResult;

    /**
     * 联系内容
     */
    @TableField("contact_content")
    private String contactContent;

    /**
     * 承诺还款金额
     */
    @TableField("promise_amount")
    private BigDecimal promiseAmount;

    /**
     * 承诺还款日期
     */
    @TableField("promise_date")
    private LocalDate promiseDate;

    /**
     * 操作员ID
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    // 非数据库字段
    /**
     * 案件编号
     */
    @TableField(exist = false)
    private String caseNo;

    /**
     * 客户姓名
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 操作员姓名
     */
    @TableField(exist = false)
    private String operatorName;
}