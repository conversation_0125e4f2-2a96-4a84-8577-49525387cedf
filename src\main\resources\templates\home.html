<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #0c2461 0%, #1e3799 100%);
            color: #f0f2f5;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
            font-size: 14px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(90deg, #f6b93b, #fad390);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-title i {
            font-size: 26px;
            color: #f6b93b;
            background: rgba(12, 36, 97, 0.7);
            padding: 10px;
            border-radius: 50%;
        }

        .date-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        /* 顶部数据概览 */
        .data-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .overview-card {
            padding: 16px;
            border-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(6px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: 110px;
        }

        .overview-card.primary {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(99, 102, 241, 0.25));
        }

        .overview-card.warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.25), rgba(249, 115, 22, 0.25));
        }

        .overview-card.success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.25), rgba(52, 211, 153, 0.25));
        }

        .overview-card.info {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.25), rgba(14, 165, 233, 0.25));
        }

        .metric {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .metric .label {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.85);
        }

        .metric .value {
            font-size: 20px;
            font-weight: 700;
        }

        .metric .trend {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            width: fit-content;
        }

        .trend.up {
            background: rgba(39, 174, 96, 0.25);
            color: #27ae60;
        }

        .trend.down {
            background: rgba(231, 76, 60, 0.25);
            color: #e74c3c;
        }

        .icon i {
            font-size: 28px;
            opacity: 0.7;
        }

        /* 主体内容区 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        /* 面板通用样式 */
        .panel {
            background: rgba(20, 40, 100, 0.45);
            border-radius: 14px;
            padding: 16px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(6px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            height: 300px;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #f6b93b;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border-radius: 8px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn.active {
            background: #f6b93b;
            color: #0c2461;
        }

        select {
            padding: 6px 12px;
            border-radius: 8px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
        }

        /* 面板内容区域 */
        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 3px;
        }

        /* 滚动条样式 */
        .panel-content::-webkit-scrollbar {
            width: 8px;
        }

        .panel-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        .panel-content::-webkit-scrollbar-thumb {
            background: rgba(246, 185, 59, 0.6);
            border-radius: 4px;
        }

        .panel-content::-webkit-scrollbar-thumb:hover {
            background: rgba(246, 185, 59, 0.8);
        }

        /* 快速操作按钮 */
        .quick-actions-top {
            display: flex;
            gap: 10px;
            margin-left: auto;
            margin-right: 15px;
        }

        .action-btn-top {
            padding: 8px 15px;
            background: rgba(246, 185, 59, 0.2);
            border: 1px solid rgba(246, 185, 59, 0.4);
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            color: #f6b93b;
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .action-btn-top:hover {
            background: rgba(246, 185, 59, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(246, 185, 59, 0.2);
        }

        .action-btn-top i {
            font-size: 14px;
        }

        /* 排行榜样式 */
        .ranking-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .top-performer, .performer {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.2s ease;
        }

        .top-performer {
            background: rgba(246, 185, 59, 0.15);
            border-left: 4px solid #f6b93b;
        }

        .top-performer:hover, .performer:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .rank {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            background: rgba(12, 36, 97, 0.7);
            color: #f6b93b;
            margin-right: 12px;
        }

        .top-performer .rank {
            background: #f6b93b;
            color: #0c2461;
        }

        .user-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        .details {
            line-height: 1.3;
        }

        .name {
            font-weight: 600;
            font-size: 15px;
        }

        .team {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .performance-data {
            min-width: 120px;
            text-align: right;
        }

        .amount {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 15px;
        }

        .progress-container {
            position: relative;
            height: 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #34d399);
            border-radius: 8px;
            transition: width 0.5s ease;
        }

        .progress-container span {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 11px;
            color: white;
            z-index: 1;
        }

        /* 图表容器 */
        .chart-container {
            height: 100%;
            position: relative;
        }

        /* 案件分布表格样式 */
        .case-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            margin-top: -10px;
        }

        .case-table th {
            text-align: left;
            padding: 12px;
            background-color: #0c2461;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: -10px;
            z-index: 20;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .case-table td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.9);
        }

        .case-table tr:last-child td {
            border-bottom: none;
        }

        .case-table .highlight {
            color: #f6b93b;
            font-weight: 600;
        }

        .case-table .progress-cell {
            width: 40%;
        }

        .case-table .progress-bar {
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            margin-top: 5px;
        }

        .case-table .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #6366f1);
            border-radius: 5px;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="header-title">
                <i class="fas fa-home"></i>
                <h1>首页</h1>
            </div>
            
            <div class="quick-actions-top">
                <button class="action-btn-top">
                    <i class="fas fa-file-export"></i>
                    <span>导出报表</span>
                </button>
                <button class="action-btn-top">
                    <i class="fas fa-bell"></i>
                    <span>设置提醒</span>
                </button>
                <button class="action-btn-top">
                    <i class="fas fa-cog"></i>
                    <span>设置</span>
                </button>
                <button class="action-btn-top">
                    <i class="fas fa-sync-alt"></i>
                    <span>刷新</span>
                </button>
            </div>
            
            <div class="date-info" id="currentDateTime">
                <i class="fas fa-calendar-alt"></i> 加载中...
            </div>
        </div>

        <!-- 顶部数据概览 -->
        <div class="data-overview">
            <div class="overview-card warning">
                <div class="metric">
                    <div class="label">总在案户数</div>
                    <div class="value" id="totalCases">1,234</div>
                    <div class="trend up">
                        <i class="fas fa-arrow-up"></i>
                        <span>+5.2%</span>
                    </div>
                </div>
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>

            <div class="overview-card primary">
                <div class="metric">
                    <div class="label">总在案金额</div>
                    <div class="value" id="totalAmount">¥8.56M</div>
                    <div class="trend up">
                        <i class="fas fa-arrow-up"></i>
                        <span>+12.8%</span>
                    </div>
                </div>
                <div class="icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>

            <div class="overview-card success">
                <div class="metric">
                    <div class="label">今日回收</div>
                    <div class="value" id="todayRecovery">¥156K</div>
                    <div class="trend up">
                        <i class="fas fa-arrow-up"></i>
                        <span>+8.4%</span>
                    </div>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>

            <div class="overview-card info">
                <div class="metric">
                    <div class="label">平均回收率</div>
                    <div class="value" id="avgRecoveryRate">68.5%</div>
                    <div class="trend down">
                        <i class="fas fa-arrow-down"></i>
                        <span>-2.1%</span>
                    </div>
                </div>
                <div class="icon">
                    <i class="fas fa-percentage"></i>
                </div>
            </div>
        </div>

        <!-- 主体内容区 -->
        <div class="main-content">
            <!-- 左侧内容 -->
            <div class="left-column">
                <!-- 业绩排行榜 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title"><i class="fas fa-trophy"></i> 榜上有名</h3>
                        <div class="panel-actions">
                            <button class="btn active">个人</button>
                            <button class="btn">团队</button>
                            <select>
                                <option>今日</option>
                                <option>本周</option>
                            </select>
                        </div>
                    </div>

                    <div class="panel-content">
                        <div class="ranking-list">
                            <div class="top-performer">
                                <div class="rank">1</div>
                                <div class="user-info">
                                    <div class="avatar">张</div>
                                    <div class="details">
                                        <div class="name">张某某</div>
                                        <div class="team">催收一组</div>
                                    </div>
                                </div>
                                <div class="performance-data">
                                    <div class="amount">¥68.5K</div>
                                    <div class="progress-container">
                                        <div class="progress" style="width: 92%"></div>
                                        <span>92%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="performer">
                                <div class="rank">2</div>
                                <div class="user-info">
                                    <div class="avatar">李</div>
                                    <div class="details">
                                        <div class="name">李某某</div>
                                        <div class="team">催收二组</div>
                                    </div>
                                </div>
                                <div class="performance-data">
                                    <div class="amount">¥52.3K</div>
                                    <div class="progress-container">
                                        <div class="progress" style="width: 85%"></div>
                                        <span>85%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="performer">
                                <div class="rank">3</div>
                                <div class="user-info">
                                    <div class="avatar">王</div>
                                    <div class="details">
                                        <div class="name">王某某</div>
                                        <div class="team">催收三组</div>
                                    </div>
                                </div>
                                <div class="performance-data">
                                    <div class="amount">¥48.8K</div>
                                    <div class="progress-container">
                                        <div class="progress" style="width: 78%"></div>
                                        <span>78%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="performer">
                                <div class="rank">4</div>
                                <div class="user-info">
                                    <div class="avatar">陈</div>
                                    <div class="details">
                                        <div class="name">陈某某</div>
                                        <div class="team">催收一组</div>
                                    </div>
                                </div>
                                <div class="performance-data">
                                    <div class="amount">¥42.1K</div>
                                    <div class="progress-container">
                                        <div class="progress" style="width: 72%"></div>
                                        <span>72%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="performer">
                                <div class="rank">5</div>
                                <div class="user-info">
                                    <div class="avatar">刘</div>
                                    <div class="details">
                                        <div class="name">刘某某</div>
                                        <div class="team">催收二组</div>
                                    </div>
                                </div>
                                <div class="performance-data">
                                    <div class="amount">¥38.9K</div>
                                    <div class="progress-container">
                                        <div class="progress" style="width: 68%"></div>
                                        <span>68%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 回收趋势 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title"><i class="fas fa-chart-line"></i> 回收趋势</h3>
                        <div class="panel-actions">
                            <button class="btn active">金额</button>
                            <button class="btn">户数</button>
                            <select>
                                <option>近7天</option>
                                <option>近30天</option>
                            </select>
                        </div>
                    </div>
                    <div class="panel-content">
                        <div class="chart-container">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="right-column">
                <!-- 团队绩效 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title"><i class="fas fa-users"></i> 团队绩效</h3>
                        <div class="panel-actions">
                            <select>
                                <option>本月</option>
                                <option>本季度</option>
                            </select>
                        </div>
                    </div>
                    <div class="panel-content">
                        <div class="chart-container">
                            <canvas id="teamChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 案件分布 -->
                <div class="panel">
                    <div class="panel-header">
                        <h3 class="panel-title"><i class="fas fa-boxes"></i> 案件分布</h3>
                        <div class="panel-actions">
                            <button class="btn active">部门</button>
                            <button class="btn">个人</button>
                        </div>
                    </div>
                    <div class="panel-content">
                        <table class="case-table">
                            <thead>
                                <tr>
                                    <th>团队/个人</th>
                                    <th>在案户数</th>
                                    <th>委托金额</th>
                                    <th>完成进度</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="highlight">催收一组</td>
                                    <td>856</td>
                                    <td>¥1,240,000</td>
                                    <td class="progress-cell">
                                        73%
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 73%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="highlight">催收二组</td>
                                    <td>732</td>
                                    <td>¥1,078,000</td>
                                    <td class="progress-cell">
                                        68%
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 68%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="highlight">催收三组</td>
                                    <td>654</td>
                                    <td>¥896,500</td>
                                    <td class="progress-cell">
                                        62%
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 62%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="highlight">催收四组</td>
                                    <td>512</td>
                                    <td>¥745,200</td>
                                    <td class="progress-cell">
                                        58%
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 58%"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新当前时间
        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            const dateStr = now.toLocaleDateString('zh-CN', options);
            document.getElementById('currentDateTime').innerHTML =
                `<i class="fas fa-calendar-alt"></i> ${dateStr}`;
        }

        // 回收趋势折线图
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        const trendChart = new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['12/08', '12/09', '12/10', '12/11', '12/12', '12/13', '12/14'],
                datasets: [{
                    label: '回收金额 (万元)',
                    data: [32, 45, 28, 51, 42, 39, 48],
                    borderColor: '#f6b93b',
                    backgroundColor: 'rgba(246, 185, 59, 0.1)',
                    borderWidth: 3,
                    pointBackgroundColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });

        // 团队绩效对比图
        const teamCtx = document.getElementById('teamChart').getContext('2d');
        const teamChart = new Chart(teamCtx, {
            type: 'bar',
            data: {
                labels: ['催收一组', '催收二组', '催收三组', '催收四组'],
                datasets: [{
                    label: '回收金额 (万元)',
                    data: [186, 152, 124, 98],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.7)',
                        'rgba(16, 185, 129, 0.7)',
                        'rgba(245, 158, 11, 0.7)',
                        'rgba(156, 163, 175, 0.7)'
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(156, 163, 175, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            font: {
                                size: 10
                            }
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });

        // 进度条动画效果
        document.addEventListener('DOMContentLoaded', function() {
            updateDateTime();

            const progressBars = document.querySelectorAll('.progress, .progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>
