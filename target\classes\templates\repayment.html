<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>还款管理 - CF金融催收管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #0c2461 0%, #1e3799 100%);
            color: #f0f2f5;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(90deg, #f6b93b, #fad390);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-title i {
            color: #f6b93b;
            font-size: 32px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981, #047857);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-title {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .stat-icon.primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }

        .stat-icon.success {
            background: linear-gradient(135deg, #10b981, #047857);
        }

        .stat-icon.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .stat-icon.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #f6b93b;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        /* 筛选区域 */
        .filter-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .filter-title {
            font-size: 16px;
            font-weight: 600;
            color: #f6b93b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-label {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }

        .filter-input:focus {
            outline: none;
            border-color: #f6b93b;
            box-shadow: 0 0 0 2px rgba(246, 185, 59, 0.2);
        }

        .filter-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        /* 数据表格 */
        .table-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        .table-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #f6b93b;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: rgba(12, 36, 97, 0.8);
            color: rgba(255, 255, 255, 0.9);
            padding: 12px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.9);
        }

        .data-table tr:hover td {
            background: rgba(255, 255, 255, 0.05);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            min-width: 60px;
            display: inline-block;
        }

        .status-success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .status-failed {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .amount {
            font-weight: 600;
            color: #f6b93b;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 0 2px;
            transition: all 0.2s ease;
        }

        .action-btn.view {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .action-btn.edit {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .action-btn.delete {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        /* 分页 */
        .pagination {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .pagination-info {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background: rgba(246, 185, 59, 0.2);
            border-color: #f6b93b;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #f6b93b;
            color: #0c2461;
            border-color: #f6b93b;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                width: 100%;
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filter-grid {
                grid-template-columns: 1fr;
            }

            .table-header {
                flex-direction: column;
                gap: 15px;
            }

            .table-actions {
                width: 100%;
                justify-content: center;
            }

            .pagination {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-money-bill-wave"></i>
                还款管理
            </h1>
            <div class="header-actions">
                <a href="/repayment/new" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新增还款
                </a>
                <button class="btn btn-success" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    导出数据
                </button>
                <button class="btn btn-warning" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">今日还款总额</div>
                    <div class="stat-icon success">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
                <div class="stat-value" id="todayAmount">¥0</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12.5% 较昨日</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">今日还款笔数</div>
                    <div class="stat-icon primary">
                        <i class="fas fa-list-ol"></i>
                    </div>
                </div>
                <div class="stat-value" id="todayCount">0</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8.3% 较昨日</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">本月还款总额</div>
                    <div class="stat-icon warning">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="stat-value" id="monthAmount">¥0</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+15.2% 较上月</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">待确认还款</div>
                    <div class="stat-icon danger">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value" id="pendingCount">0</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span>-3.1% 较昨日</span>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                筛选条件
            </div>
            <div class="filter-grid">
                <div class="filter-group">
                    <label class="filter-label">还款编号</label>
                    <input type="text" class="filter-input" id="repaymentNo" placeholder="请输入还款编号">
                </div>
                <div class="filter-group">
                    <label class="filter-label">案件编号</label>
                    <input type="text" class="filter-input" id="caseNo" placeholder="请输入案件编号">
                </div>
                <div class="filter-group">
                    <label class="filter-label">客户姓名</label>
                    <input type="text" class="filter-input" id="customerName" placeholder="请输入客户姓名">
                </div>
                <div class="filter-group">
                    <label class="filter-label">还款状态</label>
                    <select class="filter-input" id="status">
                        <option value="">全部状态</option>
                        <option value="success">已确认</option>
                        <option value="pending">待确认</option>
                        <option value="failed">已拒绝</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">还款日期</label>
                    <input type="date" class="filter-input" id="repaymentDate">
                </div>
                <div class="filter-group">
                    <label class="filter-label">金额范围</label>
                    <input type="number" class="filter-input" id="amountRange" placeholder="最小金额-最大金额">
                </div>
            </div>
            <div class="filter-actions">
                <button class="btn btn-primary" onclick="searchRepayments()">
                    <i class="fas fa-search"></i>
                    查询
                </button>
                <button class="btn" onclick="resetFilters()" style="background: rgba(255,255,255,0.1); color: white;">
                    <i class="fas fa-undo"></i>
                    重置
                </button>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-section">
            <div class="table-header">
                <div class="table-title">
                    <i class="fas fa-table"></i>
                    还款记录列表
                </div>
                <div class="table-actions">
                    <button class="btn" onclick="batchConfirm()" style="background: rgba(16,185,129,0.2); color: #10b981;">
                        <i class="fas fa-check"></i>
                        批量确认
                    </button>
                    <button class="btn" onclick="batchReject()" style="background: rgba(239,68,68,0.2); color: #ef4444;">
                        <i class="fas fa-times"></i>
                        批量拒绝
                    </button>
                </div>
            </div>

            <div class="table-container">
                <table class="data-table" id="repaymentTable">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>还款编号</th>
                            <th>案件编号</th>
                            <th>客户姓名</th>
                            <th>还款金额</th>
                            <th>还款方式</th>
                            <th>还款时间</th>
                            <th>交易流水号</th>
                            <th>状态</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="repaymentTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 <span id="startRecord">1</span> 到 <span id="endRecord">20</span> 条，
                    共 <span id="totalRecords">0</span> 条记录
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" id="prevPage" onclick="prevPage()" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="pageNumbers"></span>
                    <button class="pagination-btn" id="nextPage" onclick="nextPage()">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/repayment.js}"></script>
</body>
</html>
