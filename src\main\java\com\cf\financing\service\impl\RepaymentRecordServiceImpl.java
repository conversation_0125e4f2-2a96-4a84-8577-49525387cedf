package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.RepaymentRecord;
import com.cf.financing.mapper.RepaymentRecordMapper;
import com.cf.financing.service.IRepaymentRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 还款记录服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class RepaymentRecordServiceImpl extends ServiceImpl<RepaymentRecordMapper, RepaymentRecord> implements IRepaymentRecordService {

    @Override
    @Transactional(readOnly = true)
    public IPage<RepaymentRecord> getRepaymentPage(
            Page<RepaymentRecord> page,
            Long caseId,
            Long customerId,
            String repaymentNo,
            String repaymentType,
            String status,
            LocalDate startDate,
            LocalDate endDate,
            Long operatorId) {
        
        if (page == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }
        
        return baseMapper.selectRepaymentPage(
                page, 
                repaymentNo, 
                caseId, 
                customerId != null ? customerId.toString() : null, // customerName参数
                repaymentType, 
                status, 
                operatorId, 
                startDate, 
                endDate, 
                null,
                null
        );
    }

    @Override
    @Transactional(readOnly = true)
    public List<RepaymentRecord> getRepaymentsByCaseId(Long caseId) {
        if (caseId == null) {
            throw new IllegalArgumentException("案件ID不能为空");
        }
        return baseMapper.selectRepaymentsByCaseId(caseId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RepaymentRecord> getRepaymentsByCustomerId(Long customerId) {
        if (customerId == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }
        return baseMapper.selectRepaymentsByCustomerId(customerId);
    }

    @Override
    @Transactional(readOnly = true)
    public RepaymentRecord getRepaymentByNo(String repaymentNo) {
        if (!StringUtils.hasText(repaymentNo)) {
            throw new IllegalArgumentException("还款编号不能为空");
        }
        return baseMapper.selectRepaymentByNo(repaymentNo);
    }

    @Override
    @Transactional(readOnly = true) 
    public RepaymentRecord getRepaymentByTransactionNo(String transactionNo) {
        if (!StringUtils.hasText(transactionNo)) {
            throw new IllegalArgumentException("交易流水号不能为空");
        }
        return baseMapper.selectRepaymentByTransactionNo(transactionNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRepayment(RepaymentRecord repayment) {
        try {
            // 检查还款编号是否存在
            if (StringUtils.hasText(repayment.getRepaymentNo()) && 
                checkRepaymentNoExists(repayment.getRepaymentNo(), null)) {
                log.warn("还款编号已存在: {}", repayment.getRepaymentNo());
                return false;
            }
            
            // 检查交易流水号是否存在
            if (StringUtils.hasText(repayment.getTransactionNo()) && 
                checkTransactionNoExists(repayment.getTransactionNo(), null)) {
                log.warn("交易流水号已存在: {}", repayment.getTransactionNo());
                return false;
            }
            
            // 如果没有还款编号，自动生成
            if (!StringUtils.hasText(repayment.getRepaymentNo())) {
                repayment.setRepaymentNo(generateRepaymentNo());
            }
            
            // 设置默认状态
            if (!StringUtils.hasText(repayment.getRepaymentStatus())) {
                repayment.setRepaymentStatus("PENDING");
            }
            
            return save(repayment);
        } catch (Exception e) {
            log.error("创建还款记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRepayment(RepaymentRecord repayment) {
        try {
            // 检查还款编号是否存在
            if (StringUtils.hasText(repayment.getRepaymentNo()) && 
                checkRepaymentNoExists(repayment.getRepaymentNo(), repayment.getId())) {
                log.warn("还款编号已存在: {}", repayment.getRepaymentNo());
                return false;
            }
            
            // 检查交易流水号是否存在
            if (StringUtils.hasText(repayment.getTransactionNo()) && 
                checkTransactionNoExists(repayment.getTransactionNo(), repayment.getId())) {
                log.warn("交易流水号已存在: {}", repayment.getTransactionNo());
                return false;
            }
            
            return updateById(repayment);
        } catch (Exception e) {
            log.error("更新还款记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRepayment(Long repaymentId) {
        try {
            return removeById(repaymentId);
        } catch (Exception e) {
            log.error("删除还款记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteRepayments(List<Long> repaymentIds) {
        try {
            return removeByIds(repaymentIds);
        } catch (Exception e) {
            log.error("批量删除还款记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public BigDecimal getTotalRepaymentByCaseId(Long caseId) {
        BigDecimal total = baseMapper.selectTotalRepaymentByCaseId(caseId);
        return total != null ? total : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getTotalRepaymentByCustomerId(Long customerId) {
        BigDecimal total = baseMapper.selectTotalRepaymentByCustomerId(customerId);
        return total != null ? total : BigDecimal.ZERO;
    }

    @Override
    public Map<String, Object> getRepaymentStatistics(LocalDate startDate, LocalDate endDate) {
        return baseMapper.selectRepaymentStatistics(null, startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getRepaymentTrend(Integer days) {
        return baseMapper.selectRepaymentTrendData(null, days);
    }

    @Override
    public List<Map<String, Object>> getRepaymentTypeStatistics(LocalDate startDate, LocalDate endDate) {
        return baseMapper.selectRepaymentTypeStats(startDate, endDate);
    }

    @Override
    public List<RepaymentRecord> getTodayRepayments() {
        return baseMapper.selectTodayRepayments(null, LocalDate.now());
    }

    @Override
    public List<RepaymentRecord> getPendingRepayments() {
        return baseMapper.selectPendingRepayments(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> repaymentIds, String status, Long operatorId) {
        try {
            int result = baseMapper.batchUpdateRepaymentStatus(repaymentIds, status, operatorId);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新还款状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkRepaymentNoExists(String repaymentNo, Long excludeRepaymentId) {
        int count = baseMapper.checkRepaymentNoExists(repaymentNo, excludeRepaymentId);
        return count > 0;
    }

    @Override
    public boolean checkTransactionNoExists(String transactionNo, Long excludeRepaymentId) {
        int count = baseMapper.checkTransactionNoExists(transactionNo, excludeRepaymentId);
        return count > 0;
    }

    @Override
    public List<Map<String, Object>> getMonthlyRepaymentStatistics(Integer months) {
        return baseMapper.selectMonthlyRepaymentStats(months);
    }

    @Override
    public List<Map<String, Object>> getOperatorRepaymentRanking(
            LocalDate startDate, LocalDate endDate, Integer limit) {
        return baseMapper.selectOperatorRepaymentRanking(startDate, endDate, limit);
    }

    @Override
    public String generateRepaymentNo() {
        // 生成格式: REP + 年月日 + 4位序号
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 查询当日最大序号
        LambdaQueryWrapper<RepaymentRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(RepaymentRecord::getRepaymentNo, "REP" + dateStr)
               .orderByDesc(RepaymentRecord::getRepaymentNo)
               .last("LIMIT 1");
        
        RepaymentRecord lastRepayment = getOne(wrapper);
        int sequence = 1;
        
        if (lastRepayment != null && lastRepayment.getRepaymentNo() != null) {
            String lastRepaymentNo = lastRepayment.getRepaymentNo();
            if (lastRepaymentNo.length() >= 15) {
                try {
                    sequence = Integer.parseInt(lastRepaymentNo.substring(11)) + 1;
                } catch (NumberFormatException e) {
                    log.warn("解析还款编号序号失败: {}", lastRepaymentNo);
                }
            }
        }
        
        return String.format("REP%s%04d", dateStr, sequence);
    }

    @Override
    public Map<String, Object> getTodayRepaymentStatistics() {
        return baseMapper.selectTodayRepaymentStatistics();
    }

    @Override
    public List<Map<String, Object>> getDailyRepaymentStats(Integer days) {
        return baseMapper.selectDailyRepaymentStats(days);
    }

    @Override
    public List<Map<String, Object>> getMonthlyRepaymentStats(Integer months) {
        return baseMapper.selectMonthlyRepaymentStats(months);
    }
}