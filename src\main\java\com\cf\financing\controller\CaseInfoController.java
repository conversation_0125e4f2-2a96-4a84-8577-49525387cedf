package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.CaseInfo;
import com.cf.financing.service.ICaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 案件信息控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Api(tags = "案件信息管理")
@RestController
@RequestMapping("/api/case-info")
@RequiredArgsConstructor
public class CaseInfoController {

    private final ICaseInfoService caseInfoService;

    /**
     * 分页查询案件列表
     */
    @ApiOperation("分页查询案件列表")
    @GetMapping("/page")
    public Map<String, Object> getCasePage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("案件编号") @RequestParam(required = false) String caseNo,
            @ApiParam("客户姓名") @RequestParam(required = false) String customerName,
            @ApiParam("客户手机号") @RequestParam(required = false) String customerPhone,
            @ApiParam("案件状态") @RequestParam(required = false) String caseStatus,
            @ApiParam("案件等级") @RequestParam(required = false) String caseLevel,
            @ApiParam("分派人ID") @RequestParam(required = false) Long assigneeId,
            @ApiParam("逾期开始天数") @RequestParam(required = false) Integer overdueDaysStart,
            @ApiParam("逾期结束天数") @RequestParam(required = false) Integer overdueDaysEnd) {
        
        Page<CaseInfo> page = new Page<>(current, size);
        IPage<CaseInfo> result = caseInfoService.getCaseInfoPage(
                page, caseNo, customerName, customerPhone, caseStatus, 
                caseLevel, assigneeId, null, null, null, null
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 根据ID查询案件详情
     */
    @ApiOperation("根据ID查询案件详情")
    @GetMapping("/{caseId}")
    public Map<String, Object> getCaseById(
            @ApiParam("案件ID") @PathVariable Long caseId) {
        
        CaseInfo caseInfo = caseInfoService.getCaseInfoById(caseId);
        
        Map<String, Object> response = new HashMap<>();
        if (caseInfo != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", caseInfo);
        } else {
            response.put("code", 404);
            response.put("message", "案件不存在");
        }
        return response;
    }

    /**
     * 查询分派给指定用户的案件
     */
    @ApiOperation("查询分派给指定用户的案件")
    @GetMapping("/assigned/{assigneeId}")
    public Map<String, Object> getAssignedCases(
            @ApiParam("分派人ID") @PathVariable Long assigneeId,
            @ApiParam("案件状态") @RequestParam(required = false) String caseStatus) {
        
        // 创建分页参数，获取所有记录
        Page<CaseInfo> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<CaseInfo> pageResult = caseInfoService.getMyCaseList(page, assigneeId, caseStatus, null);
        List<CaseInfo> cases = pageResult.getRecords();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", cases);
        return response;
    }

    /**
     * 创建案件
     */
    @ApiOperation("创建案件")
    @PostMapping
    public Map<String, Object> createCase(@RequestBody CaseInfo caseInfo) {
        boolean success = caseInfoService.createCase(caseInfo);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "创建成功");
            response.put("data", caseInfo);
        } else {
            response.put("code", 400);
            response.put("message", "创建失败");
        }
        return response;
    }

    /**
     * 更新案件信息
     */
    @ApiOperation("更新案件信息")
    @PutMapping("/{caseId}")
    public Map<String, Object> updateCase(
            @ApiParam("案件ID") @PathVariable Long caseId,
            @RequestBody CaseInfo caseInfo) {
        
        caseInfo.setId(caseId);
        boolean success = caseInfoService.updateCase(caseInfo);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "更新失败");
        }
        return response;
    }

    /**
     * 分派案件
     */
    @ApiOperation("分派案件")
    @PutMapping("/{caseId}/assign")
    public Map<String, Object> assignCase(
            @ApiParam("案件ID") @PathVariable Long caseId,
            @RequestBody Map<String, Object> assignRequest) {
        
        Long assigneeId = Long.valueOf(assignRequest.get("assigneeId").toString());
        Long operatorId = Long.valueOf(assignRequest.get("operatorId").toString());
        String remark = (String) assignRequest.get("remark");
        
        boolean success = caseInfoService.assignCase(caseId, assigneeId, operatorId, remark);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "分派成功");
        } else {
            response.put("code", 400);
            response.put("message", "分派失败");
        }
        return response;
    }

    /**
     * 批量分派案件
     */
    @ApiOperation("批量分派案件")
    @PutMapping("/batch/assign")
    public Map<String, Object> batchAssignCases(
            @RequestBody Map<String, Object> assignRequest) {
        
        @SuppressWarnings("unchecked")
        List<Long> caseIds = (List<Long>) assignRequest.get("caseIds");
        Long assigneeId = Long.valueOf(assignRequest.get("assigneeId").toString());
        Long operatorId = Long.valueOf(assignRequest.get("operatorId").toString());
        String remark = (String) assignRequest.get("remark");
        
        boolean success = caseInfoService.batchAssignCases(caseIds, assigneeId, operatorId, remark);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量分派成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量分派失败");
        }
        return response;
    }

    /**
     * 更新案件状态
     */
    @ApiOperation("更新案件状态")
    @PutMapping("/{caseId}/status")
    public Map<String, Object> updateCaseStatus(
            @ApiParam("案件ID") @PathVariable Long caseId,
            @RequestBody Map<String, String> statusRequest) {
        
        String caseStatus = statusRequest.get("caseStatus");
        String remark = statusRequest.get("remark");
        
        boolean success = caseInfoService.updateCaseStatus(caseId, caseStatus, remark);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "状态更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "状态更新失败");
        }
        return response;
    }

    /**
     * 批量更新案件状态
     */
    @ApiOperation("批量更新案件状态")
    @PutMapping("/batch/status")
    public Map<String, Object> batchUpdateCaseStatus(
            @RequestBody Map<String, Object> statusRequest) {
        
        @SuppressWarnings("unchecked")
        List<Long> caseIds = (List<Long>) statusRequest.get("caseIds");
        String caseStatus = (String) statusRequest.get("caseStatus");
        String remark = (String) statusRequest.get("remark");
        
        boolean success = caseInfoService.batchUpdateCaseStatus(caseIds, caseStatus, remark);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量状态更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量状态更新失败");
        }
        return response;
    }

    /**
     * 更新联系信息
     */
    @ApiOperation("更新联系信息")
    @PutMapping("/{caseId}/contact")
    public Map<String, Object> updateContactInfo(
            @ApiParam("案件ID") @PathVariable Long caseId,
            @RequestBody Map<String, Object> contactRequest) {
        
        Integer contactTimes = (Integer) contactRequest.get("contactTimes");
        String contactResult = (String) contactRequest.get("contactResult");
        
        boolean success = caseInfoService.updateContactInfo(caseId, contactTimes, contactResult);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "联系信息更新成功");
        } else {
            response.put("code", 400);
            response.put("message", "联系信息更新失败");
        }
        return response;
    }

    /**
     * 删除案件
     */
    @ApiOperation("删除案件")
    @DeleteMapping("/{caseId}")
    public Map<String, Object> deleteCase(
            @ApiParam("案件ID") @PathVariable Long caseId) {
        
        boolean success = caseInfoService.deleteCase(caseId);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "删除失败");
        }
        return response;
    }

    /**
     * 批量删除案件
     */
    @ApiOperation("批量删除案件")
    @DeleteMapping("/batch")
    public Map<String, Object> batchDeleteCases(@RequestBody List<Long> caseIds) {
        boolean success = caseInfoService.batchDeleteCases(caseIds);
        
        Map<String, Object> response = new HashMap<>();
        if (success) {
            response.put("code", 200);
            response.put("message", "批量删除成功");
        } else {
            response.put("code", 400);
            response.put("message", "批量删除失败");
        }
        return response;
    }

    /**
     * 获取案件统计信息
     */
    @ApiOperation("获取案件统计信息")
    @GetMapping("/statistics")
    public Map<String, Object> getCaseStatistics(
            @ApiParam("分派人ID") @RequestParam(required = false) Long assigneeId) {
        
        Map<String, Object> statistics = caseInfoService.getCaseStatistics(assigneeId, null, null);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取逾期案件统计（按等级）
     */
    @ApiOperation("获取逾期案件统计（按等级）")
    @GetMapping("/overdue-statistics")
    public Map<String, Object> getOverdueCaseStatistics(
            @ApiParam("分派人ID") @RequestParam(required = false) Long assigneeId) {
        
        List<Map<String, Object>> statistics = caseInfoService.getOverdueCaseStatistics(assigneeId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", statistics);
        return response;
    }

    /**
     * 获取案件趋势数据
     */
    @ApiOperation("获取案件趋势数据")
    @GetMapping("/trend")
    public Map<String, Object> getCaseTrend(
            @ApiParam("天数") @RequestParam(defaultValue = "30") Integer days) {
        
        List<Map<String, Object>> trend = caseInfoService.getCaseTrend(days);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", trend);
        return response;
    }

    /**
     * 获取待处理案件数量
     */
    @ApiOperation("获取待处理案件数量")
    @GetMapping("/pending-count")
    public Map<String, Object> getPendingCaseCount(
            @ApiParam("分派人ID") @RequestParam(required = false) Long assigneeId) {
        
        Integer count = caseInfoService.getPendingCaseCount(assigneeId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", count);
        return response;
    }

    /**
     * 获取今日到期案件
     */
    @ApiOperation("获取今日到期案件")
    @GetMapping("/today-due")
    public Map<String, Object> getTodayDueCases(
            @ApiParam("分派人ID") @RequestParam(required = false) Long assigneeId) {
        
        List<CaseInfo> cases = caseInfoService.getTodayDueCases(assigneeId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", cases);
        return response;
    }

    /**
     * 生成案件编号
     */
    @ApiOperation("生成案件编号")
    @GetMapping("/generate-case-no")
    public Map<String, Object> generateCaseNo() {
        String caseNo = caseInfoService.generateCaseNo();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "生成成功");
        response.put("data", caseNo);
        return response;
    }
}